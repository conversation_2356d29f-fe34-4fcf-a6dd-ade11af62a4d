-- ah.lua
local mapping = {
  from_proto_name = "ah",
  to_proto_name = "ah",
  rule_proto_name = "ah",
  common_flag = true,
  field = {
    {from_name = "AH_ICV"        ,to_name = "ICV"           },
    {from_name = "AH_length"     ,to_name = "payLen"        },
    {from_name = "AH_Sequence"   ,to_name = "seqNum"        },
    {from_name = "AH_SPI"        ,to_name = "SPI"           },

  }
}
yalua_register_proto(mapping)