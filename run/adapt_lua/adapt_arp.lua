-- arp.lua
local mapping = {
  from_proto_name = "arp",
  to_proto_name = "arp",
  rule_proto_name = "arp",
  common_flag = true,
  field = {
    {from_name = "ProtocolType"  ,to_name = "proto"         ,rule_name = "proto"   },
    {from_name = "Opcode"        ,to_name = "opc"           ,rule_name = "operation_code"  },
    {from_name = "TargetIP"      ,to_name = "dstIPAddr"     ,rule_name = "ip_destination"  },
    {from_name = "TargetMAC"     ,to_name = "dstMACAddr"    ,rule_name = "mac_destination" },
    {from_name = "SenderIP"      ,to_name = "senderIPAddr"  ,rule_name = "ip_source"  },
    {from_name = "SenderMAC"     ,to_name = "senderMACAddr" ,rule_name = "mac_source" },
    {from_name = "HardwareType"  ,to_name = "hardw"         ,rule_name = "hardware_type"},

  }
}
yalua_register_proto(mapping)