local M = {}
-- this is common and link alias
M.mapping_common = {
  from_proto_name = "common",
  to_proto_name = "common",
  rule_proto_name = "common",
  field = {
    {from_name = "tags_site"                ,rule_name = "tags_site"               },
    {from_name = "tags_unit"                ,rule_name = "tags_unit"               },
    {from_name = "tags_task"                ,rule_name = "tags_task"               },
    {from_name = "tags_rule"                ,rule_name = "tags_rule"               },
    {from_name = "tags_anomaly"             ,rule_name = "tags_anomaly"            },
    {from_name = "tags_threat"              ,rule_name = "tags_threat"             },
    {from_name = "tags_user"                ,rule_name = "tags_user"               },
    {from_name = "tags_attack"              ,rule_name = "tags_attack"             },
    {from_name = "time_capture"             ,rule_name = "time_capture"            },
    {from_name = "tags_isp"                 ,rule_name = "tags_isp"                },
    {from_name = "mobile_imsi"              ,rule_name = "mobile_imsi"             },
    {from_name = "mobile_imei"              ,rule_name = "mobile_imei"             },
    {from_name = "mobile_msisdn"            ,rule_name = "mobile_msisdn"           },
    {from_name = "mobile_interface"         ,rule_name = "mobile_interface"        },
    {from_name = "mobile_srcip"             ,rule_name = "mobile_srcip"            },
    {from_name = "mobile_srcport"           ,rule_name = "mobile_srcport"          },
    {from_name = "mobile_destip"            ,rule_name = "mobile_destip"           },
    {from_name = "mobile_destport"          ,rule_name = "mobile_destport"         },
    {from_name = "mobile_apn"               ,rule_name = "mobile_apn"              },
    {from_name = "mobile_ecgi"              ,rule_name = "mobile_ecgi"             },
    {from_name = "mobile_tai"               ,rule_name = "mobile_tai"              },
    {from_name = "mobile_teid"              ,rule_name = "mobile_teid"             },
    {from_name = "mobile_srcisp"            ,rule_name = "mobile_srcisp"           },
    {from_name = "mobile_destisp"           ,rule_name = "mobile_destisp"          },
    {from_name = "mobile_local_province"    ,rule_name = "mobile_local_province"   },
    {from_name = "mobile_local_city"        ,rule_name = "mobile_local_city"       },
    {from_name = "mobile_owner_province"    ,rule_name = "mobile_owner_province"   },
    {from_name = "mobile_owner_city"        ,rule_name = "mobile_owner_city"       },
    {from_name = "mobile_roaming_type"      ,rule_name = "mobile_roaming_type"     },
    {from_name = "mobile_rat"               ,rule_name = "mobile_rat"              }
  }
}

M.mapping_link = {
  from_proto_name = "link",
  to_proto_name = "link",
  rule_proto_name = "link",
  field = {
    { from_name = "sesBytes",            rule_name = "ip_bytes",              },
    { from_name = "downSesBytes",        rule_name = "ip_bytes_dst",          },
    { from_name = "upSesBytes",          rule_name = "ip_bytes_src",          },
    { from_name = "downLinkTcpOpts",     rule_name = "tcp_options_dst",       },
    { from_name = "upLinkTcpOpts",       rule_name = "tcp_options_src",       },
    { from_name = "downLinkSynTcpWins",  rule_name = "tcp_winsize_dst",       },
    { from_name = "upLinkSynTcpWins",    rule_name = "tcp_winsize_src",       },
    { from_name = "downLinkPayLenSet",   rule_name = "trans_paylen_set_dst",  },
    { from_name = "upLinkPayLenSet",     rule_name = "trans_paylen_set_src",  },
    { from_name = "downLinkStream",      rule_name = "ip_stream_dst",         },
    { from_name = "upLinkStream",        rule_name = "ip_stream_src",         },
    { from_name = "downLinkDesBytes",    rule_name = "ip_desiredbytes_dst",   },
    { from_name = "upLinkDesBytes",      rule_name = "ip_desiredbytes_src",   },
    { from_name = "tcpFlagsSynAckCnt",   rule_name = "tcp_flags_syn_ack_cnt", },
    { from_name = "tcpFlagsUrgCnt",      rule_name = "tcp_flags_urg_cnt",     },
    { from_name = "tcpFlagsAckCnt",      rule_name = "tcp_flags_ack_cnt",     },
    { from_name = "tcpFlagsPshCnt",      rule_name = "tcp_flags_psh_cnt",     },
    { from_name = "tcpFlagsRstCnt",      rule_name = "tcp_flags_rst_cnt",     },
    { from_name = "tcpFlagsSynCnt",      rule_name = "tcp_flags_syn_cnt",     },
    { from_name = "tcpFlagsFinCnt",      rule_name = "tcp_flags_fin_cnt",     },
    { from_name = "appDirec",            rule_name = "ip_direction",          },
    { from_name = "firTtlBySrv",         rule_name = "ip_ttl_dst",            },
    { from_name = "firTtlByCli",         rule_name = "ip_ttl_src",            },
    { from_name = "downLinkPktNum",      rule_name = "ip_packets_dst",        },
    { from_name = "upLinkPktNum",        rule_name = "ip_packets_src",        },
    { from_name = "downPayLen",          rule_name = "ip_databytes_dst",      },
    { from_name = "upPayLen",            rule_name = "ip_databytes_src",      }
  }

}

function M.print_sdt()
  print("------------ hello ----------------------- custom")
end

return M
