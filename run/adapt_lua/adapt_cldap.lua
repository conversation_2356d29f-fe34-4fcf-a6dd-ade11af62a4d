-- cldap.lua
local mapping = {
  from_proto_name = "cldap",
  to_proto_name = "cldap",
  rule_proto_name = "cldap",
  common_flag = true,
  field = {
    {from_name = "flagKDC"               ,to_name = "flagKDC"       },
    {from_name = "flagLDAP"              ,to_name = "flagLDAP"      },
    {from_name = "flagGC"                ,to_name = "flagGC"        },
    {from_name = "flagPDC"               ,to_name = "flagPDC"       },
    {from_name = "netlogon_ClientSite"   ,to_name = "cliSite"       },
    {from_name = "netlogon_ServerSite"   ,to_name = "srvSite"       },
    {from_name = "netlogon_NetBIOSDomain",to_name = "netbiosDom"    },
    {from_name = "netlogon_Forest"       ,to_name = "forest"        },
    {from_name = "netlogon_DomainSID"    ,to_name = "domSID"        },
    {from_name = "ntverVPGC"             ,to_name = "ntverVGC"      },
    {from_name = "ntverVPDC"             ,to_name = "ntverVPDC"     },
    {from_name = "ntverVNT4"             ,to_name = "ntverVNT4"     },
    {from_name = "netlogon_User"         ,to_name = "usrName"       },
    {from_name = "netlogon_Hostname"     ,to_name = "hostName"      },
    {from_name = "netlogon_DomainGUID"   ,to_name = "domGUID"       },
    {from_name = "netlogon_Domain"       ,to_name = "dnsDom"        },
    {from_name = "AttributesValue"       ,to_name = "attVal"        },
    {from_name = "AttributesDesc"        ,to_name = "attType"       },
    {from_name = "ProtocolOp"            ,to_name = "msgType"       },
    {from_name = "MessageID"             ,to_name = "msgID"         },

    
  }
}
yalua_register_proto(mapping)