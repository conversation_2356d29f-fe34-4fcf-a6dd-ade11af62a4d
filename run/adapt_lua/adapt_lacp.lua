-- lacp.lua
local mapping = {
  from_proto_name = "lacp",
  to_proto_name = "lacp",
  rule_proto_name = "lacp",
  common_flag = true,
  field = {
    {from_name = "PartnerStateFlagExpired"    ,to_name = "parisexp"   },
    {from_name = "PartnerStateFlagDefaulted"  ,to_name = "parisdef"   },
    {from_name = "PartnerStateFlagDistrib"    ,to_name = "parisdis"   },
    {from_name = "PartnerStateFlagCollecting" ,to_name = "pariscol"   },
    {from_name = "PartnerStateFlagSync"       ,to_name = "patissyn"   },
    {from_name = "PartnerStateFlagAggregation",to_name = "parisagg"   },
    {from_name = "PartnerStateFlagTimeout"    ,to_name = "paristimout"},
    {from_name = "PartnerStateFlagActivity"   ,to_name = "parisact"   },
    {from_name = "PartnerPort"                ,to_name = "parpor"     },
    {from_name = "PartnerPortPriority"        ,to_name = "parporpri"  },
    {from_name = "PartnerKey"                 ,to_name = "parkey"     },
    {from_name = "PartnerSystemID"            ,to_name = "parsysid"   },
    {from_name = "PartnerSystemPriority"      ,to_name = "parsyspri"  },
    {from_name = "ActorStateFlagExpired"      ,to_name = "actisexp"   },
    {from_name = "ActorStateFlagDefaulted"    ,to_name = "actisdef"   },
    {from_name = "ActorStateFlagDistrib"      ,to_name = "actisdis"   },
    {from_name = "ActorStateFlagCollecting"   ,to_name = "actiscol"   },
    {from_name = "ActorStateFlagSync"         ,to_name = "actissyn"   },
    {from_name = "ActorStateFlagAggregation"  ,to_name = "actisagg"   },
    {from_name = "ActorStateFlagTimeout"      ,to_name = "actistimout"},
    {from_name = "ActorStateFlagActivity"     ,to_name = "actisact"   },
    {from_name = "ActorPort"                  ,to_name = "actpor"     },
    {from_name = "ActorPortPriority"          ,to_name = "actporpri"  },
    {from_name = "ActorKey"                   ,to_name = "actkey"     },
    {from_name = "ActorSystemID"              ,to_name = "actsysid"   },
    {from_name = "ActorSystemPriority"        ,to_name = "actsyspri"  },
    {from_name = "SrcMAC"                     ,to_name = "srcMacAddr" },
  }
}
yalua_register_proto(mapping)