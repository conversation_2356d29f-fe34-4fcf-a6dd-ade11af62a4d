-- ospf.lua
local mapping = {
  from_proto_name = "ospf",
  to_proto_name = "ospf",
  rule_proto_name = "ospf",
  common_flag = true,
  field = {
    {from_name = "LS_opt_N"                      ,to_name = "rouLSNT"        },
    {from_name = ""                              ,to_name = "tosExtRouTag"   },
    {from_name = ""                              ,to_name = "tosFwdIPAddr"   },
    {from_name = ""                              ,to_name = "fwdAddrV6"      },
    {from_name = "Reference_id"                  ,to_name = "refLsID"        },
    {from_name = "Reference_type"                ,to_name = "refLsType"      },
    {from_name = ""                              ,to_name = "asnssaT"        },
    {from_name = "Reference_ADV_router"          ,to_name = "refAdvRou"      },
    {from_name = "Prefix_addr"                   ,to_name = "addrPre"        },
    {from_name = "Prefix_P_flag"                 ,to_name = "preP"           },
    {from_name = "Prefix_LA_flag"                ,to_name = "preLa"          },
    {from_name = "Prefix_NU_flag"                ,to_name = "preNu"          },
    {from_name = "Prefix_len"                    ,to_name = "preLen"         },
    {from_name = ""                              ,to_name = "dstRou"         },
    {from_name = "ROUTER_link_num_of_metric_0"   ,to_name = "tos"            },
    {from_name = "ROUTER_link_type_0"            ,to_name = "rouItemType"    },
    {from_name = "ROUTER_nr_links"               ,to_name = "linkNum"        },
    {from_name = ""                              ,to_name = "RouIPv6Addr"    },
    {from_name = "Traffic_engineering_type"      ,to_name = "teTlvType"      },
    {from_name = ""                              ,to_name = "remIntIPV6"     },
    {from_name = ""                              ,to_name = "teInstID"       },
    {from_name = "Traffic_engineering_metric"    ,to_name = "teMetric"       },
    {from_name = "Prefix_count"                  ,to_name = "preNum"         },
    {from_name = "Local_interface_id"            ,to_name = "linkLocIPv6Addr"},
    {from_name = "LS_Len"                        ,to_name = "lsaLen"         },
    {from_name = "LS_Checksum"                   ,to_name = "lsChe"          },
    {from_name = "LS_Numbre"                     ,to_name = "lsaNum"         },
    {from_name = "master_flag"                   ,to_name = "msTag"          },
    {from_name = "more_flag"                     ,to_name = "moreTag"        },
    {from_name = "init_flag"                     ,to_name = "initTag"        },
    {from_name = ""                              ,to_name = "synTag"         },
    {from_name = "interface_mtu"                 ,to_name = "mtu"            },
    {from_name = "interface_id"                  ,to_name = "intID"          },
    {from_name = "opt_V6"                        ,to_name = "optV6"          },
    {from_name = "opt_MT"                        ,to_name = "optMt"          },
    {from_name = "opt_E"                         ,to_name = "optE"           },
    {from_name = "opt_MC"                        ,to_name = "optMc"          },
    {from_name = "opt_N"                         ,to_name = "optN"           },
    {from_name = "opt_DC"                        ,to_name = "optDc"          },
    {from_name = "opt_L"                         ,to_name = "optL"           },
    {from_name = "instance_id"                   ,to_name = "instID"         },
    {from_name = "head_checksum"                 ,to_name = "chckSum"        },
    {from_name = ""                              ,to_name = "pktLen"         },
    {from_name = "LSA_link_id"                   ,to_name = "LSALinkID"      },
    {from_name = "LSA_link_type"                 ,to_name = "LSALinkType"    },
    {from_name = "Admin_group"                   ,to_name = "adminGrp"       },
    {from_name = "Unreservable_bandwith"         ,to_name = "unresBW"        },
    {from_name = "Maximum_reservable_bandwith"   ,to_name = "maxResBW"       },
    {from_name = "Maximum_bandwidth"             ,to_name = "maxBW"          },
    {from_name = "Traffic_engineering_metric"    ,to_name = "traEngMet"      },
    {from_name = "Remote_interface_id"           ,to_name = "remIntIP"       },
    {from_name = "Local_interface_id"            ,to_name = "locIntIPAddr"   },
    {from_name = "Local_interface_id"            ,to_name = "rouIntAddr"     },
    {from_name = "neighbor_routerid_v3"          ,to_name = "neigRouID"      },
    {from_name = "Neighbor_interfaceid_v3"       ,to_name = "neigIntID"      },
    {from_name = "As_ext_router_tag"             ,to_name = "extRouTag"      },
    {from_name = "As_ext_type"                   ,to_name = "extType"        },
    {from_name = "As_ext_forward_addr"           ,to_name = "fwdAddr"        },
    {from_name = "NETWORK_attached_router_0"     ,to_name = "attRou"         },
    {from_name = "ROUTER_link_0_metric_0"        ,to_name = "metric"         },
    {from_name = "ROUTER_link_type_0"            ,to_name = "linkType"       },
    {from_name = "ROUTER_link_data_0"            ,to_name = "linkData"       },
    {from_name = "ROUTER_link_id_0"              ,to_name = "linkID"         },
    {from_name = "Lsa_netmask"                   ,to_name = "LSANetMask"     },
    {from_name = "Abr_flag"                      ,to_name = "borBit"         },
    {from_name = "Asbr_flag"                     ,to_name = "extBit"         },
    {from_name = "Virtual_flag"                  ,to_name = "VLFlag"         },
    {from_name = "LS_Age"                        ,to_name = "lsaAge"         },
    {from_name = "Sequence_number"               ,to_name = "LSASeqNum"      },
    {from_name = "Advertising_router"            ,to_name = "advRou"         },
    {from_name = "Link_state_id"                 ,to_name = "linkStaID"      },
    {from_name = "LS_type"                       ,to_name = "linkStaType"    },
    {from_name = "Db_sequence"                   ,to_name = "DBSeqNum"       },
    {from_name = "Hello_active_neighbor"         ,to_name = "neighbor"       },
    {from_name = "Hello_backup_designated_router",to_name = "BDR"            },
    {from_name = "Hello_designated_router"       ,to_name = "DR"             },
    {from_name = "Hello_router_dead_interval"    ,to_name = "deadInt"        },
    {from_name = "Hello_router_priority"         ,to_name = "rouPri"         },
    {from_name = "NETWORK_netmask"               ,to_name = "netMask"        },
    {from_name = "Hello_interval"                ,to_name = "helInt"         },
    {from_name = "Cryptographic_data"            ,to_name = "authData"       },
    {from_name = "Cryptographic_seqnum"          ,to_name = "cryptSeqNum"    },
    {from_name = "Cryptographic_keyid"           ,to_name = "keyID"          },
    {from_name = "Auth_password"                 ,to_name = "authPwd"        },
    {from_name = "Auth_type"                     ,to_name = "authType"       },
    {from_name = "Area_id"                       ,to_name = "areaID"         },
    {from_name = "Src_router"                    ,to_name = "rouID"          },
    {from_name = "Message_type"                  ,to_name = "msgType"        },
    {from_name = "Version"                       ,to_name = "ver"            },

    
  }
}
yalua_register_proto(mapping)