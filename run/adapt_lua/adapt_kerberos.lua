-- kerberos.lua
local mapping = {
  from_proto_name = "kerberos",
  to_proto_name = "kerberos",
  rule_proto_name = "kerberos",
  common_flag = true,
  field = {
    {from_name = "ticket_sname"     ,to_name = "ticSname"       },
    {from_name = "ticket_sname_type",to_name = "ticType"        },
    {from_name = "PadataTypeStr"    ,to_name = "patgsreqEncType"},
    {from_name = "pdataData"        ,to_name = "patgsreqEncData"},
    {from_name = "ticket_realm"     ,to_name = "ticRea"         },
    {from_name = ""                 ,to_name = "opNum"          },
    {from_name = ""                 ,to_name = "proRealm"       },
    {from_name = ""                 ,to_name = "proName"        },
    {from_name = "errText"          ,to_name = "errText"        },
    {from_name = "stime"            ,to_name = "srvTime"        },
    {from_name = "ctime"            ,to_name = "cliTime"        },
    {from_name = ""                 ,to_name = "senderTime"     },
    {from_name = "userData"         ,to_name = "usrData"        },
    {from_name = "nonce"            ,to_name = "nonce"          },
    {from_name = "from"             ,to_name = "from"           },
    {from_name = "crealm"           ,to_name = "cliRealm"       },
    {from_name = "version"          ,to_name = "protVer"        },
    {from_name = "ErrorCode"        ,to_name = "ErrorCode"      },
    {from_name = "susec"            ,to_name = "srvTimeUsec"    },
    {from_name = "cusec"            ,to_name = "cliTimeUsec"    },
    {from_name = "ticket_version"   ,to_name = "ticVer"         },
    {from_name = "add_ticket"       ,to_name = "addTic"         },
    {from_name = "hostname"         ,to_name = "hostName"       },
    {from_name = "AddrType"         ,to_name = "hostAddrType"   },
    {from_name = "req_etype"        ,to_name = "encryType"      },
    {from_name = "rtime"            ,to_name = "rTime"          },
    {from_name = "till"             ,to_name = "TILL"           },
    {from_name = "server_name"      ,to_name = "srvName"        },
    {from_name = "realm"            ,to_name = "realm"          },
    {from_name = "client_name"      ,to_name = "cliName"        },
    {from_name = "kdc_option"       ,to_name = "KDCOpt"         },
    {from_name = "enc_data"         ,to_name = "encData"        },
    {from_name = "enc_version"      ,to_name = "encVer"         },
    {from_name = "enc_alg_id"       ,to_name = "encDataType"    },
    {from_name = "MessageType"      ,to_name = "msgType"        },

    
  }
}
yalua_register_proto(mapping)