-- dbbasic.lua
local mapping = {
  from_proto_name = "dbbasic",
  to_proto_name = "dbbasic",
  rule_proto_name = "db",
  common_flag = true,
  field = {
    {from_name = ""              ,to_name = "pgApp"         },
    {from_name = "OracleHost"    ,to_name = "orcHost"       ,rule_name = "oracle_host",tll = 1},
    {from_name = "DbSql"         ,to_name = "DBSQL"         ,rule_name = "sql"        ,tll = 1},
    {from_name = "DbName"        ,to_name = "DBName"        ,rule_name = "name"       ,tll = 1},
    {from_name = "DbPort"        ,to_name = "DBPort"        ,rule_name = "port"       ,tll = 1},
    {from_name = "DbIP"          ,to_name = "DBIP"          ,rule_name = "ip"         ,tll = 1},
    {from_name = "Password"      ,to_name = "pas"           ,rule_name = "pwd"        ,tll = 1},
    {from_name = "UserName"      ,to_name = "log"           ,rule_name = "usr"       ,tll = 1},
    {from_name = "DbType"        ,to_name = "DBType"        ,rule_name = "type"       ,tll = 1},

  }
}
yalua_register_proto(mapping)