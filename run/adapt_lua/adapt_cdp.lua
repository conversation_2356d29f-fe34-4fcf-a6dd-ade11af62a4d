-- cdp.lua
local mapping = {
  from_proto_name = "cdp",
  to_proto_name = "cdp",
  rule_proto_name = "cdp",
  common_flag = true,
  field = {
    {from_name = "system_object_identifier",to_name = "sysOID"        },
    {from_name = "location"                ,to_name = "loc"           },
    {from_name = "capabilities"            ,to_name = "cap"           },
    {from_name = "vtp_management_domain"   ,to_name = "VTPManDom"     },
    {from_name = "odr_default_gateway"     ,to_name = "pre"           },
    {from_name = "platform"                ,to_name = "plaInfo"       },
    {from_name = "software_version"        ,to_name = "softwVer"      },
    {from_name = "portid"                  ,to_name = "porID"         },
    {from_name = "ip_address"              ,to_name = "IPaddr"        },
    {from_name = "deviceid"                ,to_name = "devID"         },
    {from_name = "cdp_ttl"                     ,to_name = "TTL"           },

  }
}
yalua_register_proto(mapping)