-- lldp.lua
local mapping = {
  from_proto_name = "lldp",
  to_proto_name = "lldp",
  rule_proto_name = "lldp",
  common_flag = true,
  field = {
    {from_name = "OID"                  ,to_name = "snmpOID"      },
    {from_name = "InterfaceNumber"      ,to_name = "snmpIntNum"   },
    {from_name = "InterfaceType"        ,to_name = "snmpIntType"  },
    {from_name = "ManagementAddress"    ,to_name = "snmpManAddr"  },
    {from_name = "ManagementAddressType",to_name = "snmpAddrType" },
    {from_name = "Capabilities"         ,to_name = "sysCap"       },
    {from_name = "SystemDescription"    ,to_name = "sysDes"       },
    {from_name = "SystemName"           ,to_name = "sysName"      },
    {from_name = "PortId"               ,to_name = "portID"       },
    {from_name = "PortIdType"           ,to_name = "portIDSubType"},
    {from_name = "ChassisId"            ,to_name = "chaID"        },
  }
}
yalua_register_proto(mapping)