-- pptp.lua
local mapping = {
  from_proto_name = "pptp",
  to_proto_name = "pptp",
  rule_proto_name = "pptp",
  common_flag = true,
  field = {
    {from_name = "pRecvACCM"         ,to_name = "RecvAccm"        },
    {from_name = "pSendACCM"         ,to_name = "SendAccm"        },
    {from_name = "pAlignErrCnt"      ,to_name = "AlignErrs"       },
    {from_name = "pTimeOutErrCnt"    ,to_name = "TimeoutErrs"     },
    {from_name = "pBFOverCnt"        ,to_name = "BufOverruns"     },
    {from_name = "pHWOverCnt"        ,to_name = "HardwareOverruns"},
    {from_name = "pFramErrCnt"       ,to_name = "FramingErrs"     },
    {from_name = "pCrcErrCnt"        ,to_name = "CrcErrs"         },
    {from_name = "pCallStat"         ,to_name = "CallStat"        },
    {from_name = "pktTransDelay"     ,to_name = "pktTransDelay"   },
    {from_name = "inCallResult"      ,to_name = "IncomRes"        },
    {from_name = "phyChannelID"      ,to_name = "phyChnId"        },
    {from_name = "pConnSpeed"        ,to_name = "connSpeed"       },
    {from_name = "pCauseCode"        ,to_name = "causeCode"       },
    {from_name = "outCallResult"     ,to_name = "OutGoingRes"     },
    {from_name = "peerCallID"        ,to_name = "peerCallId"      },
    {from_name = "subAddress"        ,to_name = "subAddr"         },
    {from_name = "phoneNumber"       ,to_name = "phoneNum"        },
    {from_name = "pktProcDelay"      ,to_name = "pktProcDelay"    },
    {from_name = "pktRecvWinSize"    ,to_name = "pktRecvWinSize"  },
    {from_name = "framingType"       ,to_name = "framingType"     },
    {from_name = "bearerType"        ,to_name = "bearerType"      },
    {from_name = "ocrqMaxBps"        ,to_name = "maxBps"          },
    {from_name = "ocrqMinBps"        ,to_name = "minBps"          },
    {from_name = "callSerialNum"     ,to_name = "callSerialNum"   },
    {from_name = "echoResult"        ,to_name = "KeepAliveRes"    },
    {from_name = "echoID"            ,to_name = "id"              },
    {from_name = "stopResult"        ,to_name = "StopConnRes"     },
    {from_name = "stopReason"        ,to_name = "reason"          },
    {from_name = "errorCode"         ,to_name = "errCod"          },
    {from_name = "startConnResult"   ,to_name = "startConnRes"    },
    {from_name = "firmwareRev"       ,to_name = "firmwareRev"     },
    {from_name = "maxChannels"       ,to_name = "maxChannels"     },
    {from_name = "bearerCap"         ,to_name = "bearerCap"       },
    {from_name = "framingCap"        ,to_name = "famingCap"       },
    {from_name = "pptpVersion"       ,to_name = "protoVer"        },
    {from_name = "callID"            ,to_name = "callID"          },
    {from_name = "dialedNumber"      ,to_name = "calleNum"        },
    {from_name = "dialingNumber"     ,to_name = "calliNum"        },
    {from_name = "hostName"          ,to_name = "hostName"        },
    {from_name = "vendorName"        ,to_name = "venName"         },
    {from_name = "controlMessageType",to_name = "conType"         },

    
  }
}
yalua_register_proto(mapping)