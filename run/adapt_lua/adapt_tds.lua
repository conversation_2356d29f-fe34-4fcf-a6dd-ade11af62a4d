-- tds.lua
local mapping = {
  from_proto_name = "tds",
  to_proto_name = "tds",
  rule_proto_name = "tds",
  common_flag = true,
  field = {
    {from_name = "Procedure_00_Parameters",to_name = "procPar"       },
    {from_name = "StoredProcedureID_00"   ,to_name = "stoProID"      },
    {from_name = "Procedure_00_Name"      ,to_name = "procName"      },
    {from_name = ""                       ,to_name = "sqlTypeExp"    },
    {from_name = "SQL_Type_Flags"         ,to_name = "sqlTypeOptFlag"},
    {from_name = "Option_Flags2"          ,to_name = "optFlag2"      },
    {from_name = "Option_Flags1"          ,to_name = "optFlag1"      },
    {from_name = "ConnectionID"           ,to_name = "conID"         },
    {from_name = "ClientPID"              ,to_name = "cliPID"        },
    {from_name = "Clientversion"          ,to_name = "cliVer"        },
    {from_name = "TDSversion"             ,to_name = "srvVer"        },
    {from_name = "Locale"                 ,to_name = "locale"        },
    {from_name = "LibraryName"            ,to_name = "libName"       },
    {from_name = "ProgVer"                ,to_name = "srvProgVer"    },
    {from_name = "Program"                ,to_name = "srvProgName"   },
    {from_name = "ServerName"             ,to_name = "srvName"       },
    {from_name = "AppName"                ,to_name = "AppName"       },
    {from_name = "ClientName"             ,to_name = "cliName"       },
    {from_name = "PacketType"             ,to_name = "pktType"       },

    
  }
}
yalua_register_proto(mapping)