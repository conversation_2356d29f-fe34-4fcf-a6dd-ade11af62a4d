-- ntp.lua
local mapping = {
  from_proto_name = "ntp",
  to_proto_name = "ntp",
  rule_proto_name = "ntp",
  common_flag = true,
  field = {
    {from_name = "Opcode"              ,to_name = "opcode"         },
    {from_name = "Status"              ,to_name = "sta"            },
    {from_name = "response_bit"        ,to_name = "rspbit"         },
    {from_name = "ipv6_local_addr"     ,to_name = "monlislocipv6"  },
    {from_name = "ipv6_remote_addr"    ,to_name = "monlisremipv6"  },
    {from_name = "ntppriv_version"     ,to_name = "monlisntpver"   },
    {from_name = "ntppriv_mode"        ,to_name = "monlismod"      },
    {from_name = "ntppriv_port"        ,to_name = "monlispor"      },
    {from_name = "local_address"       ,to_name = "monlislocipaddr"},
    {from_name = "remote_address"      ,to_name = "monlisremipaddr"},
    {from_name = "Associd"             ,to_name = "assId"          },
    {from_name = "Transmit_Timestamp"  ,to_name = "transTimeStamp" },
    {from_name = "Receive_Timestamp"   ,to_name = "recvTimeStamp"  },
    {from_name = "Origin_Timestamp"    ,to_name = "orgTimeStamp"   },
    {from_name = "Version_number"      ,to_name = "ver"            },
    {from_name = "Reference_Timestamp" ,to_name = "refTime"        },
    {from_name = "Reference_ID"        ,to_name = "refID"          },
    {from_name = "Root_Dispersion"     ,to_name = "rooDis"         },
    {from_name = "Root_Delay"          ,to_name = "rooDelay"       },
    {from_name = "Peer_Clock_Precision",to_name = "precision"      },
    {from_name = "Peer_Clock_Stratum"  ,to_name = "stratum"        },
    {from_name = "Mode"                ,to_name = "NTPMode"        },

    
  }
}
yalua_register_proto(mapping)