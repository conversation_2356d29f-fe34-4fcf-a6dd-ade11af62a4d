-- smb_netlogon.lua
local mapping = {
  from_proto_name = "smb_logon",
  to_proto_name = "smb_netlogon",
  rule_proto_name = "smb_logon",
  common_flag = true,
  field = {
    {from_name = "uppPro"        ,to_name = "uppPro"        },
    {from_name = "cmd"           ,to_name = "cmd"           },
    {from_name = "dbCnt"         ,to_name = "dbCnt"         },
    {from_name = "lowSeq"        ,to_name = "lowSeq"        },
    {from_name = "sig"           ,to_name = "sig"           },
    {from_name = "domSid"        ,to_name = "domSid"        },
    {from_name = "updType"       ,to_name = "updType"       },
    {from_name = "osVer"         ,to_name = "osVer"         },
    {from_name = "minVer"        ,to_name = "minVer"        },
    {from_name = "uniDomName"    ,to_name = "uniDomName"    },
    {from_name = "scrName"       ,to_name = "scrName"       },
    {from_name = "srvIp"         ,to_name = "srvIp"         },
    {from_name = "cliSitName"    ,to_name = "cliSitName"    },
    {from_name = "srvSitName"    ,to_name = "srvSitName"    },
    {from_name = "srvName"       ,to_name = "srvName"       },
    {from_name = "srvDnsName"    ,to_name = "srvDnsName"    },
    {from_name = "domDnsName"    ,to_name = "domDnsName"    },
    {from_name = "forDnsName"    ,to_name = "forDnsName"    },
    {from_name = "domGUID"       ,to_name = "domGUID"       },
    {from_name = "accCtrl"       ,to_name = "accCtrl"       },
    {from_name = "domSideSize"   ,to_name = "domSideSize"   },
    {from_name = "usrName"       ,to_name = "usrName"       },
    {from_name = "reqCnt"        ,to_name = "reqCnt"        },
    {from_name = "domName"       ,to_name = "domName"       },
    {from_name = "uniPdcName"    ,to_name = "uniPdcName"    },
    {from_name = "pdcName"       ,to_name = "pdcName"       },
    {from_name = "lm2OTok"       ,to_name = "lm2OTok"       },
    {from_name = "lmntTok"       ,to_name = "lmntTok"       },
    {from_name = "ntVer"         ,to_name = "ntVer"         },
    {from_name = "uniCompName"   ,to_name = "uniCompName"   },
    {from_name = "mslotName"     ,to_name = "mslotName"     },
    {from_name = "compName"      ,to_name = "compName"      },
    {from_name = "amRelKey"      ,to_name = "amRelKey"      },

  }
}
yalua_register_proto(mapping)