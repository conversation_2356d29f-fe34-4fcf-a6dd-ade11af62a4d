-- eigrp.lua
local mapping = {
  from_proto_name = "eigrp",
  to_proto_name = "eigrp",
  rule_proto_name = "eigrp",
  common_flag = true,
  field = {
    {from_name = ""                   ,to_name = "rouOpc"        },
    {from_name = ""                   ,to_name = "rouTid"        },
    {from_name = ""                   ,to_name = "rouAfi"        },
    {from_name = ""                   ,to_name = "comList"       },
    {from_name = "routeReplicatedFlag",to_name = "opaRepl"       },
    {from_name = "routeActiveFlag"    ,to_name = "opaAct"        },
    {from_name = "candDefaultFlag"    ,to_name = "opaCd"         },
    {from_name = "srcWithdrawFlag"    ,to_name = "opaSrc"        },
    {from_name = ""                   ,to_name = "metEne"        },
    {from_name = ""                   ,to_name = "metQen"        },
    {from_name = ""                   ,to_name = "metJit"        },
    {from_name = ""                   ,to_name = "metMtu"        },
    {from_name = ""                   ,to_name = "peeTerm"       },
    {from_name = ""                   ,to_name = "mulSeq"        },
    {from_name = ""                   ,to_name = "seqProtAddrs"  },
    {from_name = ""                   ,to_name = "authData"      },
    {from_name = ""                   ,to_name = "authType"      },
    {from_name = ""                   ,to_name = "virID"         },
    {from_name = ""                   ,to_name = "extMet"        },
    {from_name = ""                   ,to_name = "adminTag"      },
    {from_name = ""                   ,to_name = "rouTag"        },
    {from_name = ""                   ,to_name = "scaBW"         },
    {from_name = ""                   ,to_name = "extFlag"       },
    {from_name = "ExtProtoID"         ,to_name = "extProtID"     },
    {from_name = "OrigAS"             ,to_name = "oriASNum"      },
    {from_name = "OrigRoutID"         ,to_name = "originRou"     },
    {from_name = "Destination"        ,to_name = "dst"           },
    {from_name = "PrefixLength"       ,to_name = "perLen"        },
    {from_name = "Load"               ,to_name = "load"          },
    {from_name = "Reliability"        ,to_name = "reliability"   },
    {from_name = "HopCount"           ,to_name = "hopCou"        },
    {from_name = "BW"                 ,to_name = "BW"            },
    {from_name = "Delay"              ,to_name = "delay"         },
    {from_name = "NextHop"            ,to_name = "nextHop"       },
    {from_name = "Type"               ,to_name = "rouType"       },
    {from_name = "TLVVersion"         ,to_name = "TLVSofVer"     },
    {from_name = ""                   ,to_name = "tidList"       },
    {from_name = "SoftVersion"        ,to_name = "softwVer"      },
    {from_name = "HoldTime"           ,to_name = "holdTime"      },
    {from_name = "ParamK"             ,to_name = "parK"          },
    {from_name = "AS"                 ,to_name = "ASNum"         },
    {from_name = "Ack"                ,to_name = "ACK"           },
    {from_name = "Seq"                ,to_name = "seq"           },
    {from_name = "Flag"               ,to_name = "flag"          },
    {from_name = "Opcode"             ,to_name = "opCode"        },
    {from_name = "Version"            ,to_name = "ver"           },

  }
}
yalua_register_proto(mapping)