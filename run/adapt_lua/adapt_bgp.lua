-- bgp.lua
local mapping = {
  from_proto_name = "bgp",
  to_proto_name = "bgp",
  rule_proto_name = "bgp",
  common_flag = true,
  field = {
    {from_name = "UpdateMPUnreachNLRI"          ,to_name = "mpUnreachNLRI"   ,rule_name = "multiple_proto_unreach_router"  ,tll = 1},
    {from_name = "UpdateMPUnreachNLRISafi"      ,to_name = "mpUnreachSAFI"   ,rule_name = "mp_unreach_safi"  ,tll = 1},
    {from_name = "UpdateMPUnreachNLRIAfi"       ,to_name = "mpUnreachAFI"    ,rule_name = "mp_unreach_afi"   ,tll = 1},
    {from_name = "UpdateMPReachNLRI"            ,to_name = "mpReach"         ,rule_name = "mp_reach_nlri"    ,tll = 1},
    {from_name = "UpdateMPReachNexthop"         ,to_name = "mpReachNextHop"  ,rule_name = "multiple_proto_reach_next_hop",tll = 1},
    {from_name = "UpdateMPReachNLRISafi"        ,to_name = "mpReachSAFI"     ,rule_name = "mp_reach_safi"    ,tll = 1},
    {from_name = "UpdateMPReachNLRIAfi"         ,to_name = "mpReachAFI"      ,rule_name = "mp_reach_afi"     ,tll = 1},
    {from_name = ""                             ,to_name = "NLRIPre"         },
    {from_name = ""                             ,to_name = "NLRIPreMAS"      },
    {from_name = ""                             ,to_name = "ipv6Prefix"      },
    {from_name = ""                             ,to_name = "ipv4Prefix"      },
    {from_name = ""                             ,to_name = "VPNlabSta"       },
    {from_name = ""                             ,to_name = "rouDis"          },
    {from_name = ""                             ,to_name = "prefixLen"       },
    {from_name = ""                             ,to_name = "typeCode"        },
    {from_name = ""                             ,to_name = "extAreaId"       },
    {from_name = ""                             ,to_name = "locAdmin"        },
    {from_name = ""                             ,to_name = "extMetType"      },
    {from_name = ""                             ,to_name = "extRouType"      },
    {from_name = ""                             ,to_name = "extRouID"        },
    {from_name = ""                             ,to_name = "extSubType"      },
    {from_name = "ext_UpdateCommunitiesTypeHigh",to_name = "extTypeHigh"     ,rule_name = "extend_communities_type"         ,tll = 1},
    {from_name = "UpdateCommunitiesValue"       ,to_name = "comVal"          ,rule_name = "commval"          ,tll = 1},
    {from_name = "UpdateCommunitiesAs"          ,to_name = "comAS"           ,rule_name = "commas"           ,tll = 1},
    {from_name = ""                             ,to_name = "errType"         },
    {from_name = ""                             ,to_name = "gateway"         },
    {from_name = ""                             ,to_name = "atoAgg"          },
    {from_name = ""                             ,to_name = "dstBorAS"        },
    {from_name = ""                             ,to_name = "srcBorAS"        },
    {from_name = ""                             ,to_name = "nexHopIpv6"      },
    {from_name = "OpenCapabilityTypes"          ,to_name = "capType"         ,rule_name = "cap_type"        ,tll = 1},
    {from_name = "OpenParamType"                ,to_name = "parType"         ,rule_name = "param_type"        ,tll = 1},
    {from_name = "RouteRefreshAFI"              ,to_name = "afi"             ,rule_name = "afi"              ,tll = 1},
    {from_name = "NotificationErrData"          ,to_name = "errData"         ,rule_name = "error_data"         ,tll = 1},
    {from_name = "NotificationMIEC"             ,to_name = "errSubCode"      ,rule_name = "error_subcode"      ,tll = 1},
    {from_name = "NotificationMAEC"             ,to_name = "errCode"         ,rule_name = "error_code"         ,tll = 1},
    {from_name = "OpenHoldtime"                 ,to_name = "holdTime"        ,rule_name = "holdtime"         ,tll = 1},
    {from_name = "UpdateNetworkLayerRechability",to_name = "nlri"            ,rule_name = "nlri"             ,tll = 1},
    {from_name = "UpdateWithDrawnRoutes"        ,to_name = "withDraw"        ,rule_name = "withdrawn_route"        ,tll = 1},
    {from_name = "UpdateMPUnReachability"       ,to_name = "MVNLURIPreAndMas"},
    {from_name = "UpdateMPReachability"         ,to_name = "MVNLRIPreAndMas" },
    {from_name = "RouteRefreshSAFI"             ,to_name = "SAFI"            ,rule_name = "safi"             ,tll = 1},
    {from_name = "UpdateClusterList"            ,to_name = "cluIDList"       ,rule_name = "cluster_id"          ,tll = 1},
    {from_name = "UpdateOriginatorId"           ,to_name = "originID"        ,rule_name = "original_id"        ,tll = 1},
    {from_name = "UpdateAggregatorIp"           ,to_name = "aggAddr"         ,rule_name = "aggr_ip"          ,tll = 1},
    {from_name = "UpdateAggregatorAs"           ,to_name = "aggAS"           ,rule_name = "aggregated_router_as"          ,tll = 1},
    {from_name = "ext_UpdateCommunities"        ,to_name = "extComName"      ,rule_name = "extend_communities"         ,tll = 1},
    {from_name = "UpdateCommunities"            ,to_name = "comName"         ,rule_name = "communities"             ,tll = 1},
    {from_name = "UpdateLocalPref"              ,to_name = "locPre"          ,rule_name = "local_preference"        ,tll = 1},
    {from_name = "UpdateMed"                    ,to_name = "MED"             ,rule_name = "multiple_exit_discriminator"              ,tll = 1},
    {from_name = "UpdateNextHop"                ,to_name = "nexHop"          ,rule_name = "next_hop_ip"         ,tll = 1},
    {from_name = "UpdateAsPath"                 ,to_name = "ASPath"          ,rule_name = "as_path"          ,tll = 1},
    {from_name = "UpdateOrigin"                 ,to_name = "origin"          ,rule_name = "origin"           ,tll = 1},
    {from_name = "OpenAuthenticationData"       ,to_name = "authData"        ,rule_name = "auth_data"        ,tll = 1},
    {from_name = "OpenAuthenticationCode"       ,to_name = "authCode"        ,rule_name = "auth_code"        ,tll = 1},
    {from_name = "OpenIdentifier"               ,to_name = "rouID"           ,rule_name = "router_id"               ,tll = 1},
    {from_name = "OpenMyas"                     ,to_name = "ASNum"           ,rule_name = "asn"              ,tll = 1},
    {from_name = "OpenVersion"                  ,to_name = "ver"             ,rule_name = "version"          ,tll = 1},
    {from_name = "BgpType"                      ,to_name = "msgType"         ,rule_name = "type"         ,tll = 1},
    {from_name = "marker"                       ,to_name = "marker"          ,rule_name = "marker"           ,tll = 1},

  }
}
yalua_register_proto(mapping)