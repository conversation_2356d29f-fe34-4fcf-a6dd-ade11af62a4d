-- snmp.lua
local mapping = {
  from_proto_name = "snmp",
  to_proto_name = "snmp",
  rule_proto_name = "snmp",
  common_flag = true,
  field = {
    {from_name = "agent_addr"     ,to_name = "agentAddr"     ,rule_name = "agent"      ,tll = 1},
    {from_name = "ErrorStatus"    ,to_name = "errStatus"     ,rule_name = "error_status"      ,tll = 1},
    {from_name = ""               ,to_name = "sysDescr"      ,tll = 1},
    {from_name = ""               ,to_name = "sysName"       ,tll = 1},
    {from_name = "engineIdDataStr",to_name = "authEngIDData" ,tll = 1},
    {from_name = "engineIdFormat" ,to_name = "authEngIDForm" ,tll = 1},
    {from_name = "context_name"   ,to_name = "sysCon"        ,tll = 1},
    {from_name = "objectname"     ,to_name = "objectname"    ,tll = 1},
    {from_name = "engine_id"      ,to_name = "authEngID"     ,rule_name = "engine_id"       ,tll = 1},
    {from_name = "msgAuthFlag"    ,to_name = "authFlag"      ,rule_name = "flag_auth"       ,tll = 1},
    {from_name = "msgEncryptFlag" ,to_name = "encFlag"       ,rule_name = "flag_encrypt"        ,tll = 1},
    {from_name = "msgReportFlag"  ,to_name = "repFlag"       ,rule_name = "flag_report"        ,tll = 1},
    {from_name = "ObjSyn"         ,to_name = "ObjSyn"        },
    {from_name = "objVal"         ,to_name = "objVal"        ,rule_name = "mib_object_value",tll = 1},
    {from_name = "OID"            ,to_name = "OID"           ,rule_name = "mib_oid"         ,tll = 1},
    {from_name = "sec_mode"       ,to_name = "secMode"       },
    {from_name = "enterprise"     ,to_name = "entID"         ,rule_name = "enterprise_id"   ,tll = 1},
    {from_name = "Request_ID"     ,to_name = "reqID"         },
    {from_name = "PDU_Type"       ,to_name = "PDUType"       ,rule_name = "pdutype"         ,tll = 1},
    {from_name = "Community"      ,to_name = "comm"          ,rule_name = "community"       ,tll = 1},
    {from_name = "Version"        ,to_name = "ver"           ,rule_name = "version"         ,tll = 1},

    
  }
}
yalua_register_proto(mapping)