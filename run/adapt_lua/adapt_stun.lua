-- stun.lua
local mapping = {
  from_proto_name = "stun",
  to_proto_name = "stun",
  rule_proto_name = "stun",
  common_flag = true,
  field = {
    {from_name = "Software"               ,to_name = "softName"      },
    {from_name = "Password"               ,to_name = "pwd"           },
    {from_name = "Username"               ,to_name = "usrName"       },
    {from_name = "Response_origin_port"   ,to_name = "srcPort"       },
    {from_name = "Response_origin_address",to_name = "srcAddrResp"   },
    {from_name = "Changed_port"           ,to_name = "chaPort"       },
    {from_name = "Changed_server"         ,to_name = "chaAddr"       },
    {from_name = "Change_port_flag"       ,to_name = "chaPortFlag"   },
    {from_name = "Change_ip_flag"         ,to_name = "chaIPFlag"     },
    {from_name = "Response_port"          ,to_name = "respPort"      },
    {from_name = "Response_address"       ,to_name = "respAddr"      },
    {from_name = "Mapped_port"            ,to_name = "mapPort"       },
    {from_name = "Mapped_address"         ,to_name = "mapAddr"       },
    {from_name = "Message_transaction_id" ,to_name = "traID"         },
    {from_name = "Message_type"           ,to_name = "msgType"       },

    
  }
}
yalua_register_proto(mapping)