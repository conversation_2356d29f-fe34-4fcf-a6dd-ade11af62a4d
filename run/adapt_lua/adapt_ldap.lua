-- ldap.lua
local mapping = {
  from_proto_name = "ldap",
  to_proto_name = "ldap",
  rule_proto_name = "ldap",
  common_flag = true,
  field = {
    {from_name = "AttributesValue",to_name = "attVal"        },
    {from_name = "AttributesDesc" ,to_name = "attType"       },
    {from_name = "ProtocolOp"     ,to_name = "msgType"       },
    {from_name = "MessageID"      ,to_name = "msgID"         },

    
  }
}
yalua_register_proto(mapping)