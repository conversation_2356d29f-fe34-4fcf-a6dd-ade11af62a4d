-- ocsp.lua
local mapping = {
  from_proto_name = "ocsp",
  to_proto_name = "ocsp",
  rule_proto_name = "ocsp",
  common_flag = true,
  field = {
    {from_name = ""                     ,to_name = "serLoc"                 },
    {from_name = "ArchiveCutoff"        ,to_name = "arcCut"                 },
    {from_name = "cliAccRespType"       ,to_name = "cliAccRespType"         },
    {from_name = ""                     ,to_name = "certRevListTimeInCliReq"},
    {from_name = ""                     ,to_name = "certRevListNumInCliReq" },
    {from_name = ""                     ,to_name = "certRevListURLInCliReq" },
    {from_name = "nonInCliReq"          ,to_name = "nonInCliReq"            },
    {from_name = "extIDInCliReq"        ,to_name = "extIDInCliReq"          },
    {from_name = "serialNumber"         ,to_name = "certSerNumInCliReq"     },
    {from_name = "issuerKeyHash"        ,to_name = "issPubKeyHasInCliReq"   },
    {from_name = "issuerNameHash"       ,to_name = "issNameHasInCliReq"     },
    {from_name = "hashAlgorithm_element",to_name = "hasAlgIDInCliReq"       },

    
  }
}
yalua_register_proto(mapping)