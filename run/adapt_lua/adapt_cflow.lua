-- cflow.lua
local mapping = {
  from_proto_name = "cflow",
  to_proto_name = "cflow",
  rule_proto_name = "cflow",
  common_flag = true,
  field = {
    {from_name = ""                   ,to_name = "natEvent"            },
    {from_name = "version"            ,to_name = "version"             },
    {from_name = "postNatDstTransPort",to_name = "postNaptDstTransPort"},
    {from_name = "postNatSrcTransPort",to_name = "postNaptSrcTransPort"},
    {from_name = "postNatDstIpv4Addr" ,to_name = "postNatDstIpv4Addr"  },
    {from_name = "postNatSrcIpv4Addr" ,to_name = "postNatSrcIpv4Addr"  },
    {from_name = "pkt"                ,to_name = "postPktTotalCount"   },
    {from_name = "oct"                ,to_name = "postOctTotalCount"   },
    {from_name = "direction"          ,to_name = "direction"           },
    {from_name = "oct"                ,to_name = "oct"                 },
    {from_name = "pkt"                ,to_name = "pkt"                 },
    {from_name = "outputInt"          ,to_name = "outputInt"           },
    {from_name = "inputInt"           ,to_name = "inputInt"            },
    {from_name = "tcpFlag"            ,to_name = "tcpFlag"             },
    {from_name = "tos"                ,to_name = "tos"                 },
    {from_name = "prot"               ,to_name = "prot"                },
    {from_name = "nxtHop"             ,to_name = "nxtHop"              },
    {from_name = "dstNetMas"          ,to_name = "dstNetMas"           },
    {from_name = "srcNetMas"          ,to_name = "srcNetMas"           },
    {from_name = "dstNetAs"           ,to_name = "dstNetAS"            },
    {from_name = "srcNetAs"           ,to_name = "srcNetAS"            },
    {from_name = "srcId"              ,to_name = "srcId"               },
    {from_name = "engId"              ,to_name = "engId"               },
    {from_name = "engType"            ,to_name = "engType"             },
    {from_name = "seq"                ,to_name = "seq"                 },
    {from_name = "nanoSeconds"        ,to_name = "nanoSeconds"         },
    {from_name = "seconds"            ,to_name = "seconds"             },
    {from_name = "sysUpTime"          ,to_name = "sysUpTime"           },
    {from_name = "count"              ,to_name = "count"               },
  }
}
yalua_register_proto(mapping)