-- ntlm.lua
local mapping = {
  from_proto_name = "ntlmssp",
  to_proto_name = "ntlm",
  rule_proto_name = "ntlmssp",
  common_flag = true,
  field = {
    {from_name = "TargetName"         ,to_name = "ntlmTarName"   },
    {from_name = "channelBindings"    ,to_name = "appRelationkey"},
    {from_name = "SessionKey"         ,to_name = "sesKey"        },
    {from_name = "NTLMResponse"       ,to_name = "NTResp"        },
    {from_name = "LanManagerResponse" ,to_name = "LMResp"        },
    {from_name = "NTLMClientChallenge",to_name = "cliCha"        },
    {from_name = "NTLMServerChallenge",to_name = "srvCha"        },
    {from_name = "HostName"           ,to_name = "hostName"      },
    {from_name = "UserName"           ,to_name = "usrName"       },
    {from_name = "DomainName"         ,to_name = "cliDom"        },
    {from_name = "DnsTreeName"        ,to_name = "DNSTree"       },
    {from_name = "DnsComputerName"    ,to_name = "DNSHostName"   },
    {from_name = "DnsDomainName"      ,to_name = "DNSDom"        },
    {from_name = "NbComputerName"     ,to_name = "netName"       },
    {from_name = "NbDomainName"       ,to_name = "netDomName"    },

  }
}
yalua_register_proto(mapping)