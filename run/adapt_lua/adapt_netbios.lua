-- netbios.lua
local mapping = {
  from_proto_name = "netbios",
  to_proto_name = "netbios",
  rule_proto_name = "netbios",
  common_flag = true,
  field = {
    {from_name = "calleName"       ,to_name = "calleName"       },
    {from_name = "calliName"       ,to_name = "calliName"       },
    {from_name = "msgType"         ,to_name = "msgType"         },
    {from_name = "addData"         ,to_name = "addData"         },
    {from_name = "addTtl"          ,to_name = "addTtl"          },
    {from_name = "addClass"        ,to_name = "addClass"        },
    {from_name = "addType"         ,to_name = "addType"         },
    {from_name = "addName"         ,to_name = "addName"         },
    {from_name = "authData"        ,to_name = "authData"        },
    {from_name = "authTtl"         ,to_name = "authTtl"         },
    {from_name = "authClass"       ,to_name = "authClass"       },
    {from_name = "authType"        ,to_name = "authType"        },
    {from_name = "authName"        ,to_name = "authName"        },
    {from_name = "ansData"         ,to_name = "ansData"         },
    {from_name = "ansTtl"          ,to_name = "ansTtl"          },
    {from_name = "ansClass"        ,to_name = "ansClass"        },
    {from_name = "qryClass"        ,to_name = "qryClass"        },
    {from_name = "resRecType"      ,to_name = "resRecType"      },
    {from_name = "ansName"         ,to_name = "ansName"         },
    {from_name = "qryRecType"      ,to_name = "qryRecType"      },
    {from_name = "qryName"         ,to_name = "qryName"         },
    {from_name = "sessDatePktSize" ,to_name = "sessDataPktSize" },
    {from_name = "maxTotalSessPoss",to_name = "maxTotalSessPoss"},
    {from_name = "maxPendingNum"   ,to_name = "maxPendingNum"   },
    {from_name = "pendingNum"      ,to_name = "pendingNUm"      },
    {from_name = ""                ,to_name = "maxTotalCmdNum"  },
    {from_name = ""                ,to_name = "totalCmdNum"     },
    {from_name = ""                ,to_name = "freeCmdNum"      },
    {from_name = "noResrcNum"      ,to_name = "noResrcNum"      },
    {from_name = "retransNum"      ,to_name = "retransNUm"      },
    {from_name = "goodRecvsNum"    ,to_name = "goodRecvsNum"    },
    {from_name = "goodSendsNum"    ,to_name = "goodSendsNum"    },
    {from_name = "sendAbortsNum"   ,to_name = "sendAbortsNum"   },
    {from_name = "collNum"         ,to_name = "collNUm"         },
    {from_name = "aligErrNum"      ,to_name = "aligErrNum"      },
    {from_name = "crcsNum"         ,to_name = "crcsNum"         },
    {from_name = "statisticsPer"   ,to_name = "statisticsPer"   },
    {from_name = "verNum"          ,to_name = "verNum"          },
    {from_name = "testRes"         ,to_name = "testRes"         },
    {from_name = "jumpers"         ,to_name = "jumpers"         },
    {from_name = "tid"             ,to_name = "uintID"          },
    {from_name = "nbstatName"      ,to_name = "nbstatName"      },
    {from_name = "nbAddr"          ,to_name = "nbAddr"          },
    {from_name = "rcode"           ,to_name = "rcode"           },
    {from_name = "raflag"          ,to_name = "raflag"          },
    {from_name = "rdflag"          ,to_name = "rdflag"          },
    {from_name = "tcflag"          ,to_name = "tcflag"          },
    {from_name = "aaflag"          ,to_name = "aaflag"          },
    {from_name = "opcode"          ,to_name = "opcode"          },
    {from_name = "AddNum"          ,to_name = "AddNum"          },
    {from_name = "AuthNum"         ,to_name = "AuthNum"         },
    {from_name = "AnsNum"          ,to_name = "AnsNum"          },
    {from_name = "QuestNum"        ,to_name = "QuestNum"        },
    {from_name = "tid"             ,to_name = "tid"             },
    {from_name = "addr"            ,to_name = "addr"            },
    {from_name = "netbiosSuf"      ,to_name = "netbiosSuf"      },
    {from_name = "netbiosName"     ,to_name = "netbiosName"     },
    {from_name = "dstHostName"     ,to_name = "dstHostName"     },
    {from_name = "srcHostName"     ,to_name = "srcHostName"     },
    {from_name = "srcHostAddr"     ,to_name = "srcHostAddr"     },

    
  }
}
yalua_register_proto(mapping)