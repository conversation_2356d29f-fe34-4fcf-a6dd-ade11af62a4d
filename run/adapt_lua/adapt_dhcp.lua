-- dhcp.lua
local mapping = {
  from_proto_name = "dhcp",
  to_proto_name = "dhcp",
  rule_proto_name = "dhcp",
  common_flag = true,
  field = {
    {from_name = "OptionNum"       ,to_name = "optLen"        },
    {from_name = "Seconds"         ,to_name = "leaTerm"       },
    {from_name = "OptionData"      ,to_name = "optVal"        },
    {from_name = "OptionTag"       ,to_name = "optType"       },
    {from_name = "BootFileName"    ,to_name = "booFileName"   },
    {from_name = "Servername"      ,to_name = "svcHosName"    },
    {from_name = "ClientMacAddr"   ,to_name = "cliMacAddr"    },
    {from_name = "RelayAgentIPaddr",to_name = "relAddr"       },
    {from_name = "ServerIPaddr"    ,to_name = "svcAddr"       },
    {from_name = "YourIPaddr"      ,to_name = "newCliAddr"    },
    {from_name = "ClientIPaddr"    ,to_name = "curAddr"       },
    {from_name = "HardwareType"    ,to_name = "hardwType"     },
    {from_name = "MessageType"     ,to_name = "code"          },

    
  }
}
yalua_register_proto(mapping)