-- icmp.lua
local mapping = {
  from_proto_name = "icmp",
  to_proto_name = "icmp",
  rule_proto_name = "icmp",
  common_flag = true,
  field = {
    {from_name = "unr_ttl"       ,to_name = "excTTL"        ,rule_name = "unreachable_ttl"     ,tll = 1},
    {from_name = "response_time" ,to_name = "resTime"       ,rule_name = "resp_time"   ,tll = 1},
    {from_name = "checksum"      ,to_name = "checkSum"      },
    {from_name = "mulCastAddr"   ,to_name = "mulCastAddr"   },
    {from_name = "pointer"       ,to_name = "excPointer"    },
    {from_name = "mtu"           ,to_name = "nextHopMtu"    },
    {from_name = "unr_dstip"     ,to_name = "ndpTarAddr"    },
    {from_name = "curMtu"        ,to_name = "ndpCurMtu"     },
    {from_name = "validtime"     ,to_name = "ndpValLifeTime"},
    {from_name = "prefix"        ,to_name = "ndpPreFix"     },
    {from_name = "prelen"        ,to_name = "ndpPreLen"     },
    {from_name = "linkaddr"      ,to_name = "ndpLinkAddr"   },
    {from_name = "lifetime"      ,to_name = "ndpLifeTime"   },
    {from_name = "qurIP4"        ,to_name = "qurIpv4Addr"   },
    {from_name = "qurIP6"        ,to_name = "qurIpv6Addr"   },
    {from_name = "qurType"       ,to_name = "qurType"       },
    {from_name = ""              ,to_name = "ttl"           },
    {from_name = "gatewayAddr"   ,to_name = "gwAddr"        },
    {from_name = "exc_dstport"   ,to_name = "excDstPort"    ,rule_name = "unr_port_dst",tll = 1},
    {from_name = "exc_srcport"   ,to_name = "excSrcPort"    ,rule_name = "unr_port_src",tll = 1},
    {from_name = "exc_proto"     ,to_name = "excProt"       ,rule_name = "unr_proto"   ,tll = 1},
    {from_name = "exc_dstaddr"   ,to_name = "excDstAddr"    ,rule_name = "unr_dst"     ,tll = 1},
    {from_name = "exc_srcaddr"   ,to_name = "excSrcAddr"    ,rule_name = "unr_src"     ,tll = 1},
    {from_name = "transmit_time" ,to_name = "transTimeStamp"},
    {from_name = "receive_time"  ,to_name = "recvTimeStamp" },
    {from_name = "origin_time"   ,to_name = "origTimeStamp" },
    {from_name = "payload"       ,to_name = "dataCon"       ,rule_name = "data" ,tll = 1},
    {from_name = "seq"           ,to_name = "echoSeqNum"    ,rule_name = "seq"         ,tll = 1},
    {from_name = "code"          ,to_name = "infoCode"      ,rule_name = "code"        ,tll = 1},
    {from_name = "type"          ,to_name = "msgType"       ,rule_name = "type"        ,tll = 1},

  }
}
yalua_register_proto(mapping)