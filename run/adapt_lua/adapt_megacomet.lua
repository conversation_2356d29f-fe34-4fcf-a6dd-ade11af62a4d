-- megacomet.lua
local mapping = {
  from_proto_name = "megaco",
  to_proto_name = "megacomet",
  rule_proto_name = "megaco",
  common_flag = true,
  field = {
    {from_name = "remote_sdp_desc"    ,to_name = "remDesc"       },
    {from_name = "local_sdp_desc"     ,to_name = "locDesc"       },
    {from_name = "observedevents"     ,to_name = "obsEveDesc"    },
    {from_name = "media_stream_mode"  ,to_name = "strMode"       },
    {from_name = "media_stream_id"    ,to_name = "strID"         },
    {from_name = "endpoint_identifier",to_name = "terID"         },
    {from_name = "command"            ,to_name = "cmdName"       },
    {from_name = "context_id"         ,to_name = "conID"         },
    {from_name = "transaction_name"   ,to_name = "traName"       },
    {from_name = "transaction_id"     ,to_name = "traID"         },
    {from_name = "media_gateway_id"   ,to_name = "msgID"         },
    {from_name = "megaco_version"     ,to_name = "ver"           },

  }
}
yalua_register_proto(mapping)