-- ip.lua
local mapping = {
  from_proto_name = "ip",
  to_proto_name = "ip",
  rule_proto_name = "ip",
  common_flag = true,
  field = {
    {from_name = "ip_contentlen" ,to_name = "content_len"   ,rule_name = "content_length"},
    {from_name = "ip_content"    ,to_name = "content"       ,rule_name = "content"    },
    {from_name = "ip_ttl"        ,to_name = "ttl"           ,rule_name = "ttl"        },
    {from_name = "ip_header"     ,to_name = "header"        ,rule_name = "header"     },
    {from_name = "ip_len"        ,to_name = "len"           ,rule_name = "length"        },
    {from_name = "ip_flag"       ,to_name = "flag"          ,rule_name = "flags"       },

  }
}
yalua_register_proto(mapping)