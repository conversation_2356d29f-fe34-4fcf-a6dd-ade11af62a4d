-- stp.lua
local mapping = {
  from_proto_name = "stp",
  to_proto_name = "stp",
  rule_proto_name = "stp",
  common_flag = true,
  field = {
    {from_name = "port_id"           ,to_name = "portID"        },
    {from_name = "bridge_system_addr",to_name = "briMacAddr"    },
    {from_name = "bridge_priority"   ,to_name = "briID"         },
    {from_name = "root_path_cost"    ,to_name = "rooPathCos"    },
    {from_name = "root_system_addr"  ,to_name = "rooMACAddr"    },
    {from_name = "root_priority"     ,to_name = "rooID"         },
    {from_name = "flag"              ,to_name = "PDUFlag"       },
    {from_name = "message_type"      ,to_name = "PDUType"       },

    
  }
}
yalua_register_proto(mapping)