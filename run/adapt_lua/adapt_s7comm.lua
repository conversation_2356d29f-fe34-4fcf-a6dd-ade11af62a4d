-- s7comm.lua
local mapping = {
  from_proto_name = "s7comm",
  to_proto_name = "s7comm",
  rule_proto_name = "s7comm",
  common_flag = true,
  field = {
    {from_name = "Type"           ,to_name = "paraType"      ,rule_name = "type" ,tll = 1},
    {from_name = "Data"           ,to_name = "data"          ,rule_name = "data"       ,tll = 1},
    {from_name = "Sub"            ,to_name = "paraSub"       ,rule_name = "sub_function"  ,tll = 1},
    {from_name = "Mode"           ,to_name = "paraMode"      ,rule_name = "param_code" ,tll = 1},
    {from_name = "Group"          ,to_name = "paraGrp"       ,rule_name = "function_gourp",tll = 1},
    {from_name = "Code"           ,to_name = "paraCode"      ,rule_name = "param_code" ,tll = 1},
    {from_name = "Class"          ,to_name = "paraClass"     ,rule_name = "param_class",tll = 1},
    {from_name = "DataEntry"      ,to_name = "dataInfo"      ,tll = 1},
    {from_name = "DataEntries"    ,to_name = "itemCnt"       },
    {from_name = "Parameter"      ,to_name = "para"          ,rule_name = "param"       ,tll = 1},
    {from_name = "DataLength"     ,to_name = "dataLen"       ,rule_name = "data_length"   ,tll = 1},
    {from_name = "ParameterLength",to_name = "paraLen"       ,rule_name = "param_length"  ,tll = 1},
    {from_name = "PDUReference"   ,to_name = "pdu"           ,rule_name = "pdu"        ,tll = 1},
    {from_name = "Rosctr"         ,to_name = "rosctr"        ,rule_name = "rosctr"     ,tll = 1},
    {from_name = "ProtoIdentifier",to_name = "proto"         ,rule_name = "proto_id"      ,tll = 1},

    
  }
}
yalua_register_proto(mapping)