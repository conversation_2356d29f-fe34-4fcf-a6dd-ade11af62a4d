-- xwin.lua
local mapping = {
  from_proto_name = "xwin",
  to_proto_name = "xwin",
  rule_proto_name = "",
  common_flag = true,
  field = {
    {from_name = "majorVersion"  ,to_name = "majVer"        },
    {from_name = "minorVersion"  ,to_name = "minVer"        },
    {from_name = "opCode"        ,to_name = "opCode"        },
    {from_name = "keyCode"       ,to_name = "keyCode"       },
    {from_name = "vendor"        ,to_name = "venName"       },
    {from_name = "fontName"      ,to_name = "fonName"       },
    {from_name = "cooX"          ,to_name = "Xcoo"          },
    {from_name = "cooY"          ,to_name = "Ycoo"          },
    {from_name = "winWidth"      ,to_name = "winWid"        },
    {from_name = "winHeight"     ,to_name = "winHei"        },

  }
}
yalua_register_proto(mapping)