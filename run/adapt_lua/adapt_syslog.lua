-- syslog.lua
local mapping = {
  from_proto_name = "syslog",
  to_proto_name = "syslog",
  rule_proto_name = "syslog",
  common_flag = true,
  field = {
    {from_name = "processid"     ,to_name = "processId"     ,rule_name = "pid"       },
    {from_name = ""              ,to_name = "serviceType"   },
    {from_name = "messageid"     ,to_name = "eventInfo"     ,rule_name = "info"},
    {from_name = "appname"       ,to_name = "event"         ,rule_name = "event"     },
    {from_name = "message"       ,to_name = "message"       ,rule_name = "msg"   },
    {from_name = "hostname"      ,to_name = "hostname"      ,rule_name = "host"  },
    {from_name = "timestamp"     ,to_name = "timesstamp"    ,rule_name = "time" },
    {from_name = "level"         ,to_name = "level"         ,rule_name = "level"     },
    {from_name = "facility"      ,to_name = "facility"      ,rule_name = "facility"  },

  }
}
yalua_register_proto(mapping)