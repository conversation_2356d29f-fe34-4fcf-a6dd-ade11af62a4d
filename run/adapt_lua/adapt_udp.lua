-- udp.lua
local mapping = {
  from_proto_name = "udp",
  to_proto_name = "udp",
  rule_proto_name = "udp",
  common_flag = true,
  field = {
    {from_name = "udp_payloadlen",to_name = "payload_len"   ,rule_name = "payload_length"},
    {from_name = "udp_payload"   ,to_name = "payload"       ,rule_name = "payload"    },
    {from_name = "udp_header"    ,to_name = "header"        ,rule_name = "header"     },

  }
}
yalua_register_proto(mapping)