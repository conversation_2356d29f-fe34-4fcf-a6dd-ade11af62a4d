-- dtls.lua
local mapping = {
  from_proto_name = "dtls",
  to_proto_name = "dtls",
  rule_proto_name = "dtls",
  common_flag = true,
  field = {
    {from_name = "ClientCertificateLength"    ,to_name = "cliCertLen"     },
    {from_name = "ClientCertificateIssuerName",to_name = "cliCertAuthDesc"},
    {from_name = "CertificatesLength"         ,to_name = "srvCertLen"     },
    {from_name = "ServerExtensions"           ,to_name = "srvExt"         },
    {from_name = "ServerCompressionMethod"    ,to_name = "srvComMet"      },
    {from_name = "ServerCipherSuite"          ,to_name = "srvCipSui"      },
    {from_name = "ServertoClientcookie"       ,to_name = "srvToCliCoo"    },
    {from_name = "ServerSessionID"            ,to_name = "srvSesID"       },
    {from_name = "ServerRandomBytes"          ,to_name = "srvRand"        },
    {from_name = "ServerGMTUnixTime"          ,to_name = "srvGMTUniTime"  },
    {from_name = "ServerVersion"              ,to_name = "srvVer"         },
    {from_name = "ClientExtensions"           ,to_name = "cliExt"         },
    {from_name = "ClientCompressionMethods"   ,to_name = "cliComMet"      },
    {from_name = "ClientCipherSuites"         ,to_name = "cliCipSui"      },
    {from_name = "ClientCookie"               ,to_name = "cliCookie"      },
    {from_name = "ClientSessionID"            ,to_name = "cliSesID"       },
    {from_name = "ClientRandomBytes"          ,to_name = "cliRand"        },
    {from_name = "ClientGMTUnixTime"          ,to_name = "cliGMTUniTime"  },
    {from_name = "HandshakeType"              ,to_name = "handShaType"    },
    {from_name = "AlertDescription"           ,to_name = "aleDesc"        },
    {from_name = "AlertLevel"                 ,to_name = "aleLev"         },
    {from_name = "Epoch"                      ,to_name = "epoch"          },
    {from_name = "Version"                    ,to_name = "version"        },
    {from_name = "ContentType"                ,to_name = "conType"        },

    
  }
}
yalua_register_proto(mapping)