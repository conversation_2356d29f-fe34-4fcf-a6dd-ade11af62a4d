#include "dpi_utils.h"
#include "dpi_common.h"
#include "dpi_log.h"

#include <ctype.h>
#include <stdio.h>
#include <string.h>
#include <sys/types.h>
#include <dirent.h>
#include <ifaddrs.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "syscall.h"
#include <sys/time.h>
#include "cpuid.h"


const char *dpi_utils_show_bytes(uint64_t bytes, char out[], int len)
{
    if (bytes > 1024 * 1024 * 1024)
        snprintf(out, len, "%.2lf GB", (double)bytes / (1024 * 1024 * 1024));
    else if (bytes > 1024 * 1024)
        snprintf(out, len, "%.2lf MB", (double)bytes / (1024 * 1024));
    else if (bytes > 1024)
        snprintf(out, len, "%.2lf KB", (double)bytes / (1024));
    else
        snprintf(out, len, "%lu B", bytes);

    return out;
}


const char *dpi_utils_show_bps(uint64_t bps, char out[], int len)
{
    if (bps > 1000 * 1000 * 1000)
        snprintf(out, len, "%.2lf Gbps", (double)bps / (1000 * 1000 * 1000));
    else if (bps > 1000 * 1000)
        snprintf(out, len, "%.2lf Mbps", (double)bps / (1000 * 1000));
    else if (bps > 1000)
        snprintf(out, len, "%.2lf Kbps", (double)bps / (1000));
    else
        snprintf(out, len, "%lu bps", bps);

    return out;
}


static const char s_chars[] = {0x0a, 0x0d, 0x20, 0x00};

/**
 * 去除字符串左边的指定字符
 * @param chars
 *      指定删除的字符串, 以'\0'结束。
 *      ==NULL 时默认空白符。
 *
 * @return
 *     自左向右偏移后的位置, 原始内容不变
*/
char *dpi_utils_lstrip(char *str, int max_len, const char *chars)
{
    int i;

    if(chars == NULL)
        chars = s_chars;

    for(i = 0; i < max_len; i++)
    {
        if(find_special_char((const uint8_t *)chars, strlen(chars), str[i]) < 0)
            break;
    }

    return &str[i];
}

/**
 * 去除字符串右边的指定字符,
 * @param chars
 *      指定删除的字符串, 以'\0'结束。
 *      ==NULL 时默认空白符。
 *
 * @return
 *      原始str指针, 最右边的目标字符替换为'\0'
*/
char *dpi_utils_rstrip(char *str, int max_len, const char *chars)
{
    int i;

    if(chars == NULL)
        chars = s_chars;

    for(i = max_len -1; i >= 0; i--)
    {
        if(find_special_char((const uint8_t *)chars, strlen(chars), str[i]) < 0)
            break;

        str[i] = '\0';
    }

    return str;
}

/**
 * 同时执行 dpi_utils_lstrip 和 dpi_utils_rstrip
*/
char *dpi_utils_strip(char *str, int max_len, const char *chars)
{
    char *p = dpi_utils_lstrip(str, max_len, chars);
    return dpi_utils_rstrip(p, max_len - (p - str), chars);
}



char * dpi_strstr_kmp(const char * haystack, const char * needle)
{
  int len_haystack = strlen(haystack);
  int len_needle = strlen(needle);

  if (len_needle > len_haystack) {
    return NULL;
  }

  int i = 0, j = -1;
  int next[len_needle];
  next[0] = -1;

  while (i < len_needle - 1) {
    if (j == -1 || needle[i] == needle[j]) {
      ++i;
      ++j;
      next[i] = (needle[i] != needle[j]) ? j : next[j];
    } else {
      j = next[j];
    }
  }

  i = j = 0;
  while (i < len_haystack && j < len_needle) {
    if (j == -1 || haystack[i] == needle[j]) {
      ++i;
      ++j;
    } else {
      j = next[j];
    }
  }

  if (j == len_needle) {
    return (char *)(haystack + i - j);
  } else {
    return NULL;
  }
}

char * dpi_strstr(const char * haystack, int len_haystack,
                   const char * needle, int len_needle)
{
  if (len_needle > len_haystack) {
    return NULL;
  }

  int i = 0, j = -1;
  int next[len_needle];
  next[0] = -1;

  while (i < len_needle - 1) {
    if (j == -1 || needle[i] == needle[j]) {
      ++i;
      ++j;
      next[i] = (needle[i] != needle[j]) ? j : next[j];
    } else {
      j = next[j];
    }
  }

  i = j = 0;
  while (i < len_haystack && j < len_needle) {
    if (j == -1 || haystack[i] == needle[j]) {
      ++i;
      ++j;
    } else {
      j = next[j];
    }
  }

  if (j == len_needle) {
    return (char *)(haystack + i - j);
  } else {
    return NULL;
  }
}

int dpi_str_join(char *str1, const char * str2, const char * spearator)
{
    if (str1 == NULL || str2 == NULL) {
        return -1;
    }

    if (strlen(str1) > 0) {
        strcat(str1, spearator);
    }

    strcat(str1, str2);


    return 0;
}

/** IPv4, TCP, UDP 基于 checksum 的流完整性校验 **/
// IPv4伪头部
typedef struct {
    uint8_t  saddr[4];
    uint8_t  daddr[4];
    uint8_t  res1;
    uint8_t  proto;
    uint16_t tlen;
} PseudoIPv4Hdr;

// IPv6伪头部
typedef struct {
    uint8_t  saddr[16];
    uint8_t  daddr[16];
    uint8_t  res1;
    uint8_t  proto;
    uint16_t tlen;
} PseudoIPv6Hdr;

static uint16_t TCP_checksum(uint16_t *pseudoIPHdr, size_t ipHdrlen, uint16_t *pkt, uint16_t tlen, uint16_t init)
{
    uint16_t pad = 0;
    uint32_t csum = init;
    uint16_t i;

    for (i=0; i < ipHdrlen/2; i++) {
        csum += pseudoIPHdr[i];
    }

    csum += pkt[0] + pkt[1] + pkt[2] + pkt[3] + pkt[4] +
            pkt[5] + pkt[6] + pkt[7] + pkt[9];

    tlen -= 20;
    pkt += 10;

    while (tlen > 1) {
        csum += pkt[0];
        pkt += 1;
        tlen -= 2;
    }

    if (tlen == 1) {
        *(uint8_t *)(&pad) = (*(uint8_t *)pkt);
        csum += pad;
    }

    csum = (csum >> 16) + (csum & 0x0000FFFF);
    csum += (csum >> 16);

    return (uint16_t)~csum;
}


static uint16_t UDP_checksum(uint16_t *ipHdr, size_t ipHdrLen, uint16_t *pkt, uint16_t tlen, uint16_t init)
{
    uint16_t pad = 0;
    uint32_t csum = init;
    uint16_t i;

    for (i=0; i < ipHdrLen/2; i++)
    {
        csum += ipHdr[i];
    }

    csum += pkt[0] + pkt[1] + pkt[2];
    tlen -= 8;
    pkt += 4;

    while (tlen > 1) {
        csum += pkt[0];
        pkt += 1;
        tlen -= 2;
    }

    if (tlen == 1) {
        *(uint8_t *)(&pad) = (*(uint8_t *)pkt);
        csum += pad;
    }

    csum = (csum >> 16) + (csum & 0x0000FFFF);
    csum += (csum >> 16);

    return (uint16_t)~csum;
}


uint16_t IPv4Checksum(const uint16_t *pkt, uint16_t hlen, uint16_t init)
{
    uint16_t i;
    uint32_t csum = init;
    csum += pkt[0] + pkt[1] + pkt[2] + pkt[3] + pkt[4] +
            pkt[6] + pkt[7] + pkt[8] + pkt[9];

    i = 0;
    hlen -= 20;
    pkt += 10;

    while (i < hlen)
    {
        csum += pkt[i];
        i += 2;
    }

    csum = (csum >> 16) + (csum & 0x0000FFFF);
    csum += (csum >> 16);

    return (uint16_t)~csum;
}


uint16_t TCPv4Checksum(const struct dpi_iphdr *iph4, const struct dpi_tcphdr *tcph, uint16_t tlen, uint16_t init)
{
    uint16_t        csum;
    PseudoIPv4Hdr   pedIpHdr;

    memcpy(&pedIpHdr, iph4->saddr, 8);
    pedIpHdr.res1   = 0;
    pedIpHdr.proto  = 6;
    pedIpHdr.tlen   = htons(tlen);

    csum = TCP_checksum((uint16_t*)&pedIpHdr, sizeof(PseudoIPv4Hdr), (uint16_t*)tcph, tlen, init);

    return csum;
}

uint16_t TCPv6Checksum(const struct dpi_ipv6hdr *iph6, const struct dpi_tcphdr  *tcph, uint16_t tlen, uint16_t init)
{
    uint16_t        csum;
    PseudoIPv6Hdr   pedIpHdr;

    memcpy(&pedIpHdr, iph6->ip6_src, 32);
    pedIpHdr.res1   = 0;
    pedIpHdr.proto  = 6;
    pedIpHdr.tlen   = htons(tlen);

    csum = TCP_checksum((uint16_t*)&pedIpHdr, sizeof(PseudoIPv6Hdr), (uint16_t*)tcph, tlen, init);

    return csum;
}

uint16_t UDPv4Checksum(const struct dpi_iphdr *iph4, const struct dpi_udphdr  *udph, uint16_t tlen, uint16_t init)
{
    uint16_t        csum;
    PseudoIPv4Hdr   pedIpHdr;

    memcpy(&pedIpHdr, iph4->saddr, 8);
    pedIpHdr.res1   = 0;
    pedIpHdr.proto  = 17;
    pedIpHdr.tlen   = htons(tlen);

    csum = UDP_checksum((uint16_t*)&pedIpHdr, sizeof(PseudoIPv4Hdr), (uint16_t*)udph, tlen, init);

    return csum;
}


uint16_t UDPv6Checksum(const struct dpi_ipv6hdr *iph6, const struct dpi_udphdr  *udph, uint16_t tlen, uint16_t init)
{
    uint16_t        csum;
    PseudoIPv6Hdr   pedIpHdr;

    memcpy(&pedIpHdr, iph6->ip6_src, 32);
    pedIpHdr.res1   = 0;
    pedIpHdr.proto  = 17;
    pedIpHdr.tlen   = htons(tlen);

    csum = UDP_checksum((uint16_t*)&pedIpHdr, sizeof(PseudoIPv6Hdr), (uint16_t*)udph, tlen, init);

    return csum;
}

static int is_leap_year(time_t year) {
    if (year % 4) return 0;         /* A year not divisible by 4 is not leap. */
    else if (year % 100) return 1;  /* If div by 4 and not 100 is surely leap. */
    else if (year % 400) return 0;  /* If div by 100 *and* 400 is not leap. */
    else return 1;                  /* If div by 100 and not by 400 is leap. */
}

void nolocks_localtime(struct tm *tmp, time_t t, time_t tz, int dst)
{
    if(tmp == NULL)  return;
    const time_t secs_min = 60;
    const time_t secs_hour = 3600;
    const time_t secs_day = 3600*24;

    t -= tz;                            /* Adjust for timezone. */
    t += 3600*dst;                      /* Adjust for daylight time. */
    time_t days = t / secs_day;         /* Days passed since epoch. */
    time_t seconds = t % secs_day;      /* Remaining seconds. */

    tmp->tm_isdst = dst;
    tmp->tm_hour = seconds / secs_hour;
    tmp->tm_min = (seconds % secs_hour) / secs_min;
    tmp->tm_sec = (seconds % secs_hour) % secs_min;

    /* 1/1/1970 was a Thursday, that is, day 4 from the POV of the tm structure
 *      * where sunday = 0, so to calculate the day of the week we have to add 4
 *           * and take the modulo by 7. */
    tmp->tm_wday = (days+4)%7;

    /* Calculate the current year. */
    tmp->tm_year = 1970;
    while(1) {
        /* Leap years have one day more. */
        time_t days_this_year = 365 + is_leap_year(tmp->tm_year);
        if (days_this_year > days) break;
        days -= days_this_year;
        tmp->tm_year++;
    }
    tmp->tm_yday = days;  /* Number of day of the current year. */

    /* We need to calculate in which month and day of the month we are. To do
 *      * so we need to skip days according to how many days there are in each
 *           * month, and adjust for the leap year that has one more day in February. */
    int mdays[12] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    mdays[1] += is_leap_year(tmp->tm_year);

    tmp->tm_mon = 0;
    while(days >= mdays[tmp->tm_mon]) {
        days -= mdays[tmp->tm_mon];
        tmp->tm_mon++;
    }

    tmp->tm_mday = days+1;  /* Add 1 since our 'days' is zero-based. */
    tmp->tm_year -= 1900;   /* Surprisingly tm_year is year-1900. */
}

int dpi_utils_strftime(char buff[], int buff_size, const char *format)
{
    time_t t = time(NULL);
    //struct tm *tmp = localtime(&t);
    struct tm tmp;
    nolocks_localtime(&tmp, t, -28800, 0);
    strftime(buff, buff_size, "%Y%m%d%H%M%S", &tmp);

    return 0;
}

char* dpi_utils_get_ipv4_addr_of_if(const char *interface_name,char* ip_addr)
{
    struct ifaddrs *ifap, *ifa;
    struct sockaddr_in *sa;
    char *ip_address = NULL;

    if (getifaddrs(&ifap) == -1)
    {
        return NULL;
    }

    for (ifa = ifap; ifa; ifa = ifa->ifa_next)
    {
        if (ifa->ifa_addr->sa_family == AF_INET && strcmp(ifa->ifa_name, interface_name) == 0)
        {
            sa = (struct sockaddr_in *) ifa->ifa_addr;
            ip_address = inet_ntoa(sa->sin_addr);
            break;
        }
    }

    freeifaddrs(ifap);
    if(ip_address)
    {
      strcpy(ip_addr,ip_address);
    }else {
      strcpy(ip_addr, "127.0.0.1");
    }

    return ip_address;
}

char* dpi_utils_get_tbl_out_dir(const char *mount_path,char* operation){
    int ret = 0;
    FILE *fp = NULL;
    char buffer[1024] = {0};
    char cmd[1024] = {0};

    if (strlen(mount_path) == 0)
      goto finish;

    snprintf(cmd, sizeof(cmd), "mount | grep -i %s", mount_path);
    fp = popen(cmd, "r");
    if (fp == NULL)
        goto finish;
    fgets(buffer, sizeof(buffer), fp);
    pclose(fp);
    if (strlen(buffer) == 0)
        goto finish;

    printf("挂载NFS ：%s\n", mount_path);
    strncpy(g_config.tbl_out_dir, mount_path, sizeof(g_config.tbl_out_dir));

finish:
    if (g_config.tbl_out_dir[strlen(g_config.tbl_out_dir) - 1] == '/')
        g_config.tbl_out_dir[strlen(g_config.tbl_out_dir) - 1] = '\0';
    //if (!strstr(g_config.tbl_out_dir, operation)) {
    //    strcat(g_config.tbl_out_dir, "/");
    //    strcat(g_config.tbl_out_dir, operation);
    //}
    printf("TBL 输出路径 ：%s\n", g_config.tbl_out_dir);

    strncpy(g_config.sdt_out_pcap_dir, g_config.tbl_out_dir, sizeof(g_config.sdt_out_pcap_dir));
    strncpy(g_config.sdt_out_event_dir, g_config.tbl_out_dir, sizeof(g_config.sdt_out_event_dir));
    strncpy(g_config.sdt_out_syslog_dir, g_config.tbl_out_dir, sizeof(g_config.sdt_out_syslog_dir));
    return g_config.tbl_out_dir;
}

void dpi_utils_traverse_dir(const char * path, dir_callback cb)
{
  DIR           *dir;
  struct dirent *entry;
  struct stat    statbuf;

  // 打开目录
  if ((dir = opendir(path)) == NULL) {
    perror("opendir");
    return;
  }

  // 读取目录中的每个项
  while ((entry = readdir(dir)) != NULL) {
    char filepath[1024];
    snprintf(filepath, sizeof(filepath), "%s/%s", path, entry->d_name);

    // 获取文件信息
    if (lstat(filepath, &statbuf) == -1) {
      perror("lstat");
      continue;
    }

    // 如果是目录，则递归遍历该目录
    if (S_ISDIR(statbuf.st_mode)) {
      // 忽略 . 和 .. 目录
      if (strcmp(entry->d_name, ".") != 0 && strcmp(entry->d_name, "..") != 0) {
        dpi_utils_traverse_dir(filepath, cb);
      }
    } else {
      cb(filepath);
    }
  }

  closedir(dir);
}

//名单查找, 找到返回1,没找到返回0
gpointer white_filter(const char* servername, uint16_t namelen, GHashTable *whitelist_table)
{
    gpointer ret;
    int len, i = 0;
    char * tmp= strndup(servername, namelen);
    if(tmp){
        len = strlen(tmp);
        while(len--){
            if(tmp[len] == '.')
                ++i;
            if(i == 2)
                break;
        }
        ret = g_hash_table_lookup(whitelist_table, tmp + 1 + len);
        if((!ret) && len >= 0)
            ret = g_hash_table_lookup(whitelist_table, tmp);
        free(tmp);
        return ret;
    }
    else{
        DPI_LOG(DPI_LOG_ERROR, "malloc fail");
    }
    return 0;
}

__thread uint32_t	sdx_file_sn;		// 当天由该线程创建的文件个数计数，从1开始，长度不固定
__thread char		sdx_cur_date[20];	// 当天的日期 ex:2022-10-19
rte_atomic64_t current_file_writ_counts;

int get_special_filename(const char *taskid, const char *business_name, const char *ext_name, char *name, int len, uint8_t sn_update) {
	if (!business_name || !ext_name || !name || len == 0)
		return 0;

	int ret;
	char tmp[20];
	struct tm tm_tmp;
	struct timeval t;

	gettimeofday(&t, NULL);
	localtime_r(&t.tv_sec, &tm_tmp);

	memset(tmp, 0, sizeof(tmp));
	snprintf(tmp, sizeof(tmp), "%4d-%02d-%02d", tm_tmp.tm_year + 1900, tm_tmp.tm_mon + 1, tm_tmp.tm_mday);
  if (strcmp(tmp, sdx_cur_date) != 0) {
          memset(sdx_cur_date, 0, sizeof(sdx_cur_date));
          strcpy(sdx_cur_date, tmp);
          rte_atomic64_set(&current_file_writ_counts, 0);
          sdx_file_sn = 1;
  }
  if (g_config.tbl_out_minute_dir) {
          snprintf(g_config.tbl_out_dir_time_dir, sizeof(g_config.tbl_out_dir_time_dir), "%04d-%02d-%02d/%02d-%02d",
              tm_tmp.tm_year + 1900, tm_tmp.tm_mon + 1, tm_tmp.tm_mday, tm_tmp.tm_hour, tm_tmp.tm_min);
  } else if (sdx_file_sn == 1) {
          strftime(g_config.tbl_out_dir_time_dir, sizeof(g_config.tbl_out_dir_time_dir), "%Y%m%d", &tm_tmp);
  }
  char dir[COMMON_FILE_PATH] = {0};

  snprintf(dir, COMMON_FILE_PATH, "%s/datagram/%s/%s", g_config.tbl_out_dir, business_name, g_config.tbl_out_dir_time_dir);
	mkdirs(dir);

  const char *ip_addr = g_config.sdx_config.sdx_ip_str;
  char suffix[16] = {0};
  if(strlen(ext_name)>16){
      strncpy(suffix, ext_name, strlen(ext_name) > sizeof(suffix) ? sizeof(suffix) - 1 : strlen(ext_name));
      ext_name = suffix;
  }
  ret = snprintf(name, len,
      "%s/%s-%s-%s-%u.%s", dir,
      g_config.sdt_out_produce_data_dev_name,
      ip_addr, 
      business_name,
      sdx_file_sn,
      ext_name);

  if (sn_update != 0)
          sdx_file_sn++;

  return ret;

}



char * dpi_strncasestr_kmp(const char * haystack, int haystack_len, const char * needle)
{
  int len_haystack = haystack_len;
  int len_needle = strlen(needle);

  if (len_needle > len_haystack) {
    return NULL;
  }

  int i = 0, j = -1;
  int next[len_needle];
  next[0] = -1;

  while (i < len_needle - 1) {
    if (j == -1 || tolower(needle[i]) == tolower(needle[j])) {
      ++i;
      ++j;
      next[i] = (tolower(needle[i]) != tolower(needle[j])) ? j : next[j];
    } else {
      j = next[j];
    }
  }

  i = j = 0;
  while (i < len_haystack && j < len_needle) {
    if (j == -1 || tolower(haystack[i]) ==tolower( needle[j])) {
      ++i;
      ++j;
    } else {
      j = next[j];
    }
  }

  if (j == len_needle) {
    return (char *)(haystack + i - j);
  } else {
    return NULL;
  }
}


const char * dpi_strnstr_kmp(const char * haystack, int haystack_len, const char * needle)
{
  int len_haystack = haystack_len;
  int len_needle = strlen(needle);

  if (len_needle > len_haystack) {
    return NULL;
  }

  int i = 0, j = -1;
  int next[len_needle];
  next[0] = -1;

  while (i < len_needle - 1) {
    if (j == -1 || needle[i] == needle[j]) {
      ++i;
      ++j;
      next[i] = (needle[i] != needle[j]) ? j : next[j];
    } else {
      j = next[j];
    }
  }

  i = j = 0;
  while (i < len_haystack && j < len_needle) {
    if (j == -1 || haystack[i] == needle[j]) {
      ++i;
      ++j;
    } else {
      j = next[j];
    }
  }

  if (j == len_needle) {
    return (char *)(haystack + i - j);
  } else {
    return NULL;
  }
}

const char *sse41_strnstr(const char *data, size_t len, const char *target) {
  size_t target_len = strlen(target);
  if (target_len == 0 || len < target_len || target_len > 16)
    return NULL;

  // 预加载首尾字符（双检测策略）
  const char    first_char = target[0];
  const char    last_char = target[target_len - 1];
  const __m128i first_vec = _mm_set1_epi8(first_char);
  const __m128i last_vec = _mm_set1_epi8(last_char);

  // SIMD寄存器复用配置
  const size_t step_size = 32;  // 同时处理两个16字节块
  const size_t safe_end = len - target_len;
  size_t       prefetch_offset = 64;  // 预取提前量

  for (size_t i = 0; i <= safe_end;) {
    // 内存预取优化（提前预取后续数据）
    if (i + prefetch_offset < len) {
      _mm_prefetch(data + i + prefetch_offset, _MM_HINT_T0);
    }

    // SIMD寄存器复用：同时加载两个16字节块
    __m128i chunk1, chunk2;
    if (i + step_size <= len) {
      chunk1 = _mm_loadu_si128((__m128i *)(data + i));
      chunk2 = _mm_loadu_si128((__m128i *)(data + i + 16));
    } else {
      // 处理剩余不足32字节的情况
      char         buffer1[16] = {0}, buffer2[16] = {0};
      const size_t remain = len - i;
      memcpy(buffer1, data + i, remain > 16 ? 16 : remain);
      if (remain > 16) {
        memcpy(buffer2, data + i + 16, remain - 16);
      }
      chunk1 = _mm_loadu_si128((__m128i *)buffer1);
      chunk2 = _mm_loadu_si128((__m128i *)buffer2);
    }

    // 双块并行匹配首字符
    const __m128i cmp1 = _mm_cmpeq_epi8(chunk1, first_vec);
    const __m128i cmp2 = _mm_cmpeq_epi8(chunk2, first_vec);
    int           mask = _mm_movemask_epi8(cmp1) | (_mm_movemask_epi8(cmp2) << 16);

    while (mask) {
      const int    lsb = __builtin_ctz(mask);
      const size_t pos = i + (lsb % 16) + ((lsb >= 16) ? 16 : 0);

      // 边界检查
      if (pos > safe_end) {
        mask &= ~(1 << lsb);
        continue;
      }

      // 双检测策略：验证首尾字符
      if (data[pos + target_len - 1] == last_char) {
        if (memcmp(data + pos, target, target_len) == 0) {
          return data + pos;
        }
      }

      mask &= mask - 1;
    }

    // 动态步进调整
    const size_t next_block = (i % step_size == 0) ? step_size : 16;
    i += (mask == 0) ? next_block : 1;
  }
  return NULL;
}

const char *sse41_strncasestr(const char *haystack, size_t len, const char *needle) {
  const size_t needle_len = strlen(needle);
  if (needle_len == 0)
    return haystack;
  if (needle_len > 16)
    return NULL;

  // 加载并处理needle（转换为小写且清除高位）
  __m128i n = _mm_loadu_si128((const __m128i *)needle);
  n = _mm_and_si128(_mm_or_si128(n, _mm_set1_epi8(0x20)), _mm_set1_epi8(0x7F));

  // 创建正确的比较掩码（前needle_len字节参与比较）
  __m128i mask = _mm_cmplt_epi8(_mm_setr_epi8(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15), _mm_set1_epi8(needle_len));

  // 主搜索循环
  for (size_t i = 0; i + 16 <= len; ++i) {
    __m128i h = _mm_loadu_si128((const __m128i *)(haystack + i));
    h = _mm_and_si128(_mm_or_si128(h, _mm_set1_epi8(0x20)), _mm_set1_epi8(0x7F));

    __m128i cmp = _mm_cmpeq_epi8(h, n);
    int     bits = _mm_movemask_epi8(_mm_and_si128(cmp, mask));

    if (bits == ((1 << needle_len) - 1)) {
      if (i + needle_len > len)
        break;
      if (memcmp(haystack + i, needle, needle_len) == 0)
        return haystack + i;
    }
  }

  // 处理末尾剩余部分
  if (len >= needle_len) {
    const char *end = haystack + len - needle_len;
    for (const char *p = haystack; p <= end; ++p) {
      if (strncasecmp(p, needle, needle_len) == 0)
        return p;
    }
  }
  return NULL;
}

const char *simd_strnstr_avx2(const char *data, size_t len, const char *target) {
  size_t target_len = strlen(target);
  if (target_len == 0 || len < target_len || target_len > 32)
    return NULL;

  // 仅加载目标字符串的第一个字符用于 SIMD 扫描
  char    first_char = target[0];
  __m256i first_char_vec = _mm256_set1_epi8(first_char);

  for (size_t i = 0; i <= len - target_len; ++i) {
    // 加载 32 字节数据块（边界安全）
    __m256i chunk;
    if (i + 32 <= len) {
      chunk = _mm256_loadu_si256((__m256i *)(data + i));
    } else {
      char buffer[32] = {0};
      memcpy(buffer, data + i, len - i);
      chunk = _mm256_loadu_si256((__m256i *)buffer);
    }

    // 查找首字符匹配位置
    __m256i cmp = _mm256_cmpeq_epi8(chunk, first_char_vec);
    int     mask = _mm256_movemask_epi8(cmp);

    while (mask) {
      int    pos = __builtin_ctz(mask);
      size_t candidate = i + pos;

      // 验证后续字符
      if (candidate + target_len <= len && memcmp(data + candidate, target, target_len) == 0) {
        return data + candidate;
      }

      mask &= mask - 1;  // 清除最低有效位
    }
  }
  return NULL;
}

const char *simd_strncasestr_avx2(const char *haystack, size_t len, const char *needle) {
  const size_t needle_len = strlen(needle);
  if (needle_len == 0)
    return haystack;
  if (needle_len > 32)
    return NULL;  // AVX2寄存器最大处理32字节

  // 加载needle并转换为小写，同时清除非ASCII高位
  __m256i n = _mm256_loadu_si256((const __m256i *)needle);
  n = _mm256_and_si256(_mm256_or_si256(n, _mm256_set1_epi8(0x20)), _mm256_set1_epi8(0x7F));

  // 创建有效位掩码（前needle_len字节参与比较）
  const __m256i mask =
      _mm256_cmpgt_epi8(_mm256_set1_epi8(needle_len), _mm256_setr_epi8(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,
                                                          17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31));

  // 主搜索循环（每次处理32字节块）
  for (size_t i = 0; i + 32 <= len; ++i) {
    __m256i h = _mm256_loadu_si256((const __m256i *)(haystack + i));
    h = _mm256_and_si256(_mm256_or_si256(h, _mm256_set1_epi8(0x20)), _mm256_set1_epi8(0x7F));

    // 应用掩码进行比较
    __m256i cmp = _mm256_and_si256(_mm256_cmpeq_epi8(h, n), mask);
    int     bits = _mm256_movemask_epi8(cmp);

    // 检查完整匹配
    if (bits == ((1 << needle_len) - 1)) {
      // 验证实际内容匹配
      if (i + needle_len > len)
        break;
      if (strncasecmp(haystack + i, needle, needle_len) == 0)
        return haystack + i;
    }
  }

  // 处理剩余部分（非SIMD方式）
  const size_t remaining = len % 32;
  const char  *end = haystack + len - needle_len;
  for (const char *p = haystack + len - remaining; p <= end; ++p) {
    if (strncasecmp(p, needle, needle_len) == 0)
      return p;
  }

  return NULL;
}

// 平台特征检测宏
#if defined(__x86_64__) || defined(_M_X64) || defined(__i386__) || defined(_M_IX86)
    #define X86_ARCH 1
#else
    #define X86_ARCH 0
#endif
// 编译器指令集支持检测
#if defined(__AVX2__) || (defined(_MSC_VER) && defined(__AVX2__))
    #define COMPILER_AVX2 1
#else
    #define COMPILER_AVX2 0
#endif

#if defined(__SSE4_1__) || (defined(_MSC_VER) && defined(__SSE4_1__))
    #define COMPILER_SSE41 1
#else
    #define COMPILER_SSE41 0
#endif

struct CpuFeatures {
    int avx2_supported;
    int sse41_supported;
};

// 修正后的 CPU 特征初始化函数
static inline void init_cpu_features(struct CpuFeatures* features) {
    features->avx2_supported = 0;
    features->sse41_supported = 0;

#if defined(__x86_64__) || defined(__i386__) || defined(_M_IX86) || defined(_M_X64)
    unsigned int regs[4] = {0};

    // 检测 SSE4.1
    #if defined(__GNUC__)
    __cpuid_count(1, 0, regs[0], regs[1], regs[2], regs[3]);
    #elif defined(_MSC_VER)
    __cpuid(regs, 1);
    #endif
    features->sse41_supported = (regs[2] & (1 << 19)) ? 1 : 0;

    // 检测 AVX2
    #if defined(__GNUC__)
    __cpuid_count(7, 0, regs[0], regs[1], regs[2], regs[3]);
    #elif defined(_MSC_VER)
    __cpuidex(regs, 7, 0);
    #endif
    features->avx2_supported = (regs[1] & (1 << 5)) ? 1 : 0;
#endif
}
// 特征缓存实现
static struct CpuFeatures g_cpu_features = {0};
static int g_initialized = 0;

static inline struct CpuFeatures get_cpu_features() {
    if (!g_initialized) {
        init_cpu_features(&g_cpu_features);
        g_initialized = 1;
    }
    return g_cpu_features;
}

// 算法选择
const char* dpi_hybrid_strnstr(const char* data, size_t len, const char* target) {
    const size_t target_len = strlen(target);
    const struct CpuFeatures features = get_cpu_features();

    // 基础校验
    if (target_len == 0) return data;
    if (len < target_len) return NULL;

    // 根据长度和硬件支持选择算法
    if (target_len <= 16) {
#if COMPILER_SSE41 && X86_ARCH
        if (features.sse41_supported) {
            return sse41_strnstr(data, len, target);
        }
#endif
        // 回退到 KMP
        return dpi_strnstr_kmp(data, len, target);
    }
    else if (target_len <= 64) {
#if COMPILER_AVX2 && X86_ARCH
        if (features.avx2_supported) {
            return simd_strnstr_avx2(data, len, target);
        }
#endif
        // 回退到 SSE4.1 或 KMP
#if COMPILER_SSE41 && X86_ARCH
        if (features.sse41_supported) {
            return sse41_strnstr(data, len, target);
        }
#endif
        return dpi_strnstr_kmp(data, len, target);
    }
    else {
        return dpi_strnstr_kmp(data, len, target);
    }
}

const char *dpi_hybrid_strncasestr(const char *data, size_t len, const char *target) {
    const size_t             target_len = strlen(target);
    const struct CpuFeatures features = get_cpu_features();

    if (target_len == 0)
        return data;
    if (len < target_len)
        return NULL;

    // 添加大小写不敏感标识
    const uint8_t case_insensitive = 1 /* 根据实际情况判断 */;

    if (target_len <= 16) {
#if COMPILER_SSE41 && X86_ARCH
        if (features.sse41_supported && case_insensitive) {
            return sse41_strncasestr(data, len, target);
        }
#endif
        return dpi_strncasestr_kmp(data, len, target);
    } else if (target_len <= 64) {
#if COMPILER_AVX2 && X86_ARCH
        if (features.avx2_supported && case_insensitive) {
            return simd_strncasestr_avx2(data, len, target);
        }
#endif
#if COMPILER_SSE41 && X86_ARCH
        if (features.sse41_supported && case_insensitive) {
            return sse41_strncasestr(data, len, target);
        }
#endif
        return dpi_strncasestr_kmp(data, len, target);
    } else {
        return dpi_strncasestr_kmp(data, len, target);
    }
}

char *my_strrstr(const char *str1, const char *str2)
{
    if(str1==NULL || str2==NULL){
        return NULL;
    }

    char *last=NULL;
    char *current=NULL;

    current=strstr(str1,str2);
    while(current!=NULL){
        last=current;
        current=strstr(current+1,str2);
    }
    return last;
}


static                          /* add by zhengsw : static 兼容g++ */
inline int dpi_ishex(int x)
{
    return  (x >= '0' && x <= '9')  || (x >= 'a' && x <= 'f')  || (x >= 'A' && x <= 'F');
}

/*****************************************************************
*Function    :YV_UrlDecode
*Description :URLcode编码转换, 三个字节转换为1个字节
*Input       :URLcode编码
*Output      :URLdecode编码
*Return      :转换成功字节数
*Others      :none
*****************************************************************/
int  dpi_url_decode(const char *src, int inputLen, char *dec, int BufferSize)
{
    char *pdec = NULL;
    const char *srcEnd = src + inputLen;
    int a = 0;
    int b = 0;
    int c = 0;
    unsigned char value = 0;

    if(NULL == src || NULL == dec || inputLen < 0 || BufferSize < 0)
    {
        return -1;
    }


    int  counter = 0; //转换成功计数器
    for (pdec = dec; src < srcEnd; pdec++)
    {
        if(pdec - dec > BufferSize)
        {
            return pdec - dec;
        }
        a = *(src + 0);
        b = *(src + 1);
        c = *(src + 2);

        if (a == '%' && dpi_ishex(b) && dpi_ishex(c))
        {
            if(b >= 'a' && b <= 'f')
            {
                value = b - 'a'+ 0x0a;
            }
            else if (b >= 'A' && b <= 'F')
            {
                value = b - 'A'+ 0x0a;
            }
            else
            {
                value = b - '0' + 0x30;
            }

            value = value << 4;

            if(c >= 'a' && c <= 'f')
            {
                value |= (0x0F & (c - 'a' + 0x0a));
            }
            else if (c >= 'A' && c <= 'F')
            {
                value |= (0x0F & (c - 'A' + 0x0a));
            }
            else
            {
                value |= (0x0F & (c - '0'+ 0x30));
            }

            *pdec = value;
            src+=3;
        }
        else
        {
            *pdec = *src;
            src++;
        }
        counter++; // 转换成功计数器累计
    }
    return  counter;
}
