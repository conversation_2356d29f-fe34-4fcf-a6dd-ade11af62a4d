#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/syscall.h>
#include <sys/time.h>
#include <string.h>
#include <arpa/inet.h>
#include <errno.h>
#include <stdlib.h>
#include <assert.h>
#include <fcntl.h>
#include <string.h>
#include <time.h>
#include <endian.h>
#include <pcap/pcap.h>

#include "dpi_dpdk_wrapper.h"

#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "sdt_action_out.h"
#include "post.h"
#include "dpi_forward_eth.h"
#include "dpi_forward_kafka.h"
#include "dpi_utils.h"
#include "sdx/LineConvert.h"

#include "cJSON.h"
extern int cloneIndex, freeIndex;
extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;;
extern struct tbl_log_file tbl_log_array[TBL_LOG_MAX];
extern struct rte_mempool *pktmbuf_pool[2];
struct rte_hash *g_sdt_hash_db;
struct rte_ring *sdt_out_ring[SDT_MAX_OUT_RING];

extern rte_atomic64_t sdt_pcap_out_forward_pkts;
extern rte_atomic64_t sdt_pcap_out_forward_ok_pkts;
extern rte_atomic64_t sdt_pcap_out_forward_fail_pkts;
extern rte_atomic64_t sdt_pcap_success_pkts;
extern rte_atomic64_t sdt_pcap_do_pkt_dump;
extern rte_atomic64_t sdt_event_success_pkts;
extern rte_atomic64_t sdt_pcap_fail_pkts;
extern rte_atomic64_t sdt_event_fail_pkts;
extern rte_atomic64_t sdt_pcap_out_pkts;
extern rte_atomic64_t sdt_syslog_fail_pkts;
extern rte_atomic64_t sdt_syslog_success_pkts;

int g_sdt_hash_db_clear_flag = 0;

static int sdt_out_thfunc_cnt = 0;
static int sdt_out_thfunc_signal = 0;

struct mac_forward_cnt_t mac_forward_cnt[20]; //默认最大10个，Task最大10个

struct rte_mempool *pktstream_pool;


pid_t gettid(void);
pid_t gettid(void)
{
    return syscall(SYS_gettid);
}

int sdt_init_rules_to_hash_db_hit(int action, SdtMatchResult *result, sdt_out_status *status)
{
    switch(action)
    {
        case SAE_packetDump:
            if(result->packetDump_args.minute>0){
                status->pcap_status.max_time       = result->packetDump_args.minute;
            }else{
                status->pcap_status.max_time       = g_config.web_config.pcap_truncation;
            }

            if(result->packetDump_args.size>0){
                status->pcap_status.max_pcap_size  = result->packetDump_args.size;
            }else{
                status->pcap_status.max_pcap_size  = g_config.web_config.pcap_size;
            }
            break;
        default:
            break;
    }
    return 0;
}

#if 1
int
sdt_init_rules_to_hash_db(SdtMatchResult *rules_result, sdt_out_status  *rule_elem)
{
    if(!rules_result){
        return -1;
    }

    rule_elem->match_result = rules_result;
    int ret = PKT_DROP;
    for(int idx = 0; idx < CHAR_BIT *(int)sizeof(rules_result->action); idx++)
    {
        uint32_t action = 1 << idx;
        if(rules_result->action & action)
        {
            ret = sdt_init_rules_to_hash_db_hit(action, rules_result, rule_elem);
            if(PKT_DROP == ret)
            {
                return PKT_DROP;
            }
        }
    }

    rule_elem->event_status.max_event_num = g_config.sdt_out_event_max_line;
    rule_elem->event_status.max_time      = g_config.write_tbl_maxtime;

    return 0;
}

int
sdt_rule_hash_db_insert(SdtMatchResult *rules_result)
{
    char rule_key[SDT_RULE_KEY_SIZE]={0};
    sdt_out_status  *lookup_result=NULL;
    snprintf(rule_key, SDT_RULE_KEY_SIZE, "%s_%s_%s_%d",
            rules_result->unitID,
            rules_result->taskID,
            rules_result->groupID,
            rules_result->ruleID);
    int pos = rte_hash_lookup_data(g_sdt_hash_db,(const void *)rule_key, (void *)&lookup_result);
    if(pos>=0){
        return 0;
    }

    sdt_out_status  *rule_elem=NULL;
    rule_elem=(sdt_out_status  *)malloc(sizeof(sdt_out_status));
    if(!rule_elem){
        DPI_LOG(DPI_LOG_ERROR, "rule_hash_code:%s insert hash db failed, malloc failed!", rule_key);
        return -1;
    }
    memset(rule_elem, 0, sizeof(sdt_out_status));
    sdt_init_rules_to_hash_db(rules_result, rule_elem);

    if(!rule_elem->match_result){
        return -1;
    }

    int retval = rte_hash_add_key_data(g_sdt_hash_db, (const void *)rule_key, (void *)rule_elem);
    if(retval<0){
        DPI_LOG(DPI_LOG_ERROR, "rte_hash_add_key_data %s insert hash db failed! hash table numbers:%d",
                                                           rule_key,
                                                           rte_hash_count(g_sdt_hash_db));
        free(rule_elem);
        rule_elem = NULL;
    }

    return 0;
}

static int
sdt_rule_hash_db_clean_(void)
{
    sdt_out_status *next_hop;
    uint32_t       *rule_key;
    uint32_t       iter = 0;
    int            retval=0;

    while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key,
            (void *) &next_hop, &iter) >= 0)
    {
        retval = rte_hash_del_key(g_sdt_hash_db, (const void *)rule_key);
        if (retval < 0){
            continue;
        }
        if(next_hop){
            free(next_hop);
            next_hop=NULL;
        }
    }

    return 0;
}

int
sdt_rule_hash_db_clean(void)
{
    //通知管理线程去 clean
    g_sdt_hash_db_clear_flag = 1;
    printf("wait g_sdt_hash_db_clear ...\n");

    //自旋等待 -- 全部就绪

    log_debug("等待 sdt_match 线程的信号...");
    while (sdt_match_thfunc_signal > 0)
    {
        usleep(1000*100);
    }
    log_debug("sdt_match 线程已全部暂停!");

    log_debug("等待 tbl_out 线程的信号...");
    while (tbl_out_thfunc_signal > 0)
    {
        usleep(1000*100);
    }
    log_debug("tbl_out 线程已全部暂停!");

    log_debug("等待 sdt_out 线程的信号...");
    while(sdt_out_thfunc_signal != sdt_out_thfunc_cnt)
    {
        usleep(1000*100);
    }
    log_debug("sdt_out 线程已全部暂停!");

    //开始干活
    sdt_clean_rule_data_status();
    sdt_rule_hash_db_clean_();

    g_sdt_hash_db_clear_flag = 0;
    printf("wait g_sdt_hash_db_clear OK\n");

    return 0;
}


sdt_out_status *
sdt_rule_hash_db_lookup(SdtMatchResult *match_result)
{
    if(!match_result){
        return NULL;
    }

    sdt_out_status  *lookup_result=NULL;
    char rule_key[SDT_RULE_KEY_SIZE]={0};
    snprintf(rule_key, SDT_RULE_KEY_SIZE, "%s_%s_%s_%d",
            match_result->unitID,
            match_result->taskID,
            match_result->groupID,
            match_result->ruleID);
    int pos = rte_hash_lookup_data( g_sdt_hash_db,
                                    (const void *)rule_key,
                                    (void **)&lookup_result);
    if(pos>=0){
        return lookup_result;
    }


    sdt_out_status  *rule_elem=NULL;
    rule_elem=(sdt_out_status  *)malloc(sizeof(sdt_out_status));
    if(!rule_elem){
        DPI_LOG(DPI_LOG_ERROR, "rte_hash_add_key_data :%s malloc failed!",
                                                          rule_key);
        return NULL;
    }
    memset(rule_elem, 0, sizeof(sdt_out_status));
    sdt_init_rules_to_hash_db(match_result, rule_elem);

    int retval = rte_hash_add_key_data(g_sdt_hash_db, (const void *)rule_key, (void *)rule_elem);
    if(retval<0){
        DPI_LOG(DPI_LOG_ERROR, "rte_hash_add_key_data: %s insert hash db failed! hash table numbers:%d",
                                                           rule_key,
                                                           rte_hash_count(g_sdt_hash_db));
        free(rule_elem);
        return NULL;
    }

    return rule_elem;
}

sdt_out_status *
sdt_rule_hash_lookup_key(const char *uintid, const char *taskid, const char *groupid, uint32_t rule_id)
{
    sdt_out_status  *lookup_result=NULL;
    char rule_key[SDT_RULE_KEY_SIZE]={0};
    snprintf(rule_key, SDT_RULE_KEY_SIZE, "%s_%s_%s_%d", uintid, taskid, groupid, rule_id);
    int pos = rte_hash_lookup_data( g_sdt_hash_db, (const void *)rule_key, (void **)&lookup_result);
    if(pos>=0){
        return lookup_result;
    }
    return NULL;
}
#endif


static int SdxRecIPRule_init(SdxRecIPRule *rec)
{
    FILE    *fp;
    int     len = 0;
    char    *file_name = rec->filename_writing;

    len = snprintf(file_name, FILENAME_MAX,
            "/tmp/tbls/sdt/pcap/"
            /*, g_config.tbl_out_dir, time_to_date(g_config.g_now_time)*/);

    if (access(file_name, F_OK))
        mkdirs(file_name);

    if (g_config.show_task_id)
    {
        len += snprintf(file_name + len, FILENAME_MAX - len,
                    "/%s-%s-pcap-%u-%s.json",
                    g_config.sdt_out_produce_data_dev_name,
                    g_config.sdx_config.sdx_ip_str,
                    dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL),
                    g_config.task_id);
    }
    else
    {
        len += snprintf(file_name + len, FILENAME_MAX - len,
                    "/%s-%s-pcap-%u.json",
                    g_config.sdt_out_produce_data_dev_name,
                    g_config.sdx_config.sdx_ip_str,
                    dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL));
    }

    strncpy(rec->filename_finished, file_name, len);
    strcpy(file_name + len, ".tmp");

    if ((fp = fopen(file_name, "w")) == NULL)
    {
        DPI_LOG(DPI_LOG_ERROR, "Error open: %s, %s", file_name, strerror(errno));
        return -1;
    }

    rec->fp          = fp;
    rec->create_time = g_config.g_now_time;
    rec->write_bytes = 0;

    return 0;
}


static void SdxRecIPRule_uninit(SdxRecIPRule *rec)
{
    if (!rec || !rec->fp)
        return;

    fclose(rec->fp);

    //空文件删除
    if(0 == rec->write_bytes) {
        remove(rec->filename_writing);
    } else {
        if (rename(rec->filename_writing, rec->filename_finished) == -1) {
            DPI_LOG(DPI_LOG_ERROR, "Error rename: %s, \"%s\"", rec->filename_writing, strerror(errno));
        }
    }

    rec->fp = NULL;
    rec->create_time = 0;
    rec->write_bytes = 0;
    memset(rec->filename_writing, 0, FILENAME_MAX);
    memset(rec->filename_finished, 0, FILENAME_MAX);
}


static int SdxRecIPRule_write_one_record(SdxRecIPRule *rec, sdt_out_status  *out_elem)
{
    struct stu_rule_pcap   *pcap_status = &out_elem->pcap_status;
    SdtMatchResult         *match_result = out_elem->match_result;

    if (match_result == NULL || rec == NULL)
    {
        return -1;
    }

    if (rec->fp == NULL)
    {
        // 写时创建, 避免产生空文件
        if (SdxRecIPRule_init(rec) != 0)
            return -1;
    }

    char    *buff = (char*)dpi_malloc(MAX_CONTENT_SIZE);
    memset(buff, 0, MAX_CONTENT_SIZE);

    /** <SDX-测试大纲> B.6 要求字段 */
    cJSON   *elem = cJSON_CreateObject();
    cJSON_AddNumberToObject(elem, "DBEGINTIME",     pcap_status->pcap_time);
    cJSON_AddStringToObject(elem, "NGROUPNO",       match_result->groupID);
    cJSON_AddStringToObject(elem, "SGROUPNAME",     match_result->groupName);

    uint32_t HW[4];
    sdx_convert_linename(out_elem->first_mac_hdr.Datasrc.Global_LineNO, buff, HW);
    cJSON_AddNumberToObject(elem, "LINENO1",        HW[0]);
    cJSON_AddNumberToObject(elem, "LINENO2",        HW[1]);
    cJSON_AddNumberToObject(elem, "LINENO3",        HW[2]);
    cJSON_AddNumberToObject(elem, "LINENO4",        HW[3]);
    cJSON_AddStringToObject(elem, "sLineName",      buff);
    cJSON_AddStringToObject(elem, "INFO_TYPE",      "");                   //2024-11-15 思文:牡丹江要求加入
    cJSON_AddStringToObject(elem, "TASK_ID",        match_result->taskID); //2024-11-15 思文:牡丹江要求加入
    cJSON_AddNumberToObject(elem, "RULE_ID",        match_result->ruleID);
    cJSON_AddStringToObject(elem, "SSYSFROM",       g_config.web_config.sys_from);
    cJSON_AddNumberToObject(elem, "NFILELENGTH",    pcap_status->pkt_bytes);

    snprintf(buff, MAX_CONTENT_SIZE, "%s.pcap", pcap_status->pcap_name);
    cJSON_AddStringToObject(elem, "SFILEPATH",      buff);
    cJSON_AddStringToObject(elem, "DATAFROM",       g_config.web_config.data_from);
    cJSON_AddStringToObject(elem, "SIGTYPE",        g_config.web_config.sig_type);
    cJSON_AddStringToObject(elem, "reserved",       "");

    int write_len = 0;
    int buff_len = 0;
    if (cJSON_PrintPreallocated(elem, buff, MAX_CONTENT_SIZE -5, 0) > 0)
    {
        buff_len = strlen(buff);
        write_len += fwrite(buff, 1, buff_len, rec->fp);
        write_len += fwrite("\n", 1, 1, rec->fp);
    }
    else
    {
        DPI_LOG(DPI_LOG_WARNING, "error cjson print");
    }

    rec->write_bytes += write_len;
    cJSON_Delete(elem);
    dpi_free(buff);

    //磁盘已满?
    if(write_len != (buff_len + 1))
    {
        DPI_LOG(DPI_LOG_ERROR, "文件无法写入 %s", rec->filename_writing);
    }

    return 0;
}


static void SdxRecIPRule_rotate(SdxRecIPRule *rec)
{
    if (!rec || !rec->fp)
        return;

    uint32_t json_max_size = g_config.web_config.json_size ? g_config.web_config.json_size : 1 * 1024 * 1024;     // 默认 1M
    uint32_t json_max_time = g_config.web_config.json_truncation ? g_config.web_config.json_truncation : 60 * 60; // 默认1小时

    if (rec->write_bytes >= json_max_size
      || (g_config.g_now_time - rec->create_time) >= json_max_time)
    {
        SdxRecIPRule_uninit(rec);
    }
}




static int
sdt_out_init(void)
{
    int i;
    struct rte_hash_parameters sdt_rules_hash_para = {
        .name      = "sdt_rules_status",
        .entries   = g_config.sdt_rule_max_num,
        .key_len   = SDT_RULE_KEY_SIZE,
        .hash_func = rte_jhash,
        .hash_func_init_val = 0,
        .socket_id = g_config.socketid,
    };

    g_sdt_hash_db = rte_hash_create(&sdt_rules_hash_para);
    if (g_sdt_hash_db == NULL){
        DPI_LOG(DPI_LOG_ERROR, "create g_sdt_hash_db failed!");
        return -1;
    }

    {
        int pool_size = g_config.sdt_out_thead_num * g_config.sdt_out_ring_size;
        char name[64] = {0};
        snprintf(name, sizeof(name), "pktstream_pool_%d", g_config.socketid);

        pktstream_pool = rte_mempool_create(name, pool_size, sizeof(struct packet_stream),
                        RTE_MEMPOOL_CACHE_MAX_SIZE, 0,
                        NULL, NULL,
                        NULL, NULL,
                        g_config.socketid, 0);

        if (pktstream_pool == NULL) {
            DPI_LOG(DPI_LOG_ERROR, "Cannot init packet out pool on socket %d", g_config.socketid);
            exit(-1);
        } else {
            DPI_LOG(DPI_LOG_DEBUG, "Allocated packet out pool on socket %d", g_config.socketid);
        }
    }

    for (i = 0; i < g_config.sdt_out_thead_num; i++) {
        char ring_name[64] = {0};
        snprintf(ring_name, sizeof(ring_name), "sdtout_ring_%d_%d",
                                                g_config.socketid,
                                                i);
        sdt_out_ring[i] = rte_ring_create(ring_name,
                                          g_config.sdt_out_ring_size,
                                          g_config.socketid,
                                          RING_F_SC_DEQ);
        if (sdt_out_ring[i] == NULL) {
            DPI_LOG(DPI_LOG_ERROR, "error while create tbl ring\n");
            return -1;
        }
        if (rte_ring_lookup(ring_name) != sdt_out_ring[i]) {
            DPI_LOG(DPI_LOG_ERROR, "SDT OUT Cannot lookup ring from its name[%s]\n",
                                                                         ring_name);
            return -1;
        }
    }

    return 0;
}


static int
sdt_out_event_fragment(sdt_out_status           *out_elem)
{
    if(NULL==out_elem->event_status.event_fp){
        return 0;
    }

    fclose(out_elem->event_status.event_fp);
    out_elem->event_status.event_fp=NULL;

    char fininsh_name[COMMON_FILE_PATH]={0};
    strncpy(fininsh_name,
            out_elem->event_status.event_name,
            strlen(out_elem->event_status.event_name)-strlen(".tmp"));

    rename(out_elem->event_status.event_name, fininsh_name);
    memset(out_elem->event_status.event_name,0,sizeof(out_elem->event_status.event_name));

    return 1;
}


static int
sdt_out_syslog_fragment(sdt_out_status           *out_elem)
{
    if(NULL==out_elem->syslog_status.syslog_fp){
        return 0;
    }

    fclose(out_elem->syslog_status.syslog_fp);
    out_elem->syslog_status.syslog_fp=NULL;

    char finish_name[COMMON_FILE_PATH]={0};
    strncpy(finish_name,
            out_elem->syslog_status.syslog_name,
            strlen(out_elem->syslog_status.syslog_name)-strlen(".tmp"));

    rename(out_elem->syslog_status.syslog_name, finish_name);
    memset(out_elem->syslog_status.syslog_name,0,sizeof(out_elem->syslog_status.syslog_name));

    return 1;
}


static int
sdt_out_pcap_info_fragment(sdt_out_status  *out_elem)
{

    if(NULL==out_elem->pcap_status.pcap_log_fp){
        return 0;
    }

    fclose(out_elem->pcap_status.pcap_log_fp);
    out_elem->pcap_status.pcap_log_fp=NULL;

    char writing_name[COMMON_FILE_PATH]={0};
    char fininsh_name[COMMON_FILE_PATH]={0};

    snprintf(writing_name, COMMON_FILE_PATH,"%s.info.tmp",out_elem->pcap_status.pcap_name);
    snprintf(fininsh_name, COMMON_FILE_PATH,"%s.info",out_elem->pcap_status.pcap_name);

    rename(writing_name, fininsh_name);

    return 1;
}


static int
sdt_out_pcap_fragment(SdxOutThreadCtx *sdx_out_ctx, sdt_out_status  *out_elem)
{

    if(NULL==out_elem->pcap_status.pcap_fp){
        return 0;
    }

    fclose(out_elem->pcap_status.pcap_fp);
    out_elem->pcap_status.pcap_fp=NULL;

    char fininsh_name[COMMON_FILE_PATH]={0};
    char writing_name[COMMON_FILE_PATH]={0};
    snprintf(writing_name, COMMON_FILE_PATH,"%s.pcap.tmp",out_elem->pcap_status.pcap_name);
    snprintf(fininsh_name, COMMON_FILE_PATH,"%s.pcap",out_elem->pcap_status.pcap_name);

    rename(writing_name, fininsh_name);

    // sdt_out_pcap_info_fragment(out_elem);
    SdxRecIPRule_write_one_record(&sdx_out_ctx->sdx_rec_rule, out_elem);

    memset(out_elem->pcap_status.pcap_name,0,sizeof(out_elem->pcap_status.pcap_name));

    return 1;
}

int create_pcap_file(sdt_out_status  *out_elem, int thread_index)
{
    /* --目录规则  /单位/任务号/规则组名称/日期（天）/线路名称_日期（秒）_IP地址_线程号_序号.pcap  
        例如：/57302/CC01#CMCX###202411070027/14094323_bjk-FTP-202411/20241125/[JXC-01]YT-3-119-W-R-U2#4F$3@1-64VC4C_20241125221317_109.8.150.110_1_018.pcap
    */
    char pcap_rule_dir[COMMON_FILE_PATH]={0};
    char pcap_tmp_name[COMMON_FILE_PATH]={0};
    snprintf(pcap_rule_dir,COMMON_FILE_PATH,"%s/%s/%s/%s_%s/%.8s",
                                  g_config.sdt_out_pcap_dir,
                                  out_elem->match_result->unitID,
                                  out_elem->match_result->taskID,
                                  out_elem->match_result->groupID,
                                  out_elem->match_result->groupName,
                                  time_to_datetime(g_config.g_now_time));
    mkdirs(pcap_rule_dir);

    // 设置文件名
    if(1==g_config.sdx_config.sdx_mac_packet_header_flag){
        uint32_t HW[4] = {0,0,0,0};
        char                      buff_LINENAME1[512] = {0};
        sdx_convert_linename(out_elem->first_mac_hdr.Datasrc.Global_LineNO, buff_LINENAME1, HW);
        snprintf(out_elem->pcap_status.pcap_name, COMMON_FILE_PATH,
                                      "%s/%s-%s-%s-%d-%03u",
                                      pcap_rule_dir,
                                      buff_LINENAME1,
                                      time_to_datetime(g_config.g_now_time),
                                      g_config.sdx_config.sdx_ip_str,
                                      thread_index+1,
                                      dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL));
    } else {
        snprintf(out_elem->pcap_status.pcap_name, COMMON_FILE_PATH,
                                      "%s/%s_%s_%s_%u_%u",
                                      pcap_rule_dir,
                                      g_config.sdt_pcap_identification,
                                      time_to_datetime(g_config.g_now_time),
                                      g_config.sdx_config.sdx_ip_str,
                                      thread_index+1, //标书中要求 默认从1 开始
                                      dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL));
    
    }

    snprintf(pcap_tmp_name, COMMON_FILE_PATH, "%s.pcap.tmp",
                                  out_elem->pcap_status.pcap_name);

    // printf("sdt_pcap name:%s timeout:%u\n", pcap_tmp_name, out_elem->pcap_status.max_time);

    // 创建文件
    out_elem->pcap_status.pcap_fp = fopen(pcap_tmp_name, "w");
    if(NULL==out_elem->pcap_status.pcap_fp){
        DPI_LOG(DPI_LOG_ERROR, "创建 [%s] 失败", pcap_tmp_name);
        perror(pcap_tmp_name);
        return -1;
    }

    // 生成pcap文件的头部
    struct pcap_file_header fh;
    memset(&fh, 0, sizeof(struct pcap_file_header));
    fh.magic         = 0xA1B2C3D4;
    fh.version_major = PCAP_VERSION_MAJOR;
    fh.version_minor = PCAP_VERSION_MINOR;
    fh.snaplen       = 0XFFFFFFFF;
    fh.linktype      = DLT_EN10MB;

    /* 写入pcap头部 */
    fwrite(&fh, sizeof(struct pcap_file_header), 1, out_elem->pcap_status.pcap_fp);
    out_elem->pcap_status.pcap_time=(uint32_t)g_config.g_now_time;
    out_elem->pcap_status.pkt_bytes=0;
    out_elem->pcap_status.pkt_bytes += sizeof(struct pcap_file_header);
    return 0;
}

static int
sdt_out_pcap(SdxOutThreadCtx *sdx_out_ctx, sdt_out_status  *out_elem, struct packet_stream  *pkt_elm)
{
    if(NULL==out_elem->pcap_status.pcap_fp) /* pcap 文件还没创建，创建pcap文件并写入头*/
    {
        if(1==g_config.sdx_config.sdx_mac_packet_header_flag)
        {
            // 取第一帧mac header 传递给 pcap.json, 输出线路号
            memcpy(&out_elem->first_mac_hdr, pkt_elm->pkt_data, sizeof(struct mac_packet_header));
        }
        create_pcap_file(out_elem, pkt_elm->work_on_ringId);
    }

    if(NULL==out_elem->pcap_status.pcap_fp)
    {
        DPI_LOG(DPI_LOG_ERROR, "PCAP创建失败");
        return 0;
    }

    int ret=0;
    struct pcappkt_hdr    pckt_header;

    int offset = 0;
    if(1==g_config.sdx_config.sdx_mac_packet_header_flag && 0==g_config.sdx_config.dump_pcap_with_sdx_mac)
    {
        offset = 51;
    }

    uint32_t caplen     =  DPI_MIN(pkt_elm->pkt_data_len, TCP_PAYLOAD_MAX_LEN);
    pckt_header.caplen  =  caplen - offset;
    pckt_header.len     =  pkt_elm->pkt_data_len - offset;
    pckt_header.tv_sec  = (uint32_t)g_config.g_now_time;
    pckt_header.tv_usec = (uint32_t)(g_config.g_now_time_usec-g_config.g_now_time* 1000000);

    /* 写每个报文的的头 */
    /*写入报文帧*/
    ret = fwrite(&pckt_header, 1, sizeof(struct pcappkt_hdr), out_elem->pcap_status.pcap_fp);
    ret+= fwrite(pkt_elm->pkt_data+offset, 1, caplen-offset, out_elem->pcap_status.pcap_fp);

    //写入量判断
    if(ret == (typeof(ret))(sizeof(struct pcappkt_hdr) + caplen - offset))
    {
        uint32_t per_pkt_len = sizeof(struct pcappkt_hdr) + caplen - offset;
        out_elem->pcap_status.pkt_bytes += per_pkt_len;
        fflush(out_elem->pcap_status.pcap_fp); //及时输出
    }else{
        log_warn("报文帧无法写入");
        return -1;
    }

    /* 大小和时间互斥，1.有大小没时间，2.没大小有时间*/
    if(out_elem->pcap_status.max_pcap_size>0){/* 配置每个pcap大小，达到该大小则分包 */
        if(out_elem->pcap_status.pkt_bytes>=out_elem->pcap_status.max_pcap_size){
            char pcap_tmp_filename[COMMON_FILE_PATH]={0};
            snprintf(pcap_tmp_filename, COMMON_FILE_PATH, "%s.pcap", out_elem->pcap_status.pcap_name);
            sdt_out_pcap_fragment(sdx_out_ctx, out_elem);
            //发送“数据采集文件”消息到kafka
            forward_collect_send_kafka(out_elem, pkt_elm, pcap_tmp_filename);
            out_elem->pcap_status.pkt_bytes=0;
        }
    }else if( out_elem->pcap_status.max_time>0 &&   /* 配置了每个pcap写时间，则达到该时间分包*/
              out_elem->pcap_status.pcap_time+out_elem->pcap_status.max_time<g_config.g_now_time)
    {
         char pcap_tmp_filename[COMMON_FILE_PATH]={0};
         snprintf(pcap_tmp_filename, COMMON_FILE_PATH, "%s.pcap", out_elem->pcap_status.pcap_name);
         sdt_out_pcap_fragment(sdx_out_ctx, out_elem);
         //发送“数据采集文件”消息到kafka
         forward_collect_send_kafka(out_elem, pkt_elm, pcap_tmp_filename);
         out_elem->pcap_status.pcap_time=(uint32_t)g_config.g_now_time;
		 //初始化文件大小
         out_elem->pcap_status.pkt_bytes=0;
    }

    return 0;
}

static int
sdt_out_event(sdt_out_status  *out_elem, struct packet_stream  *pkt_elem)
{
    int ret=0;

    if(NULL==out_elem->event_status.event_fp){

        char event_rule_dir[COMMON_FILE_PATH]={0};
        snprintf(event_rule_dir,COMMON_FILE_PATH,"%s/%s/%s_%s/event/%.8s",
                                        g_config.sdt_out_event_dir,
                                        out_elem->match_result->taskID,
                                        out_elem->match_result->groupID,
                                        out_elem->match_result->groupName,
                                        time_to_datetime(g_config.g_now_time));
        mkdirs(event_rule_dir);
        // 设置event文件名
        snprintf(out_elem->event_status.event_name, COMMON_FILE_PATH,
                                      "%s/%s_%u_%s_%s_%u_%u.%s.tmp",
                                      event_rule_dir,
                                      g_config.sdt_out_produce_data_dev_name,
                                      pkt_elem->rule_id,        //把 ruleID 带在文件名上
                                      time_to_datetime(g_config.g_now_time),
                                      g_config.sdx_config.sdx_ip_str,
                                      gettid(),
                                      dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL),
                                      g_config.yv_data_suffix);
        printf("sdt_event name:%s\n",out_elem->event_status.event_name);
        // 创建文件
        out_elem->event_status.event_fp = fopen(out_elem->event_status.event_name, "w");
        if(NULL==out_elem->event_status.event_fp){
            perror(out_elem->event_status.event_name);
            return 0;
        }
        out_elem->event_status.event_cnt=0;
    }

#ifdef DPI_FUTURE_MBUF
    int max = 1024*1024*10; //10M
    char *str = malloc(max);
    int  len = record_2_string(pkt_elem->record, str, max);
    ret = fwrite(str, 1, len, out_elem->event_status.event_fp);
    free(str);
    if (ret != len)
    {
        perror(__func__);
        return -1;
    }
#else
    ret = fwrite(pkt_elem->pkt_data, 1, pkt_elem->pkt_data_len, out_elem->event_status.event_fp);
    if(ret!=pkt_elem->pkt_data_len){
        perror(__func__);
        return -1;
    }
#endif
    fwrite("\n", 1, 1, out_elem->event_status.event_fp);
    fflush(out_elem->event_status.event_fp);

    out_elem->event_status.event_time=g_config.g_now_time;
    out_elem->event_status.event_cnt++;

    /* 大小和时间互斥，1.有大小没时间，2.没大小有时间*/
    if(out_elem->event_status.max_event_num>0){/* 配置每个pcap大小，达到该大小则分包 */
        if(out_elem->event_status.event_cnt>out_elem->event_status.max_event_num){
            sdt_out_event_fragment(out_elem);
            out_elem->event_status.event_cnt=0;
        }
    }
    return 0;
}

static int
sdt_out_syslog(sdt_out_status  *out_elem, struct packet_stream  *pkt_elem)
{
    int ret=0;

    if(NULL==out_elem->syslog_status.syslog_fp){
        char syslog_rule_dir[COMMON_FILE_PATH]={0};
        char syslog_tmp_name[COMMON_FILE_PATH]={0};
        snprintf(syslog_rule_dir,COMMON_FILE_PATH,"%s/%s/%s_%s/alarm/%.8s",
                                        g_config.sdt_out_syslog_dir,
                                        out_elem->match_result->taskID,
                                        out_elem->match_result->groupID,
                                        out_elem->match_result->groupName,
                                        time_to_datetime(g_config.g_now_time));
        mkdirs(syslog_rule_dir);
        // 设置alarm文件名
        snprintf(out_elem->syslog_status.syslog_name, COMMON_FILE_PATH,
                                      "%s/%s_%u_%s_%s_%u_%u.%s.tmp",
                                      syslog_rule_dir,
                                      g_config.sdt_out_produce_data_dev_name,
                                      pkt_elem->rule_id,        //把 ruleID 带在文件名上
                                      time_to_datetime(g_config.g_now_time),
                                      g_config.sdx_config.sdx_ip_str,
                                      gettid(),
									  dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL),
									  g_config.yv_data_suffix);

        log_trace("sdt_syslog name:%s\n",out_elem->syslog_status.syslog_name);
        // 创建文件
        out_elem->syslog_status.syslog_fp = fopen(out_elem->syslog_status.syslog_name, "w");
        if(NULL==out_elem->syslog_status.syslog_fp){
            perror(out_elem->syslog_status.syslog_name);
            return -1;
        }

        out_elem->syslog_status.syslog_time=g_config.g_now_time;
        out_elem->syslog_status.syslog_cnt=0;
    }

#ifdef DPI_FUTURE_MBUF
    int max = 1024*1024*10; //10M
    char *str = malloc(max);
    int  len = record_2_string(pkt_elem->record, str, max);
    ret = fwrite(str, 1, len, out_elem->event_status.event_fp);
    free(str);
    if (ret != len)
    {
        perror(__func__);
        return -1;
    }
#else
    fwrite(pkt_elem->pkt_data, pkt_elem->pkt_data_len, 1, out_elem->syslog_status.syslog_fp);
#endif
    fwrite("\n", 1, 1, out_elem->syslog_status.syslog_fp);
    fflush(out_elem->syslog_status.syslog_fp);
    out_elem->syslog_status.syslog_cnt++;

    /* 大小和时间互斥，1.有大小没时间，2.没大小有时间*/
    if(g_config.log_max_num>0){
        if(out_elem->syslog_status.syslog_cnt>g_config.log_max_num){
            sdt_out_syslog_fragment(out_elem);
            out_elem->syslog_status.syslog_cnt=0;
        }
    }

    return 0;
}



static void
sdt_out_process(SdxOutThreadCtx *sdx_out_ctx, struct packet_stream  *pkt_elem)
{
    sdt_out_status *rule_status=NULL;
    rule_status=sdt_rule_hash_lookup_key(pkt_elem->unitID, pkt_elem->taskID, pkt_elem->groupID, pkt_elem->rule_id);
    if(NULL==rule_status)
    {
        DPI_LOG(DPI_LOG_ERROR, "sdt_rule_hash_lookup_key [%s][%s][%s][%u]", pkt_elem->unitID, pkt_elem->taskID, pkt_elem->groupID, pkt_elem->rule_id);
        return;
    }
    rte_atomic64_inc(&sdt_pcap_out_pkts);

    //互斥量 - 先 FETCH 再ADD  -- 一次只允许单个线程操作(一个线程在删除/其他线程在查询)
    sdt_atomic_lock(&rule_status->in_used);
    //互斥量 需要配对使用, 下面代码不可以直接 return

    for(int idx = 0; idx < CHAR_BIT *(int)sizeof(pkt_elem->action_type); idx++)
    {
        uint32_t action = 1 << idx;
        if(pkt_elem->action_type & action)
        {
            switch(action)
            {
                case SAE_packetDump:
                    sdt_out_pcap(sdx_out_ctx, rule_status, pkt_elem);
                    break;

                case SAE_event:
                    sdt_out_event(rule_status, pkt_elem);
                    break;

                default:
                    break;
            }
        }
    }

    if(SDT_OUT_ACTUAL_DATA_SYSLOG==pkt_elem->data_type)
    {
        sdt_out_syslog(rule_status, pkt_elem);
    }

    //更改互斥量
    sdt_atomic_unlock(&rule_status->in_used);
    return;
}

//SDX 项目 带51字节头
void sdt_out_forward_mac(struct packet_stream *pkt_stream)
{
    if(0 == (SAE_packetDump & pkt_stream->action_type))
    {
        return;
    }

    if(0 == g_config.sdx_config.sdx_tx_port_num)
    {
        printf("ERROR: 无法发送报文 sdx_tx_port_num 0\n");
        return;
    }

    uint8_t     dst_mac[6];
    uint64_t rss = time(NULL);

    uint8_t mac_src[6];
    uint8_t mac_dst[6];
    struct rte_ether_addr ports_eth_addr;


	// 1. 配置 MAC
	int ret;
	int ethdr_size = sizeof(struct mac_packet_header);
	int pkt_size = 14 + pkt_stream->pkt_data_len + sizeof(Direct_IP_Custom_Tail); //另外再加14字节头
	uint8_t *p_new_pkt;
    struct rte_mbuf *tx_mbuf;

    if(pkt_size > g_config.mbuf_size)
        return;

    tx_mbuf = rte_pktmbuf_alloc(pktmbuf_pool[g_config.socketid]);
	if (!tx_mbuf) {
        log_error("Error on rte_pktmbuf_alloc, mbuf pool not enough");
        return;
    }

	tx_mbuf->data_len = pkt_size;
	tx_mbuf->pkt_len  = pkt_size;
    p_new_pkt = rte_pktmbuf_mtod(tx_mbuf, uint8_t *);
	struct mac_packet_header *sdx_ethdr = (struct mac_packet_header *)pkt_stream->pkt_data;

    //xml 文件中具有最高优先级
    if(pkt_stream->mode_param_num > 0)
    {
        //基于 RuleID 同源同宿
        uint8_t index = rss % pkt_stream->mode_param_num;
        memcpy(mac_dst, pkt_stream->mode_param[index], 6);

        if(index < 10)
        {
            index +=0;//第1层
            if(0 == mac_forward_cnt[index].cnt)
            {
                memcpy(mac_forward_cnt[index].mac, mac_dst, 6);
            }
            ATOMIC_FETCH_ADD(&mac_forward_cnt[index].cnt);
        }
    }
    else
    //默认的 MAC池来自 config.ini 中的配置
    if(g_config.sdx_mac_num > 0)
    {
        //基于 RuleID 同源同宿
        uint8_t index = rss % g_config.sdx_mac_num;
        memcpy(mac_dst, g_config.sdx_mac[index], 6);

        if(index < 10)
        {
            index +=10;//第2层
            if(0 == mac_forward_cnt[index].cnt)
            {
                memcpy(mac_forward_cnt[index].mac, mac_dst, 6);
            }
            ATOMIC_FETCH_ADD(&mac_forward_cnt[index].cnt);
        }
    }
    else
    {
        printf  ("WARN: 帧转发 mode_param_num %u\n", pkt_stream->mode_param_num);
        log_warn("WARN: 帧转发 mode_param_num %u", pkt_stream->mode_param_num);
        return ;
    }

    //获取网卡MAC
    struct rte_ether_addr dev_mac_src;
    int portid = rss % g_config.sdx_config.sdx_tx_port_num;
    rte_eth_macaddr_get(portid, &ports_eth_addr);
    rte_ether_addr_copy(&ports_eth_addr, &dev_mac_src);

    //SMAC 填充 IP
    //填充 task_sub_type 到 SMAC的倒数第2个字节
    memcpy(mac_src, dev_mac_src.addr_bytes, 6);
    mac_src[5] = (0x000000ff & g_config.sdx_config.sdx_ip_number);
    mac_src[4] = pkt_stream->task_sub_type;

    int offset = 0;
    memcpy(p_new_pkt+offset, mac_dst, 6);   offset+=6;
    memcpy(p_new_pkt+offset, mac_src, 6);   offset+=6;
    memcpy(p_new_pkt+offset, "\x08\x00", 2);offset+=2;
    memcpy(p_new_pkt+offset, pkt_stream->pkt_data, pkt_stream->pkt_data_len); offset+=pkt_stream->pkt_data_len;

    // 2. 增加自定义trailer
    Direct_IP_Custom_Tail tail;
    memset(&tail, 0, sizeof(Direct_IP_Custom_Tail));

    memcpy(tail.Lineno, sdx_ethdr->Datasrc.Global_LineNO, sizeof(sdx_ethdr->Datasrc.Global_LineNO));
    tail.TimeSpan  = ((uint32_t)sdx_ethdr->TimeStamp[0]) << 24;
    tail.TimeSpan |= ((uint32_t)sdx_ethdr->TimeStamp[1]) << 16;
    tail.TimeSpan |= ((uint32_t)sdx_ethdr->TimeStamp[2]) << 8;
    tail.TimeSpan |= ((uint32_t)sdx_ethdr->TimeStamp[3]);
    tail.Eth_Len  = sdx_ethdr->bLinkLen;
    tail.Eth_Type = sdx_ethdr->bLinkType;
    tail.Group_ID = atoi(pkt_stream->groupID);
    memcpy(tail.TaskID, pkt_stream->taskID, TASK_ID_LENGTH);

    //14字节头+51字节头+报文帧+98字节Direct_IP_Custom_Tail  // 2024.12.05 牡丹江现场对接-改正
    memcpy(p_new_pkt+offset , &tail, sizeof(Direct_IP_Custom_Tail));

    // 3. 进行转发操作
    int ok_num = 0;
    int fail_num = 0;
    ret = do_forward(portid, tx_mbuf, pkt_size, &ok_num, &fail_num);

    rte_atomic64_add(&sdt_pcap_out_forward_ok_pkts, ok_num);
    rte_atomic64_add(&sdt_pcap_out_forward_fail_pkts, fail_num);
    rte_atomic64_add(&sdt_pcap_out_forward_pkts, ok_num + fail_num);
}

int sdt_clean_rule_data_status(void)
{
    sdt_out_status  *next_hop;
    uint32_t        *rule_key;
    uint32_t        iter = 0;
    int             retval=0;

    SdxOutThreadCtx *ctx_tmp = SdxOutThreadCtx_new();

    while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key,
            (void *) &next_hop, &iter) >= 0)
    {
        if(0==next_hop->flag){
            continue;
        }

        sdt_atomic_lock(&next_hop->in_used);

        sdt_out_pcap_fragment(ctx_tmp, next_hop);

        sdt_out_event_fragment(next_hop);

        sdt_atomic_unlock(&next_hop->in_used);
    }

    SdxOutThreadCtx_free(ctx_tmp);

    return 1;
}

int sdt_in_pcap(SdtMatchResult  *pAction, const struct pkt_info  *pkt)
{
    rte_atomic64_inc(&sdt_pcap_do_pkt_dump);
    int ret=0;
    struct packet_stream *elem = pktstream_alloc();

    if (unlikely(elem == NULL))
        return 0;

    //解决 forward 没有 eth.type 的问题
    if (g_config.sdt_mac_forward_flag)
    {
        elem->mac_flag = 1;
        memcpy(elem->mac_hdr, pkt->raw_pkt, sizeof(elem->mac_hdr));
    }
 
    strncpy(elem->unitID, pAction->unitID, sizeof(elem->unitID));
    strncpy(elem->taskID, pAction->taskID, sizeof(elem->taskID));
    strncpy(elem->groupID,pAction->groupID,sizeof(elem->groupID));
    strncpy(elem->method, pAction->method, sizeof(elem->method));
    strncpy(elem->topicName,pAction->topicName,sizeof(elem->topicName));

    elem->rule_mode       = pAction->rule_mode;
    elem->task_mode       = pAction->task_mode;
    elem->task_sub_type   = pAction->task_sub_type;
    elem->mode_param_num  = pAction->mode_param_num;
    memcpy(elem->mode_param, pAction->mode_param, sizeof(pAction->mode_param));

    elem->action_type     = SAE_packetDump;
    elem->rule_id         = pAction->ruleID;
    elem->data_type       = SDT_OUT_ACTUAL_DATA_DUMP_PCAP;

#ifdef DPI_FUTURE_MBUF
    assert(pkt->mbuf);
    elem->mbuf            = rte_mbuf_clone_0(pkt->mbuf);
    elem->pkt_data_len    = rte_pktmbuf_data_len(pkt->mbuf);
    elem->pkt_data        = rte_pktmbuf_mtod(pkt->mbuf, uint8_t*);
#else
    elem->pkt_data_len    = pkt->pkt_len;
    memcpy(elem->pkt_data, pkt->raw_pkt, pkt->pkt_len);
#endif
    int ring_id = rte_atomic64_read(&sdt_syslog_fail_pkts) % g_config.sdt_out_thead_num;
    ret = rte_ring_mp_enqueue_burst(sdt_out_ring[ring_id], (void * const*)&elem, 1, NULL);
    if (ret < 1) {
        pktstream_free(elem);
        rte_atomic64_inc(&sdt_pcap_fail_pkts);
        return 0;
    }else{
        rte_atomic64_inc(&sdt_pcap_success_pkts);
    }

    return 1;
}

int sdt_in_event(struct flow_info *flow, SdtMatchResult  *sdt_act, int direction)
{
    int ret=0;
    struct packet_stream *elem = pktstream_alloc();
    if (unlikely(elem == NULL))
        return 0;

    strncpy(elem->unitID, sdt_act->unitID, sizeof(elem->unitID));
    strncpy(elem->taskID, sdt_act->taskID, sizeof(elem->taskID));
    strncpy(elem->groupID,sdt_act->groupID,sizeof(elem->groupID));
    strncpy(elem->method, sdt_act->method, sizeof(elem->method));
    strncpy(elem->topicName,sdt_act->topicName,sizeof(elem->topicName));

    elem->rule_mode       = sdt_act->rule_mode;
    elem->task_mode       = sdt_act->task_mode;
    elem->task_sub_type   = sdt_act->task_sub_type;
    elem->mode_param_num  = sdt_act->mode_param_num;
    memcpy(elem->mode_param, sdt_act->mode_param, sizeof(sdt_act->mode_param));

    elem->data_type       = SDT_OUT_ACTUAL_DATA_EVENT;
    elem->action_type     = SAE_event;
    elem->rule_id         = sdt_act->ruleID;

    precord_t *record;
    dpi_precord_new_record(record, NULL, NULL);
#ifdef DPI_FUTURE_MBUF
    if (record)
    {
        write_shared_header(record, TCP_PAYLOAD_MAX_LEN, flow, direction);
        elem->record = record;
    }
#else
    if (record)
    {
        int len = record_2_string(record, (char*)elem->pkt_data, TCP_PAYLOAD_MAX_LEN);
        elem->pkt_data_len = len;
        //立即释放
        sdt_precord_destroy(record);
    }
#endif
    int ring_id= rte_atomic64_read(&sdt_syslog_fail_pkts) % g_config.sdt_out_thead_num;
    ret = rte_ring_mp_enqueue_burst(sdt_out_ring[ring_id], (void * const*)&elem, 1, NULL);
    if (ret < 1) {
        pktstream_free(elem);
        rte_atomic64_inc(&sdt_event_fail_pkts);
        return 0;
    }else{
        rte_atomic64_inc(&sdt_event_success_pkts);
    }

    return 1;
}

int record_write_json(precord_t *record, cJSON *json);

int sdt_in_syslog(struct flow_info *flow, SdtMatchResult  *sdt_act)
{
    int ret=0;
    struct packet_stream *elem = pktstream_alloc();
    if (unlikely(elem == NULL))
        return 0;

    strncpy(elem->unitID, sdt_act->unitID, sizeof(elem->unitID));
    strncpy(elem->taskID, sdt_act->taskID, sizeof(elem->taskID));
    strncpy(elem->groupID,sdt_act->groupID,sizeof(elem->groupID));
    strncpy(elem->method, sdt_act->method, sizeof(elem->method));
    strncpy(elem->topicName,sdt_act->topicName,sizeof(elem->topicName));

    elem->rule_mode       = sdt_act->rule_mode;
    elem->task_mode       = sdt_act->task_mode;
    elem->task_sub_type   = sdt_act->task_sub_type;
    elem->mode_param_num  = sdt_act->mode_param_num;
    memcpy(elem->mode_param, sdt_act->mode_param, sizeof(sdt_act->mode_param));

    elem->data_type       = SDT_OUT_ACTUAL_DATA_SYSLOG;
    elem->action_type     = SAE_alert;
    elem->rule_id         = sdt_act->ruleID;

    precord_t *precord    = write_sdt_syslog(sdt_act);
#ifdef DPI_FUTURE_MBUF
    elem->record          = precord;
#else
    //SYSLOG 存储为 JSON 格式
    cJSON * obj = cJSON_CreateObject();
    record_write_json(precord, obj);
    char *field_val = cJSON_PrintUnformatted(obj);
    uint32_t json_len = strlen(field_val);
    if(json_len < sizeof(elem->pkt_data))
    {
        memcpy(elem->pkt_data, field_val, json_len);
        elem->pkt_data_len = json_len;
    }
    else
    {
        printf("syslog 内容异常, 无法输出\n");
    }
    cJSON_Delete(obj);
    free(field_val);
#endif

    int ring_id= rte_atomic64_read(&sdt_syslog_fail_pkts)  % g_config.sdt_out_thead_num;
    ret = rte_ring_mp_enqueue_burst(sdt_out_ring[ring_id], (void * const*)&elem, 1, NULL);
    if (ret < 1) {
        pktstream_free(elem);
        rte_atomic64_inc(&sdt_syslog_fail_pkts);
        return 0;
    }else{
        rte_atomic64_inc(&sdt_syslog_success_pkts);
    }

    return 1;
}

/* event 上报依赖于上报统计和命中统计 */
static int
sdt_collect_report_match_cnt(sdt_out_status *rule_elem, uint64_t *report_cnt,uint64_t *report_mn_cnt, uint64_t *match_hits)
{
    if(NULL==rule_elem){
        return 0;
    }
    int j=0;
    uint64_t   local_report_cnt    = 0;
    uint64_t   local_report_mn_cnt = 0;
    uint64_t   local_match_hits    = 0;
    for(j=0;j<(int)g_config.dissector_thread_num;j++){
        /* 规则命中event上报次数计数 */
        local_report_cnt+=  rule_elem->thread_statistics[j].rule_report_cnt;

        local_report_mn_cnt+=rule_elem->thread_statistics[j].rule_report_mn_cnt;

        /* 规则命中次数计数 */
        local_match_hits+= rule_elem->thread_statistics[j].rule_match_hits;

    }
    *report_cnt=local_report_cnt;
    *match_hits=local_match_hits;
    *report_mn_cnt=local_report_mn_cnt;

    return 1;
}

int sdt_event_handle(struct flow_info *flow, SdtMatchResult *actionMatched, sdt_out_status *get_rule_status,int direction)
{
    if(NULL==actionMatched){
        return -1;
    }

    if(! (SAE_event & actionMatched->action)) {    //只处理event功能
        return -1;
    }

    if(NULL==get_rule_status){
        get_rule_status=sdt_rule_hash_db_lookup(actionMatched);
        if(NULL==get_rule_status){
            return 0;
        }
    }

    get_rule_status->thread_statistics[flow->thread_id].rule_report_cnt++;
    sdt_in_event(flow, actionMatched, direction);

    return 1;
}


int sdt_out_packet_data_out(struct flow_info *flow, struct packet_stream *pos, SdtMatchResult  *sdt_act, sdt_out_status  *flow_rule_status)
{
    if(NULL==pos || NULL==sdt_act){
        return 0;
    }

    uint32_t free_space = 0;
    int      ring_id    = 0;
    int      ret        = 0;

    struct packet_stream *item = pktstream_alloc();
    if (unlikely(item == NULL))
        return 0;

    //传递相关参数
    strncpy(item->unitID, sdt_act->unitID, sizeof(item->unitID));
    strncpy(item->taskID, sdt_act->taskID, sizeof(item->taskID));
    strncpy(item->groupID,sdt_act->groupID,sizeof(item->groupID));
    strncpy(item->method, sdt_act->method, sizeof(item->method));
    strncpy(item->topicName,sdt_act->topicName,sizeof(item->topicName));

    //牡丹江现场 -- XML文件新增3类属性
    item->mode_param_num = sdt_act->mode_param_num;
    item->task_mode      = sdt_act->task_mode;
    item->task_sub_type  = sdt_act->task_sub_type;
    item->rule_mode      = sdt_act->rule_mode;
    memcpy(item->mode_param, sdt_act->mode_param, sizeof(item->mode_param));

    item->action_type    = SAE_packetDump;
    item->rule_id        = sdt_act->ruleID;
    item->direction      = flow->direction;
    item->data_type      = SDT_OUT_ACTUAL_DATA_DUMP_PCAP;

#ifdef DPI_FUTURE_MBUF
    item->mbuf           = rte_mbuf_clone_0(pos->mbuf);
    item->pkt_data_len   = rte_pktmbuf_data_len(pos->mbuf);
    item->pkt_data       = rte_pktmbuf_mtod(pos->mbuf, uint8_t*);
#else
    int len = pos->pkt_data_len;
    if(pos->pkt_data_len>TCP_PAYLOAD_MAX_LEN)
    {
        //printf("ERROR: pkt_data_len too long\n");
        len = TCP_PAYLOAD_MAX_LEN;
    }

    item->pkt_data_len   = pos->pkt_data_len;
    memcpy(item->pkt_data, pos->pkt_data, len);
#endif
    ring_id=sdt_act->ruleID%g_config.sdt_out_thead_num;
    ret = rte_ring_mp_enqueue_burst(sdt_out_ring[ring_id], (void * const*)&item, 1, &free_space);
    if(ret==1){
        rte_atomic64_inc(&sdt_pcap_success_pkts);
    }else{
        pktstream_free(item);
        rte_atomic64_inc(&sdt_pcap_fail_pkts);
    }
    return 1;
}



/***************************************************SDT 统计相关功能函数 *************************************************/
/*
* 单包模式命中统计
* 单包模式命中统计命中的单包数和大小
*
* 流模式命中统计
* 流模式命中统计要统计流中所有包数和包大小
*
*/

/* 对于单包模式每次命中一次统计一次，适用流模式第一次统计 */
int
sdt_rule_perthread_pkt_statistics(struct flow_info *flow,
                                  SdtMatchResult   *match_result,
                                  sdt_out_status   *flow_rule_status ,
                                  int thread_id, uint8_t flag)
{
    if(NULL ==  match_result){
        return 0;
    }
    if(NULL==flow_rule_status){
        flow_rule_status=sdt_rule_hash_db_lookup(match_result);
        if(NULL==flow_rule_status){
            // 输出错误日志
            return -1;
        }
    }

    for(int idx = 0; idx < CHAR_BIT *(int)sizeof(match_result->action); idx++)
    {
        uint32_t action = 1 << idx;
        if(match_result->action & action)
        {
            switch(action)
            {
                case SAE_packetDump:
                    {
                        if(match_result->packetDump_args.size>0){
                            flow_rule_status->pcap_status.max_pcap_size=
                                (uint64_t)match_result->packetDump_args.size*1024*1024;
                        }

                        if(match_result->packetDump_args.minute>0){
                            flow_rule_status->pcap_status.max_time =
                                match_result->packetDump_args.minute*60;
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }

    if(flow_rule_status->thread_statistics[thread_id].rule_first_match_time<=0){
        flow_rule_status->thread_statistics[thread_id].rule_first_match_time=g_config.g_now_time;
    }
    flow_rule_status->thread_statistics[thread_id].rule_last_match_time=g_config.g_now_time;

    if(flag){
        flow_rule_status->thread_statistics[thread_id].rule_match_flows++;
    }

    flow_rule_status->thread_statistics[thread_id].rule_match_hits++;
    // flow_rule_status->thread_statistics[flow->thread_id].rule_match_pkts = flow->dst2src_packets+flow->src2dst_packets;
    // flow_rule_status->thread_statistics[flow->thread_id].rule_match_bytes= flow->dst2src_bytes+flow->dst2src_bytes;
    return 1;
}


/* 对于非包模式匹配的需要统计流中其他报文数及字节数 */
int
sdt_rule_perthread_flow_statistics( SdtMatchResult   *   sdt_action, sdt_out_status  *flow_rule_status, int thread_id,uint16_t pkt_len)
{
    if(NULL==flow_rule_status){
        flow_rule_status=sdt_rule_hash_db_lookup(sdt_action);
        if(NULL==flow_rule_status){
            // 输出错误日志
            return -1;
        }
    }

    flow_rule_status->thread_statistics[thread_id].rule_match_pkts++;
    flow_rule_status->thread_statistics[thread_id].rule_match_bytes+=pkt_len;

    return 1;
}




/* 定时汇总统计总量 */
int
sdt_rule_collect_statistics(void)
{
    int i=0,j=0;

    sdt_out_status *next_hop;
    uint32_t       *rule_key;
    uint32_t       iter = 0;
    int            retval=0;

    while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key,
            (void *) &next_hop, &iter) >= 0)
    {
        if(0==next_hop->flag){
            continue;
        }
        if(next_hop->report_mn_sec>0){
            next_hop->time_count++;
        }

        uint8_t     clear_time_count=0;
        uint64_t   local_report_cnt  = 0;
        uint64_t   local_match_hits  = 0;
        uint64_t   local_match_pkts  = 0;
        uint64_t   local_match_bytes = 0;
        uint64_t   local_match_flows = 0;
        for(j=0;j<(int)g_config.dissector_thread_num;j++){
            /* 规则命中event上报次数计数 */
            local_report_cnt+=  next_hop->thread_statistics[j].rule_report_cnt;
            /* 规则命中次数计数 */
            local_match_hits+= next_hop->thread_statistics[j].rule_match_hits;
            /* 规则命中报文数据计数 */
            local_match_pkts+= next_hop->thread_statistics[j].rule_match_pkts;
            /* 规则命中字节数计数 */
            local_match_bytes+= next_hop->thread_statistics[j].rule_match_bytes;
            /* 规则命中流计数 */
            local_match_flows+= next_hop->thread_statistics[j].rule_match_flows;

            if(0==next_hop->statistics_current.rule_first_match_time &&
              (0!=next_hop->thread_statistics[j].rule_first_match_time))
            {
                next_hop->statistics_current.rule_first_match_time=
                               next_hop->thread_statistics[j].rule_first_match_time;
            }

            if(0!=next_hop->thread_statistics[j].rule_first_match_time &&
              (next_hop->statistics_current.rule_first_match_time>
               next_hop->thread_statistics[j].rule_first_match_time))
            {
                next_hop->statistics_current.rule_first_match_time=
                               next_hop->thread_statistics[j].rule_first_match_time;
            }

            if(next_hop->statistics_current.rule_last_match_time<
               next_hop->thread_statistics[j].rule_last_match_time)
            {
                next_hop->statistics_current.rule_last_match_time=
                               next_hop->thread_statistics[j].rule_last_match_time;
            }

            if(next_hop->report_mn_sec &&
               next_hop->time_count>next_hop->report_mn_sec){
                next_hop->thread_statistics[j].rule_report_mn_cnt=0;
                clear_time_count=1;
            }
        }

        if(1==clear_time_count){
            next_hop->time_count=0;
        }

        if(0==next_hop->statistics_before.rule_last_match_time){
            next_hop->statistics_before.rule_last_match_time=
                               next_hop->statistics_current.rule_last_match_time;
        }

        next_hop->statistics_current.rule_report_cnt  = local_report_cnt;
        next_hop->statistics_current.rule_match_hits  = local_match_hits;
        next_hop->statistics_current.rule_match_pkts  = local_match_pkts;
        next_hop->statistics_current.rule_match_bytes = local_match_bytes;


        next_hop->statistics_current.rule_match_flows = local_match_flows;


    }

    return 1;
}


int sdt_statistics_keep_before(void)
{
    sdt_out_status *next_hop;
    uint32_t       *rule_key;
    uint32_t       iter = 0;
    int            retval=0;

    while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key,
            (void *) &next_hop, &iter) >= 0)
    {
        if(0==next_hop->flag){
            continue;
        }

        next_hop->statistics_before.rule_match_hits  =
                           next_hop->statistics_current.rule_match_hits;

        next_hop->statistics_before.rule_match_pkts  =
                           next_hop->statistics_current.rule_match_pkts;
        next_hop->statistics_before.rule_match_bytes =
                           next_hop->statistics_current.rule_match_bytes;
        next_hop->statistics_before.rule_match_flows =
                           next_hop->statistics_current.rule_match_flows;

        if(0==next_hop->statistics_before.rule_first_match_time)
        {
            next_hop->statistics_before.rule_first_match_time =
                           next_hop->statistics_current.rule_first_match_time;
        }

        next_hop->statistics_before.rule_last_match_time =
                           next_hop->statistics_current.rule_last_match_time;

    }

    return 1;
}

/**
 * 测试专用, 输出格式化的信息,
 */
static int sdt_out_UNITTEST(SdxOutThreadCtx *ctx, struct packet_stream *node)
{
    if (unlikely(ctx->test_out_fp == NULL))
    {
        char tbl_path[PATH_MAX];
        snprintf(tbl_path, PATH_MAX, "%s/test_out_%d.txt", g_config.pcap_dir, ctx->ring_id);
        ctx->test_out_fp = fopen(tbl_path, "w");
        if (!ctx->test_out_fp)
        {
            perror("fopen");
            exit(1);
        }
    }

    fprintf(ctx->test_out_fp, "%s|%s|%u|\n", node->unitID, node->taskID, node->rule_id);

    return 0;
}

static int sdt_out_PCAP(SdxOutThreadCtx *sdx_out_ctx, struct packet_stream *node)
{
    sdt_out_process(sdx_out_ctx, node);
    return 0;
}

static int sdt_out_FRAME(SdxOutThreadCtx *sdx_out_ctx, struct packet_stream *node)
{
    sdt_out_forward_mac(node);
    return 0;
}

static int sdt_out_one(SdxOutThreadCtx *sdx_out_ctx, struct packet_stream *node)
{
    struct {
        const char *method;
        int rule_mode;
        int (*sdt_out_fun)(SdxOutThreadCtx *, struct packet_stream *node);
    } list[]={
        {"KS-ZC",  0, sdt_out_PCAP},      //method 的有效形式有多种
        {"KS-ZC",  1, sdt_out_FRAME},     //根据 rule_mode 形式转发
        {"ML-MAC", 0, sdt_out_FRAME},
        {"KS-QF",  0, sdt_out_FRAME},     //将数据帧发给清分设备
        {NULL, 0, NULL},
    };

    int i = 0;
    for(i = 0; list[i].method; i++)
    {
        if(0 == strcmp(list[i].method, node->method) && node->rule_mode==list[i].rule_mode)
        {
            return list[i].sdt_out_fun(sdx_out_ctx, node);
        }
    }

    // DPI_LOG(DPI_LOG_ERROR, "ERROR: %s %s RuleID:%u 未知类型 method:%s", node->taskID, node->groupID, node->rule_id, node->method);
    return 0;
}

static uint8_t sdt_out_signal = 0;
static pthread_t sdt_out_ths[TBL_RING_MAX_NUM] = {(pthread_t)0};
static pthread_t sdt_statistics_th = (pthread_t)0;

void *sdt_statistics_thread(void * arg)
{
    while(1)
    {
        if (unlikely(sdt_out_signal == 1))
            break;
        sleep(1);
        sdt_rule_collect_statistics();
    }
    return NULL;
}

int sdt_out_thfunc_timeout(SdxOutThreadCtx *sdx_out_ctx)
{
    sdt_out_status *next_hop;
    uint32_t       *rule_key;
    uint32_t       iter = 0;
    while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key, (void *) &next_hop, &iter) >= 0)
    {

        //互斥量 - 先 FETCH 再ADD
        sdt_atomic_lock(&next_hop->in_used);
        //互斥量 需要配对使用, 下面代码不可以直接 return

        /* pcap file timeout */
        if(next_hop->pcap_status.max_time>0     &&
           next_hop->pcap_status.pcap_fp!=NULL  &&
           next_hop->pcap_status.pcap_time + next_hop->pcap_status.max_time < g_config.g_now_time)
        {
             char pcap_tmp_filename[COMMON_FILE_PATH]={0};
             snprintf(pcap_tmp_filename, COMMON_FILE_PATH, "%s.pcap", next_hop->pcap_status.pcap_name);
             //printf("文件超时分割:%s\n", pcap_tmp_filename);
             sdt_out_pcap_fragment(sdx_out_ctx, next_hop);   //pcap文件分割
             next_hop->pcap_status.pcap_time=(uint32_t)g_config.g_now_time;
            //初始化文件大小
             next_hop->pcap_status.pkt_bytes=0;
        }

        /* event log timeout */
        if( next_hop->event_status.event_fp!=NULL &&
            next_hop->event_status.event_time + g_config.write_tbl_maxtime < g_config.g_now_time)
        {
             sdt_out_event_fragment(next_hop);   //pcap文件分割
             next_hop->event_status.event_time=g_config.g_now_time;
        }

        /* syslog timeout */
        if( next_hop->syslog_status.syslog_fp!=NULL &&
            next_hop->syslog_status.syslog_time + g_config.write_tbl_maxtime < g_config.g_now_time)
        {
             sdt_out_syslog_fragment(next_hop);   //pcap文件分割
             next_hop->syslog_status.syslog_time=g_config.g_now_time;
        }

        //只允许 单个成员访问
        sdt_atomic_unlock(&next_hop->in_used);
    }
    return 0;
}


static void
sdt_out_loop(SdxOutThreadCtx *ctx)
{
    int deq_num, j;
    struct rte_ring      *ring = sdt_out_ring[ctx->ring_id];
    struct packet_stream *elem;
    struct packet_stream *elem_burst[TBL_MAX_BURST];

    while (1)
    {
        if(unlikely(1==sdt_out_signal))
        {
            if (rte_ring_empty(ring))
                break;
        }

        //HTTP线程在清理 HASH_DB
        //两个日志线程在查 HASH DB
        //在这里等着 -- 直到清理完成
        if(g_sdt_hash_db_clear_flag)
        {
            ATOMIC_ADD_FETCH(&sdt_out_thfunc_signal);
            while(g_sdt_hash_db_clear_flag)
            {
                usleep(1000*100);
            }
            ATOMIC_SUB_FETCH(&sdt_out_thfunc_signal);
        }

        deq_num = rte_ring_sc_dequeue_burst(ring, (void **)elem_burst, TBL_MAX_BURST, NULL);
        for (j = 0; j < deq_num; j++)
        {
            elem = elem_burst[j];
            elem->work_on_ringId = ctx->ring_id;
            sdt_out_one(ctx, elem);
            pktstream_free(elem);
        }

        if (deq_num <= 0)
        {
            usleep(1000*300); //300MS
            static int sdt_out_thfunc_timeout_pv = 0;
            //////////// 唯一检测 多线程超时 ///////////////////
            int pv = ATOMIC_FETCH_ADD(&sdt_out_thfunc_timeout_pv);
            if(pv % g_config.sdt_out_thead_num == g_config.timeout_index % g_config.sdt_out_thead_num)
            {
                sdt_out_thfunc_timeout(ctx);
            }
            ATOMIC_FETCH_SUB(&sdt_out_thfunc_timeout_pv);
        }
        SdxRecIPRule_rotate(&ctx->sdx_rec_rule);
    }
}

/**
 * 自动测试专用输出行为
 */
static void
sdt_out_loop_test(SdxOutThreadCtx *ctx)
{
    int deq_num, j;
    struct rte_ring      *ring = sdt_out_ring[ctx->ring_id];
    struct packet_stream *elem;
    struct packet_stream *elem_burst[TBL_MAX_BURST]={0};

    while (1)
    {
        if(unlikely(1==sdt_out_signal))
        {
            if (rte_ring_empty(ring))
                break;
        }

        //在这里等着 -- 直到清理完成
        if(unlikely(g_sdt_hash_db_clear_flag))
        {
            ATOMIC_ADD_FETCH(&sdt_out_thfunc_signal);
            while(g_sdt_hash_db_clear_flag)
            {
                usleep(1000*100);
            }
            ATOMIC_SUB_FETCH(&sdt_out_thfunc_signal);
        }

        deq_num = rte_ring_sc_dequeue_burst(ring, (void **)elem_burst, TBL_MAX_BURST, NULL);
        for (j = 0; j < deq_num; j++)
        {
            elem = elem_burst[j];
            sdt_out_UNITTEST(ctx, elem);
            pktstream_free(elem);
        }
    }
}


void *sdt_out_thfunc(void * arg)
{
    SdxOutThreadCtx *ctx = (SdxOutThreadCtx*)arg;
    //对本线程实例计数
    ATOMIC_ADD_FETCH(&sdt_out_thfunc_cnt);

    int core_affinity = g_config.sdt_out_core[ctx->ring_id];
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(core_affinity, &cpuset);

    if(pthread_setaffinity_np(pthread_self(), sizeof(cpu_set_t), &cpuset) != 0){
        DPI_LOG(DPI_LOG_WARNING, "Error while binding log thread to core %d",
                                                              core_affinity);
    }
    DPI_LOG(DPI_LOG_DEBUG, "Running log thread on core %d", core_affinity);

    if (g_config.test_mode) {
        sdt_out_loop_test(ctx);
    } else {
        sdt_out_loop(ctx);
    }

    SdxOutThreadCtx_free(ctx);
    ATOMIC_SUB_FETCH(&sdt_out_thfunc_cnt);
    return NULL;
}

static void
sdt_out_thread(void)
{
    int status;
    long i;
    pthread_t out_th;
    for(i=0; i < g_config.sdt_out_thead_num; i++)
    {
        SdxOutThreadCtx  *sdx_out_ctx = SdxOutThreadCtx_new();

        sdx_out_ctx->ring_id = i;

        status = pthread_create(&out_th, NULL, sdt_out_thfunc, (void*)sdx_out_ctx);
        if(status != 0) {
            DPI_SYS_LOG(DPI_LOG_ERROR, "error on create sdt_out_thfunc thread");
            exit(-1);
        }

        sdt_out_ths[i] = out_th;
    }

    status = pthread_create(&sdt_statistics_th, NULL, sdt_statistics_thread, NULL);
    if(status != 0) {
        DPI_SYS_LOG(DPI_LOG_ERROR, "error on sdt_statistics_thread");
        exit(-1);
    }


}


int sdt_io_init(void)
{

    int ret=0;
    ret=sdt_out_init();
    if(ret<0){
        DPI_SYS_LOG(DPI_LOG_ERROR, "sdt_out_init failed");
    }

    sdt_out_thread();

    return 1;
}

void sdt_out_threads_stop(void)
{
    int i;

    sdt_out_signal = 1;

    for(i=0; i < g_config.sdt_out_thead_num; i++)
    {
        if (!pthread_equal(sdt_out_ths[i], (pthread_t)0))
            pthread_join(sdt_out_ths[i], NULL);
    }

    if (!pthread_equal(sdt_statistics_th, (pthread_t)0))
        pthread_join(sdt_statistics_th, NULL);

    log_trace("pcap输出线程退出");
}


SdxOutThreadCtx* SdxOutThreadCtx_new()
{
    SdxOutThreadCtx *ctx = dpi_malloc(sizeof(SdxOutThreadCtx));

    memset(ctx, 0, sizeof(SdxOutThreadCtx));
    return ctx;
}

void SdxOutThreadCtx_free(SdxOutThreadCtx* ctx)
{
    if (ctx)
    {
        if (ctx->test_out_fp) fclose(ctx->test_out_fp);
        SdxRecIPRule_uninit(&ctx->sdx_rec_rule);
        dpi_free(ctx);
    }
}


/* 文件序号操作 */
#define GET_WHOLE_DAYS_BY_SEC(t)    (((t)+8*60*60) / (24*60*60))

static uint32_t     global_file_seq = 1;
static uint32_t     s_pre_days = 0;
static GMutex       file_seq_mtx;

static uint32_t dpi_safe_get_global_filename_seq(void)
{
    uint32_t cur_days = GET_WHOLE_DAYS_BY_SEC(g_config.g_now_time);

    if (cur_days == s_pre_days)
    {
        return ATOMIC_FETCH_ADD(&global_file_seq);
    }

    g_mutex_lock(&file_seq_mtx);
    if (cur_days != s_pre_days)
    {
        s_pre_days      = cur_days;
        global_file_seq = 1;
    }
    g_mutex_unlock(&file_seq_mtx);

    return ATOMIC_FETCH_ADD(&global_file_seq);
}

uint32_t dpi_safe_get_filename_seq(int file_seq_type)
{
    uint32_t seq = 0;

    switch (file_seq_type)
    {
    case FILE_SEQ_TYPE_GLOBAL:
        seq = dpi_safe_get_global_filename_seq();
        break;
    default:
        DPI_LOG(DPI_LOG_ERROR, "Unknown file_seq_type[%d]", file_seq_type);
        abort();
        break;
    }

    return seq;
}

int
sdt_check_rule_exist_report(struct flow_info *flow)
{
    uint32_t i;
    for(i=0;i<flow->sdt_flow.sdt_rule_cnt;i++)
    {
        if(!flow->sdt_flow.sdt_rules_status[i])
        {
            continue;
        }
        if(SAE_report & flow->sdt_flow.sdt_rules_status[i]->match_result->action)
        {
            return 1;
        }
    }
    return 0;
}

