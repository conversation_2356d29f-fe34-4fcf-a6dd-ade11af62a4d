/****************************************************************************************
 * 文 件 名 : dpi_snmp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: liugh      2018/09/10
编码: liugh      2018/09/10
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <yaProtoRecord/precord_schema.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_dissector.h"
#include "dpi_ber_ori.h"
#include "dpi_pschema.h"


int encode_oid_data(char *str, const char *data, uint16_t  oid_len);

extern struct rte_mempool *tbl_log_mempool;


#define SNMP_PORT_DEF       161
#define SNMP_PORT_DEF2      162

#define SNMP_OID_NUMBER     40

#define SNMP_OID_NUM        (20)


/* Security Models */

#define SNMP_SEC_ANY            0
#define SNMP_SEC_V1                1
#define SNMP_SEC_V2C            2
#define SNMP_SEC_USM            3


static const struct int_to_string sec_models[] = {
    { SNMP_SEC_ANY,            "Any" },
    { SNMP_SEC_V1,            "V1" },
    { SNMP_SEC_V2C,            "V2C" },
    { SNMP_SEC_USM,            "USM" },
    { 0,                NULL }
};

typedef enum _snmp_item {
    EM_VERSION,
    EM_COMMUNITY,
    EM_PDU_TYPE,
    EM_REQUEST_ID,
    EM_ERROR,
    EM_ERROR_INDEX,
    EM_ENTERPRISE,
    EM_AGENT_ADDR,
    EM_GENERIC_TRAP,
    EM_SPECIFIC_TRAP,
    EM_TIME_STAMP,

    EM_IDENTIFIER,
    EM_CONTEXT_NAME,
    EM_CONTEXT_ENGINE,
    EM_MSG_DIGEST,
    EM_MSG_SALT,
    EM_USER_SECNAME,
    EM_USER_AUTH_PROTO,
    EM_USER_AUTH_KEY,
    EM_USER_PRIV_PROTO,
    EM_USER_PRIV_KEY,
    EM_ENGINE_BOOTS,
    EM_ENGINE_TIME,
    EM_ENGINE_MAX_MSG_SIZE,
    EM_ENGINE_ID,


    EM_ITEM_MAX
}snmp_item_e;


#define SNMP_IPA    0        /* IP Address */
#define SNMP_CNT    1        /* Counter (Counter32) */
#define SNMP_GGE    2        /* Gauge (Gauge32) */
#define SNMP_TIT    3        /* TimeTicks */
#define SNMP_OPQ    4        /* Opaque */
#define SNMP_NSP    5        /* NsapAddress */
#define SNMP_C64    6        /* Counter64 */
#define SNMP_U32    7        /* Uinteger32 */

#define SERR_NSO    0
#define SERR_NSI    1
#define SERR_EOM    2


#define MR_SNMP_BOOL           0x01
#define MR_SNMP_INT            0x02
#define MR_SNMP_OCTSTR      0x04
#define MR_SNMP_NULL           0x05
#define MR_SNMP_OBJID          0x06
#define MR_SNMP_ENUM        0x0A
#define MR_SNMP_SEQ         0x30
#define MR_SNMP_SETOF       0x31
#define MR_SNMP_IPADDR      0x40
#define MR_SNMP_COUNTER     0x41
#define MR_SNMP_GAUGE       0x42
#define MR_SNMP_TIMETICKS   0x43
#define MR_SNMP_OPAQUE      0x44
#define MR_SNMP_GET         0xA0
#define MR_SNMP_GETNEXT     0xA1
#define MR_SNMP_GETRESP     0xA2
#define MR_SNMP_SET         0xA3
#define MR_SNMP_TRAP        0xA4


typedef enum _snmp_index_em {
    EM_SNMP_VERSION,
    EM_SNMP_COMMUNITY,
    EM_SNMP_PDU_TYPE,
    EM_SNMP_REQUEST_ID,
    EM_SNMP_ERROR_STATUS,
    EM_SNMP_ERROR_INDEX,
    EM_SNMP_ENTERPRISE,
    EM_SNMP_AGENT_ADDR,
    EM_SNMP_GENERIC_TRAP,
    EM_SNMP_SPECIFIC_TRAP,
    EM_SNMP_TIME_STAMP,
    EM_SNMP_IDENTIFIER,
    EM_SNMP_CONTEXT_NAME,
    EM_SNMP_CONTEXT_ENGINE,
    EM_SNMP_MSG_DIGEST,
    EM_SNMP_MSG_SALT,
    EM_SNMP_MSG_FLAG_REPORT,
    EM_SNMP_MSG_FLAG_ENCRYPT,
    EM_SNMP_MSG_FLAG_AUTH,
    EM_SNMP_MSG_SECMODE,
    EM_SNMP_USER_SECNAME,
    EM_SNMP_USER_AUTH_PROTO,
    EM_SNMP_USER_AUTH_KEY,
    EM_SNMP_USER_PRIV_PROTO,
    EM_SNMP_USER_PRIV_KEY,
    EM_SNMP_ENGINE_BOOTS,
    EM_SNMP_ENGINE_TIME,
    EM_SNMP_ENGINE_MAX_MSG_SIZE,
    EM_SNMP_ENGINE_ID,
    EM_SNMP_ENGINE_ID_FORMAT,
    EM_SNMP_ENGINE_ID_DATA,
    EM_SNMP_SYS_NAME,
    EM_SNMP_SYS_DESC,
    EM_SNMP_TOTAL_OID,
    EM_SNMP_O_OID00,
    EM_SNMP_O_SYNTAX00,
    EM_SNMP_O_NAME00,
    EM_SNMP_O_VALUE00,
    EM_SNMP_O_OID01,
    EM_SNMP_O_SYNTAX01,
    EM_SNMP_O_NAME01,
    EM_SNMP_O_VALUE01,
    EM_SNMP_O_OID02,
    EM_SNMP_O_SYNTAX02,
    EM_SNMP_O_NAME02,
    EM_SNMP_O_VALUE02,
    EM_SNMP_O_OID03,
    EM_SNMP_O_SYNTAX03,
    EM_SNMP_O_NAME03,
    EM_SNMP_O_VALUE03,
    EM_SNMP_O_OID04,
    EM_SNMP_O_SYNTAX04,
    EM_SNMP_O_NAME04,
    EM_SNMP_O_VALUE04,
    EM_SNMP_O_OID05,
    EM_SNMP_O_SYNTAX05,
    EM_SNMP_O_NAME05,
    EM_SNMP_O_VALUE05,
    EM_SNMP_O_OID06,
    EM_SNMP_O_SYNTAX06,
    EM_SNMP_O_NAME06,
    EM_SNMP_O_VALUE06,
    EM_SNMP_O_OID07,
    EM_SNMP_O_SYNTAX07,
    EM_SNMP_O_NAME07,
    EM_SNMP_O_VALUE07,
    EM_SNMP_O_OID08,
    EM_SNMP_O_SYNTAX08,
    EM_SNMP_O_NAME08,
    EM_SNMP_O_VALUE08,
    EM_SNMP_O_OID09,
    EM_SNMP_O_SYNTAX09,
    EM_SNMP_O_NAME09,
    EM_SNMP_O_VALUE09,
    EM_SNMP_O_OID10,
    EM_SNMP_O_SYNTAX10,
    EM_SNMP_O_NAME10,
    EM_SNMP_O_VALUE10,
    EM_SNMP_O_OID11,
    EM_SNMP_O_SYNTAX11,
    EM_SNMP_O_NAME11,
    EM_SNMP_O_VALUE11,
    EM_SNMP_O_OID12,
    EM_SNMP_O_SYNTAX12,
    EM_SNMP_O_NAME12,
    EM_SNMP_O_VALUE12,
    EM_SNMP_O_OID13,
    EM_SNMP_O_SYNTAX13,
    EM_SNMP_O_NAME13,
    EM_SNMP_O_VALUE13,
    EM_SNMP_O_OID14,
    EM_SNMP_O_SYNTAX14,
    EM_SNMP_O_NAME14,
    EM_SNMP_O_VALUE14,
    EM_SNMP_O_OID15,
    EM_SNMP_O_SYNTAX15,
    EM_SNMP_O_NAME15,
    EM_SNMP_O_VALUE15,
    EM_SNMP_O_OID16,
    EM_SNMP_O_SYNTAX16,
    EM_SNMP_O_NAME16,
    EM_SNMP_O_VALUE16,
    EM_SNMP_O_OID17,
    EM_SNMP_O_SYNTAX17,
    EM_SNMP_O_NAME17,
    EM_SNMP_O_VALUE17,
    EM_SNMP_O_OID18,
    EM_SNMP_O_SYNTAX18,
    EM_SNMP_O_NAME18,
    EM_SNMP_O_VALUE18,
    EM_SNMP_O_OID19,
    EM_SNMP_O_SYNTAX19,
    EM_SNMP_O_NAME19,
    EM_SNMP_O_VALUE19,

    EM_SNMP_OIDS,
    EM_SNMP_OBJ_VALS,
    EM_SNMP_OBJ_SYNS,
    EM_SNMP_OBJ_NAMES,

    EM_SNMP_MAX
}snmp_index_em;

typedef struct _oid {
    uint8_t        syntax_type;
    uint8_t        name_type;
    uint8_t        val_type;
    uint8_t     ber_class;

    uint8_t        name_len;
    uint8_t        val_len;

    char        obj_name[256];
    char        value[256];
    char        syntax[256];
} oid;

typedef struct _ber_data {
    uint8_t  code_type;
    uint8_t  length;
    const uint8_t     *data;
}st_ber_data;


typedef struct _ber_oid {
    st_ber_data oid_name;
    st_ber_data oid_syntax;
    st_ber_data oid_value;
}st_ber_oid;

typedef struct _snmp_info {
    uint8_t       version;
    uint8_t       pdu_type;
    uint64_t      request_id;
    uint64_t      error_status;
    uint64_t      error_index;
    uint8_t          error_status_f;

    uint64_t      spec_trap;
    uint64_t      time_stamp;

    uint16_t      total_oid;

    st_ber_data   snmp_data[EM_ITEM_MAX];
    st_ber_oid    snmp_oid[SNMP_OID_NUMBER];

    uint8_t       msg_flags;
    uint8_t          msg_report_flag;
    uint8_t          msg_encrypt_flag;
    uint8_t       msg_auth_flag;

    uint8_t          engine_id_conform;
    uint32_t      engine_enteprise_id;
    uint8_t          engine_id_format;
    uint8_t          engine_id_data[27];
    uint8_t          engine_id_datalen;
    uint64_t      engine_id_time;
    uint32_t      engine_id_ipv4;
    uint8_t       engine_id_ipv6[16];
    uint8_t          engine_id_cisco_type;
    uint8_t          engine_id_mac[6];
    uint8_t          engine_id_text[28];  // 最大 27 bytes
    uint8_t          auth_engine_id[256];
    uint8_t          auth_engine_id_len;

    uint8_t       msg_engine_boots;
    uint32_t      msg_engine_time;
    char          msg_username[256];
    char          msg_auth_params[256];
    char          msg_privacy_parmas[256];
    char          context_name[256];

//    uint64_t      request_id;

    uint8_t       msg_head_f;
    uint32_t      msg_id;
    uint32_t      msg_max_size;
    uint32_t      msg_security_mode;

    oid              oid_list[SNMP_OID_NUM];
    int              oid_count;

    int           enterprise_oid_len;
    char          enterprise_oid[256];
    char          net_address[256];
    char          net_address_len; // ip version

    uint8_t       general_trap_f;
    uint8_t       special_trap_f;
    uint32_t      general_trap;
    uint32_t      special_trap;

    char          community[256];
    char          encrypted[256];
    char          params[256];

    char          sysName[256];
    char          sysDesc[256];

    char         oids[1024];
    char         obj_vals[1024];
    char         obj_syns[1024];
    char         obj_names[1024];

}st_snmp_info;


static dpi_field_table  snmp_field_array[] = {
    DPI_FIELD_D(EM_SNMP_VERSION,                 YA_FT_UINT8,                 "Version"),
    DPI_FIELD_D(EM_SNMP_COMMUNITY,               YA_FT_STRING,                "Community"),
    DPI_FIELD_D(EM_SNMP_PDU_TYPE,                YA_FT_UINT8,                 "PDU_Type"),
    DPI_FIELD_D(EM_SNMP_REQUEST_ID,              YA_FT_STRING,                "Request_ID"),
    DPI_FIELD_D(EM_SNMP_ERROR_STATUS,            YA_FT_UINT8,                 "ErrorStatus"),
    DPI_FIELD_D(EM_SNMP_ERROR_INDEX,             YA_FT_STRING,                "Error_Index"),
    DPI_FIELD_D(EM_SNMP_ENTERPRISE,              YA_FT_STRING,                "enterprise"),
    DPI_FIELD_D(EM_SNMP_AGENT_ADDR,              YA_FT_STRING,                "agent_addr"),
    DPI_FIELD_D(EM_SNMP_GENERIC_TRAP,            YA_FT_STRING,                "generic_trap"),
    DPI_FIELD_D(EM_SNMP_SPECIFIC_TRAP,           YA_FT_STRING,                "specific_trap"),
    DPI_FIELD_D(EM_SNMP_TIME_STAMP,              YA_FT_UINT64,                "time_stamp"),
    DPI_FIELD_D(EM_SNMP_IDENTIFIER,              YA_FT_UINT32,                "identifier"),
    DPI_FIELD_D(EM_SNMP_CONTEXT_NAME,            YA_FT_STRING,                "context_name"),
    DPI_FIELD_D(EM_SNMP_CONTEXT_ENGINE,          YA_FT_UINT32,                "context_engine"),
    DPI_FIELD_D(EM_SNMP_MSG_DIGEST,              YA_FT_STRING,                "msg_digest"),
    DPI_FIELD_D(EM_SNMP_MSG_SALT,                YA_FT_STRING,                "msg_salt"),
    DPI_FIELD_D(EM_SNMP_MSG_FLAG_REPORT,         YA_FT_UINT8,                 "msgReportFlag"),
    DPI_FIELD_D(EM_SNMP_MSG_FLAG_ENCRYPT,        YA_FT_UINT8,                 "msgEncryptFlag"),
    DPI_FIELD_D(EM_SNMP_MSG_FLAG_AUTH,           YA_FT_UINT8,                 "msgAuthFlag"),
    DPI_FIELD_D(EM_SNMP_MSG_SECMODE,             YA_FT_UINT8,                 "sec_mode"),
    DPI_FIELD_D(EM_SNMP_USER_SECNAME,            YA_FT_STRING,                "user_secname"),
    DPI_FIELD_D(EM_SNMP_USER_AUTH_PROTO,         YA_FT_STRING,                "user_auth_proto"),
    DPI_FIELD_D(EM_SNMP_USER_AUTH_KEY,           YA_FT_STRING,                "user_auth_key"),
    DPI_FIELD_D(EM_SNMP_USER_PRIV_PROTO,         YA_FT_STRING,                "user_priv_proto"),
    DPI_FIELD_D(EM_SNMP_USER_PRIV_KEY,           YA_FT_STRING,                "user_priv_key"),
    DPI_FIELD_D(EM_SNMP_ENGINE_BOOTS,            YA_FT_UINT8,                 "engine_boots"),
    DPI_FIELD_D(EM_SNMP_ENGINE_TIME,             YA_FT_UINT32,                "engine_time"),
    DPI_FIELD_D(EM_SNMP_ENGINE_MAX_MSG_SIZE,     YA_FT_UINT32,                "engine_max_msg_size"),
    DPI_FIELD_D(EM_SNMP_ENGINE_ID,               YA_FT_STRING,                "engine_id"),
    DPI_FIELD_D(EM_SNMP_ENGINE_ID_FORMAT,        YA_FT_STRING,                "engineIdFormat"),
    DPI_FIELD_D(EM_SNMP_ENGINE_ID_DATA,          YA_FT_STRING,                "engineIdData"),
    DPI_FIELD_D(EM_SNMP_SYS_NAME,                YA_FT_STRING,                "sysName"),
    DPI_FIELD_D(EM_SNMP_SYS_DESC,                YA_FT_STRING,                "sysDesc"),
    DPI_FIELD_D(EM_SNMP_TOTAL_OID,               YA_FT_UINT16,                "Total_OID"),
    DPI_FIELD_D(EM_SNMP_O_OID00,                 YA_FT_STRING,                "O_OID00"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX00,              YA_FT_STRING,                "O_Syntax00"),
    DPI_FIELD_D(EM_SNMP_O_NAME00,                YA_FT_STRING,                "O_Name00"),
    DPI_FIELD_D(EM_SNMP_O_VALUE00,               YA_FT_STRING,                "O_Value00"),
    DPI_FIELD_D(EM_SNMP_O_OID01,                 YA_FT_STRING,                "O_OID01"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX01,              YA_FT_STRING,                "O_Syntax01"),
    DPI_FIELD_D(EM_SNMP_O_NAME01,                YA_FT_STRING,                "O_Name01"),
    DPI_FIELD_D(EM_SNMP_O_VALUE01,               YA_FT_STRING,                "O_Value01"),
    DPI_FIELD_D(EM_SNMP_O_OID02,                 YA_FT_STRING,                "O_OID02"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX02,              YA_FT_STRING,                "O_Syntax02"),
    DPI_FIELD_D(EM_SNMP_O_NAME02,                YA_FT_STRING,                "O_Name02"),
    DPI_FIELD_D(EM_SNMP_O_VALUE02,               YA_FT_STRING,                "O_Value02"),
    DPI_FIELD_D(EM_SNMP_O_OID03,                 YA_FT_STRING,                "O_OID03"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX03,              YA_FT_STRING,                "O_Syntax03"),
    DPI_FIELD_D(EM_SNMP_O_NAME03,                YA_FT_STRING,                "O_Name03"),
    DPI_FIELD_D(EM_SNMP_O_VALUE03,               YA_FT_STRING,                "O_Value03"),
    DPI_FIELD_D(EM_SNMP_O_OID04,                 YA_FT_STRING,                "O_OID04"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX04,              YA_FT_STRING,                "O_Syntax04"),
    DPI_FIELD_D(EM_SNMP_O_NAME04,                YA_FT_STRING,                "O_Name04"),
    DPI_FIELD_D(EM_SNMP_O_VALUE04,               YA_FT_STRING,                "O_Value04"),
    DPI_FIELD_D(EM_SNMP_O_OID05,                 YA_FT_STRING,                "O_OID05"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX05,              YA_FT_STRING,                "O_Syntax05"),
    DPI_FIELD_D(EM_SNMP_O_NAME05,                YA_FT_STRING,                "O_Name05"),
    DPI_FIELD_D(EM_SNMP_O_VALUE05,               YA_FT_STRING,                "O_Value05"),
    DPI_FIELD_D(EM_SNMP_O_OID06,                 YA_FT_STRING,                "O_OID06"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX06,              YA_FT_STRING,                "O_Syntax06"),
    DPI_FIELD_D(EM_SNMP_O_NAME06,                YA_FT_STRING,                "O_Name06"),
    DPI_FIELD_D(EM_SNMP_O_VALUE06,               YA_FT_STRING,                "O_Value06"),
    DPI_FIELD_D(EM_SNMP_O_OID07,                 YA_FT_STRING,                "O_OID07"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX07,              YA_FT_STRING,                "O_Syntax07"),
    DPI_FIELD_D(EM_SNMP_O_NAME07,                YA_FT_STRING,                "O_Name07"),
    DPI_FIELD_D(EM_SNMP_O_VALUE07,               YA_FT_STRING,                "O_Value07"),
    DPI_FIELD_D(EM_SNMP_O_OID08,                 YA_FT_STRING,                "O_OID08"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX08,              YA_FT_STRING,                "O_Syntax08"),
    DPI_FIELD_D(EM_SNMP_O_NAME08,                YA_FT_STRING,                "O_Name08"),
    DPI_FIELD_D(EM_SNMP_O_VALUE08,               YA_FT_STRING,                "O_Value08"),
    DPI_FIELD_D(EM_SNMP_O_OID09,                 YA_FT_STRING,                "O_OID09"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX09,              YA_FT_STRING,                "O_Syntax09"),
    DPI_FIELD_D(EM_SNMP_O_NAME09,                YA_FT_STRING,                "O_Name09"),
    DPI_FIELD_D(EM_SNMP_O_VALUE09,               YA_FT_STRING,                "O_Value09"),
    DPI_FIELD_D(EM_SNMP_O_OID10,                 YA_FT_STRING,                "O_OID10"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX10,              YA_FT_STRING,                "O_Syntax10"),
    DPI_FIELD_D(EM_SNMP_O_NAME10,                YA_FT_STRING,                "O_Name10"),
    DPI_FIELD_D(EM_SNMP_O_VALUE10,               YA_FT_STRING,                "O_Value10"),
    DPI_FIELD_D(EM_SNMP_O_OID11,                 YA_FT_STRING,                "O_OID11"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX11,              YA_FT_STRING,                "O_Syntax11"),
    DPI_FIELD_D(EM_SNMP_O_NAME11,                YA_FT_STRING,                "O_Name11"),
    DPI_FIELD_D(EM_SNMP_O_VALUE11,               YA_FT_STRING,                "O_Value11"),
    DPI_FIELD_D(EM_SNMP_O_OID12,                 YA_FT_STRING,                "O_OID12"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX12,              YA_FT_STRING,                "O_Syntax12"),
    DPI_FIELD_D(EM_SNMP_O_NAME12,                YA_FT_STRING,                "O_Name12"),
    DPI_FIELD_D(EM_SNMP_O_VALUE12,               YA_FT_STRING,                "O_Value12"),
    DPI_FIELD_D(EM_SNMP_O_OID13,                 YA_FT_STRING,                "O_OID13"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX13,              YA_FT_STRING,                "O_Syntax13"),
    DPI_FIELD_D(EM_SNMP_O_NAME13,                YA_FT_STRING,                "O_Name13"),
    DPI_FIELD_D(EM_SNMP_O_VALUE13,               YA_FT_STRING,                "O_Value13"),
    DPI_FIELD_D(EM_SNMP_O_OID14,                 YA_FT_STRING,                "O_OID14"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX14,              YA_FT_STRING,                "O_Syntax14"),
    DPI_FIELD_D(EM_SNMP_O_NAME14,                YA_FT_STRING,                "O_Name14"),
    DPI_FIELD_D(EM_SNMP_O_VALUE14,               YA_FT_STRING,                "O_Value14"),
    DPI_FIELD_D(EM_SNMP_O_OID15,                 YA_FT_STRING,                "O_OID15"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX15,              YA_FT_STRING,                "O_Syntax15"),
    DPI_FIELD_D(EM_SNMP_O_NAME15,                YA_FT_STRING,                "O_Name15"),
    DPI_FIELD_D(EM_SNMP_O_VALUE15,               YA_FT_STRING,                "O_Value15"),
    DPI_FIELD_D(EM_SNMP_O_OID16,                 YA_FT_STRING,                "O_OID16"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX16,              YA_FT_STRING,                "O_Syntax16"),
    DPI_FIELD_D(EM_SNMP_O_NAME16,                YA_FT_STRING,                "O_Name16"),
    DPI_FIELD_D(EM_SNMP_O_VALUE16,               YA_FT_STRING,                "O_Value16"),
    DPI_FIELD_D(EM_SNMP_O_OID17,                 YA_FT_STRING,                "O_OID17"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX17,              YA_FT_STRING,                "O_Syntax17"),
    DPI_FIELD_D(EM_SNMP_O_NAME17,                YA_FT_STRING,                "O_Name17"),
    DPI_FIELD_D(EM_SNMP_O_VALUE17,               YA_FT_STRING,                "O_Value17"),
    DPI_FIELD_D(EM_SNMP_O_OID18,                 YA_FT_STRING,                "O_OID18"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX18,              YA_FT_STRING,                "O_Syntax18"),
    DPI_FIELD_D(EM_SNMP_O_NAME18,                YA_FT_STRING,                "O_Name18"),
    DPI_FIELD_D(EM_SNMP_O_VALUE18,               YA_FT_STRING,                "O_Value18"),
    DPI_FIELD_D(EM_SNMP_O_OID19,                 YA_FT_STRING,                "O_OID19"),
    DPI_FIELD_D(EM_SNMP_O_SYNTAX19,              YA_FT_STRING,                "O_Syntax19"),
    DPI_FIELD_D(EM_SNMP_O_NAME19,                YA_FT_STRING,                "O_Name19"),
    DPI_FIELD_D(EM_SNMP_O_VALUE19,               YA_FT_STRING,                "O_Value19"),
    DPI_FIELD_D(EM_SNMP_OIDS,                    EM_F_TYPE_STRING,            "OID"),
    DPI_FIELD_D(EM_SNMP_OBJ_VALS,                EM_F_TYPE_STRING,            "objVal"),
    DPI_FIELD_D(EM_SNMP_OBJ_SYNS,                YA_FT_UINT32,                "ObjSyn"),
    DPI_FIELD_D(EM_SNMP_OBJ_NAMES,               EM_F_TYPE_STRING,            "objectname"),

};

#define  SNPRINTF(buff,len,pos,str_ptr)   \
            if(pos==0){                            \
                pos += snprintf(buff+pos, len - pos, "%s", str_ptr);  \
            }else {                                      \
                pos += snprintf(buff+pos, len - pos, ",%s", str_ptr);  \
            }                                            \

static struct int_to_string val_syntax_values[] = {
  {BER_CLASS_UNI|(BER_UNI_TAG_INTEGER<<4), "Integer32"},
//  { BER_CLASS_APP|(SNMP_IPA<<4), ""},
  { BER_CLASS_APP|(SNMP_CNT<<4), "Counter32"},
  { BER_CLASS_APP|(SNMP_GGE<<4), "Gauge32"},
  { BER_CLASS_APP|(SNMP_TIT<<4), "Timeticks"},
  { BER_CLASS_APP|(SNMP_OPQ<<4), "Opaque"},
  { BER_CLASS_APP|(SNMP_NSP<<4), "NSAP"},
  { BER_CLASS_APP|(SNMP_C64<<4), "Counter64"},
  { BER_CLASS_APP|(SNMP_U32<<4), "Unsigned32"},
  { 0,                               NULL }
};

/*
 *  dissect_snmp_VarBind
 *  this routine dissects variable bindings, looking for the oid information in our oid reporsitory
 *  to format and add the value adequatelly.
 *
 * The choice to handwrite this code instead of using the asn compiler is to avoid having tons
 * of uses of global variables distributed in very different parts of the code.
 * Other than that there's a cosmetic thing: the tree from ASN generated code would be so
 * convoluted due to the nesting of CHOICEs in the definition of VarBind/value.
 *
 * XXX: the length of this function (~400 lines) is an aberration!
 *  oid_key_t:key_type could become a series of callbacks instead of an enum
 *  the (! oid_info_is_ok) switch could be made into an array (would be slower)
 *

    NetworkAddress ::=  CHOICE { internet IpAddress }
    IpAddress ::= [APPLICATION 0] IMPLICIT OCTET STRING (SIZE (4))
    TimeTicks ::= [APPLICATION 3] IMPLICIT INTEGER (0..4294967295)
    Integer32 ::= INTEGER (-2147483648..2147483647)
    ObjectName ::= OBJECT IDENTIFIER
    Counter32 ::= [APPLICATION 1] IMPLICIT INTEGER (0..4294967295)
    Gauge32 ::= [APPLICATION 2] IMPLICIT INTEGER (0..4294967295)
    Unsigned32 ::= [APPLICATION 2] IMPLICIT INTEGER (0..4294967295)
    Integer-value ::=  INTEGER (-2147483648..2147483647)
    Integer32 ::= INTEGER (-2147483648..2147483647)
    ObjectID-value ::= OBJECT IDENTIFIER
    Empty ::= NULL
    TimeTicks ::= [APPLICATION 3] IMPLICIT INTEGER (0..4294967295)
    Opaque ::= [APPLICATION 4] IMPLICIT OCTET STRING
    Counter64 ::= [APPLICATION 6] IMPLICIT INTEGER (0..18446744073709551615)

    ObjectSyntax ::= CHOICE {
         simple SimpleSyntax,
         application-wide ApplicationSyntax
    }

    SimpleSyntax ::= CHOICE {
       integer-value Integer-value,
       string-value String-value,
       objectID-value ObjectID-value,
       empty  Empty
    }

    ApplicationSyntax ::= CHOICE {
       ipAddress-value IpAddress,
       counter-value Counter32,
       timeticks-value TimeTicks,
       arbitrary-value Opaque,
       big-counter-value Counter64,
       unsigned-integer-value Unsigned32
    }

    ValueType ::=  CHOICE {
       value ObjectSyntax,
       unSpecified NULL,
       noSuchObject[0] IMPLICIT NULL,
       noSuchInstance[1] IMPLICIT NULL,
       endOfMibView[2] IMPLICIT NULL
    }

    VarBind ::= SEQUENCE {
       name ObjectName,
       valueType ValueType
    }

 */


#define F_SNMP_ENGINEID_CONFORM 0x80
#define SNMP_ENGINEID_RFC1910 0x00
#define SNMP_ENGINEID_RFC3411 0x01

static const char* tfs_snmp_engineid_conform[] = {
    "RFC3411 (SNMPv3)",
    "RFC1910 (Non-SNMPv3)"
};

#define SNMP_ENGINEID_FORMAT_IPV4 0x01
#define SNMP_ENGINEID_FORMAT_IPV6 0x02
#define SNMP_ENGINEID_FORMAT_MACADDRESS 0x03
#define SNMP_ENGINEID_FORMAT_TEXT 0x04
#define SNMP_ENGINEID_FORMAT_OCTETS 0x05

static const struct int_to_string snmp_engineid_format_vals[] = {
    { SNMP_ENGINEID_FORMAT_IPV4,    "IPv4 address" },
    { SNMP_ENGINEID_FORMAT_IPV6,    "IPv6 address" },
    { SNMP_ENGINEID_FORMAT_MACADDRESS,    "MAC address" },
    { SNMP_ENGINEID_FORMAT_TEXT,    "Text, administratively assigned" },
    { SNMP_ENGINEID_FORMAT_OCTETS,    "Octets, administratively assigned" },
    { 0,    NULL }
};

#define SNMP_ENGINEID_CISCO_AGENT 0x00
#define SNMP_ENGINEID_CISCO_MANAGER 0x01

static const struct int_to_string snmp_engineid_cisco_type_vals[] = {
    { SNMP_ENGINEID_CISCO_AGENT,    "Agent" },
    { SNMP_ENGINEID_CISCO_MANAGER,    "Manager" },
    { 0,    NULL }
};


static int dissect_snmp_engineid(const uint8_t *payload, uint32_t remain_len, uint32_t offset, st_snmp_info *info) {

    uint32_t enterpriseid;
    time_t seconds;
    int len_remain = remain_len;

    /* first bit: engine id conformance */
    if (len_remain < 1)
        return offset;

    info->engine_id_conform = (payload[offset] >> 7) & 0x01;

    /* 4-byte enterprise number/name */
    info->engine_enteprise_id = get_uint32_ntohl(payload, offset);
    if (info->engine_id_conform)
        info->engine_id_conform -= 0x80000000; /* ignore first bit */

    offset += 4;
    len_remain -= 4;

    switch (info->engine_id_conform) {
    case SNMP_ENGINEID_RFC1910:
        /* 12-byte AgentID w/ 8-byte trailer */
        if (len_remain == 8) {
            offset += 8;
            len_remain -= 8;
        }
        else {
            // ei_snmp_rfc1910_non_conformant len_remain
            return offset;
        }
        break;
    case SNMP_ENGINEID_RFC3411: /* variable length: 5..32 */
        /* 1-byte format specifier */
        if (len_remain < 1)
            return offset;
        info->engine_id_format = payload[offset];
        offset += 1;
        len_remain -= 1;

        switch (info->engine_id_format) {
        case SNMP_ENGINEID_FORMAT_IPV4: /* 4-byte IPv4 address */
            if (len_remain == 4) {
                info->engine_id_ipv4 = get_uint32_ntohl(payload, offset);
                offset += 4;
                len_remain -= 4;
            }
            break;
        case SNMP_ENGINEID_FORMAT_IPV6: /* 16-byte IPv6 address */
            if (len_remain == 16) {
                memcpy(info->engine_id_ipv6, payload + offset, 16);
                offset += 16;
                len_remain -= 16;
            }
            break;
        case SNMP_ENGINEID_FORMAT_MACADDRESS:
            /* See: https://supportforums.cisco.com/message/3010617#3010617 for details. */
            if (info->engine_enteprise_id == 9 && len_remain == 7) {
                info->engine_id_cisco_type = payload[offset];
                offset++;
                len_remain--;
            }
            /* 6-byte MAC address */
            if (len_remain == 6) {
                memcpy(info->engine_id_mac, payload + offset, 6);
                offset += 6;
                len_remain -= 6;
            }
            break;
        case SNMP_ENGINEID_FORMAT_TEXT:
            /* max. 27-byte string, administratively assigned */
            if (len_remain <= 27) {
                memcpy(info->engine_id_text, payload + offset, len_remain);
                offset += len_remain;
                len_remain = 0;
            }
            break;
        case 128:
            /* most common enterprise-specific format: (ucd|net)-snmp random */
            if (info->engine_enteprise_id == 2021 || info->engine_enteprise_id == 8072) {
                /* demystify: 4B random, 4B/8B epoch seconds */
                if (len_remain == 8 || len_remain == 12) {
                    memcpy(info->engine_id_data, payload + offset, 4);
                    info->engine_id_datalen = 4;
                    if (len_remain == 8)
                        info->engine_id_time = (uint64_t)get_uint32_ntohl(payload, offset + 4);
                    else
                        info->engine_id_time = get_uint64_t(payload, offset + 4);

                    offset += len_remain;
                    len_remain = 0;
                }
            }
            break;
        /* fall through */
        case SNMP_ENGINEID_FORMAT_OCTETS:
        default:
            /* max. 27 bytes, administratively assigned or unknown format */
            if (len_remain <= 27) {
                memcpy(info->engine_id_data, payload + offset, len_remain);
                info->engine_id_datalen = len_remain;
                offset += len_remain;
                len_remain = 0;
            }
            break;
        }
        break;
    }

    if (len_remain > 0) {
        offset += len_remain;
    }

    return offset;
}
static int
dissect_snmp_VarBind(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    int seq_offset, name_offset, value_offset, value_start;
    uint32_t seq_len, name_len, value_len;
    uint8_t ber_class, pc;
    uint32_t tag;
    uint8_t ind;
    uint32_t *subids;
    uint8_t *oid_bytes;
    uint32_t oid_matched, oid_left;
    char str[256];
    int ret;
    const char *syntax_ptr;

    seq_offset = offset;

    //边界安全
    if(snmp_info->oid_count >= SNMP_OID_NUM)
    {
        return offset;
    }

    /* first have the VarBind's sequence header */
    offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
    offset = dpi_ber_length(pkt, offset, &seq_len, &ind);

    if (!pc && ber_class == BER_CLASS_UNI && tag == BER_UNI_TAG_SEQUENCE) {
        // dissect_unknown_ber
        return offset;
    }

    if (ind) {
        // dissect_unknown_ber
        return offset;
    }

    seq_len += offset - seq_offset;

    /* then we have the ObjectName's header */
    offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
    offset = name_offset = dpi_ber_length(pkt, offset, &name_len, &ind);
    snmp_info->oid_list[snmp_info->oid_count].name_type = tag;

    memset(str, 0, sizeof(str));
    ret = encode_oid_data(str, (const char *)(pkt->payload + name_offset), name_len);
    if (ret != 0) {
        if (!strcmp(str, "1.3.6.1.2.1.1.1.0")) {
            memset(snmp_info->sysDesc, 0, sizeof(snmp_info->sysDesc));
            strcpy(snmp_info->sysDesc, "1.3.6.1.2.1.1.1.0");
        }
        else if (!strcmp(str, "1.3.6.1.2.1.1.5.0")) {
            memset(snmp_info->sysName, 0, sizeof(snmp_info->sysName));
            strcpy(snmp_info->sysName, "1.3.6.1.2.1.1.5.0");
        }
    }

    if (!(!pc && ber_class == BER_CLASS_UNI && tag == BER_UNI_TAG_OID)) {
        // dissect_unknown_ber
        return seq_offset + seq_len;
    }

    if (ind) {
        // dissect_unknown_ber
        return seq_offset + seq_len;
    }

    offset += name_len;
    value_start = offset;

    /* then we have the value's header */
    offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
    value_offset = dpi_ber_length(pkt, offset, &value_len, &ind);
    //用于区分value类型
    snmp_info->oid_list[snmp_info->oid_count].val_type = tag;
    snmp_info->oid_list[snmp_info->oid_count].ber_class = ber_class;

    syntax_ptr = val_to_string(ber_class|(tag<<4), val_syntax_values);
    if(syntax_ptr)
        memcpy(snmp_info->oid_list[snmp_info->oid_count].syntax, syntax_ptr, strlen(syntax_ptr));

    if (!(!pc)) {
        // dissect_unknown_ber
        return seq_offset + seq_len;
    }

    if (snmp_info->oid_count >= SNMP_OID_NUM)
        return seq_offset + seq_len;

    int min_len = DPI_MIN(name_len, sizeof(snmp_info->oid_list[snmp_info->oid_count].obj_name)-1);
    if (min_len > 0) {
        memcpy(snmp_info->oid_list[snmp_info->oid_count].obj_name, pkt->payload + name_offset, min_len);
        snmp_info->oid_list[snmp_info->oid_count].obj_name[min_len + 1] = '\0';
        snmp_info->oid_list[snmp_info->oid_count].name_len = min_len;
    }

    min_len = DPI_MIN(value_len, sizeof(snmp_info->oid_list[snmp_info->oid_count].value)-1);
    if (min_len) {
        memcpy(snmp_info->oid_list[snmp_info->oid_count].value, pkt->payload + value_offset, min_len);
        snmp_info->oid_list[snmp_info->oid_count].value[min_len + 1] = '\0';
        snmp_info->oid_list[snmp_info->oid_count].val_len = min_len;
    }

    snmp_info->oid_count++;

    return seq_offset + seq_len;
}

static const ber_sequence_t VarBindList_sequence_of[1] = {
  { BER_CLASS_UNI, BER_UNI_TAG_SEQUENCE, BER_FLAGS_NOOWNTAG, dissect_snmp_VarBind },
};


static int
dissect_snmp_VarBindList(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_sequence_of(implicit_tag, pkt, offset, VarBindList_sequence_of, ctx);

    return offset;
}



/****************************************************** header ************************************************************/
/* Version */
/* Community */
/* Error */
/* Error_Index */

static int
dissect_snmp_Version(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

//    snmp_info->version = pkt->payload[offset] & 0x0F;
    uint32_t v32;
    offset = dpi_ber_integer(implicit_tag, pkt, offset, &v32);
    snmp_info->version = v32;
    return offset;
}

static int
dissect_snmp_Community(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    uint8_t        ber_class;
    uint8_t        pc, ind;
    uint32_t    tag;
    uint32_t    len;
    int            end_offset;

    /* read header and len for the octet string */
    offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
    offset = dpi_ber_length(pkt, offset, &len, &ind);
    end_offset = offset + len;

    int show_cnt = (len > (sizeof(snmp_info->community) - 1) ? (sizeof(snmp_info->community) - 1) : len);

    memcpy(snmp_info->community, pkt->payload + offset, show_cnt);

    return end_offset;
}


static int
dissect_snmp_request_id(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;
    int64_t v64;
    offset = dpi_ber_integer64(implicit_tag, pkt, offset, &v64);
    snmp_info->request_id = (uint64_t)v64;

    return offset;
}


static const struct int_to_string snmp_T_error_status_vals[] = {
  {   0, "noError" },
  {   1, "tooBig" },
  {   2, "noSuchName" },
  {   3, "badValue" },
  {   4, "readOnly" },
  {   5, "genErr" },
  {   6, "noAccess" },
  {   7, "wrongType" },
  {   8, "wrongLength" },
  {   9, "wrongEncoding" },
  {  10, "wrongValue" },
  {  11, "noCreation" },
  {  12, "inconsistentValue" },
  {  13, "resourceUnavailable" },
  {  14, "commitFailed" },
  {  15, "undoFailed" },
  {  16, "authorizationError" },
  {  17, "notWritable" },
  {  18, "inconsistentName" },
  { 0, NULL }
};


static int
dissect_snmp_T_error_status(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;
    uint32_t v32;
    offset = dpi_ber_integer(implicit_tag, pkt, offset, &v32);
    snmp_info->error_status = v32;
    snmp_info->error_status_f = 1;
    return offset;
}

static int
dissect_snmp_error_index(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;
    uint32_t v32;
    offset = dpi_ber_integer(implicit_tag, pkt, offset, &v32);
    snmp_info->error_index = v32;
    return offset;
}


static const ber_sequence_t PDU_sequence[] = {
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_request_id }, // request id
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_T_error_status }, // error status
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_error_index },    // error index
  { BER_CLASS_UNI, BER_UNI_TAG_SEQUENCE, BER_FLAGS_NOOWNTAG, dissect_snmp_VarBindList }, // variable binding
  { 0, 0, 0, NULL }
};

/******************************************************end  header ************************************************************/



static int
dissect_snmp_PDU(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_sequence(implicit_tag, pkt, offset, PDU_sequence, ctx);

    return offset;
}


static int
dissect_snmp_GetRequest_PDU(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_tagged_type(implicit_tag, pkt, offset, 1, ctx, dissect_snmp_PDU);

    return offset;
}

static int
dissect_snmp_GetNextRequest_PDU(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_tagged_type(implicit_tag, pkt, offset, 1, ctx, dissect_snmp_PDU);

    return offset;
}


static int
dissect_snmp_GetResponse_PDU(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_tagged_type(implicit_tag, pkt, offset, 2, ctx, dissect_snmp_PDU);

    return offset;
}


static int
dissect_snmp_SetRequest_PDU(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_tagged_type(implicit_tag, pkt, offset, 4, ctx, dissect_snmp_PDU);

    return offset;
}



/********************************************** start Trap PDU ************************************************/
static int
dissect_snmp_EnterpriseOID(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    offset = (uint32_t)dpi_ber_byte_string(implicit_tag, pkt, offset, snmp_info->enterprise_oid, sizeof(snmp_info->enterprise_oid), &snmp_info->enterprise_oid_len);
    return offset;
}


static int
dissect_snmp_OCTET_STRING_SIZE_4(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    uint8_t        ber_class;
    uint8_t        pc, ind;
    uint32_t    tag;
    uint32_t    len;
    int            end_offset;

    /* read header and len for the octet string */
    offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
    offset = dpi_ber_length(pkt, offset, &len, &ind);
    end_offset = offset + len;

    int show_cnt = (len > (sizeof(snmp_info->net_address) - 1) ? (sizeof(snmp_info->net_address) -1) : len);

    memcpy(snmp_info->net_address, pkt->payload + offset, show_cnt);
    snmp_info->net_address_len = show_cnt;

    return end_offset;
}

static int
dissect_snmp_NetworkAddress(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_tagged_type(implicit_tag, pkt, offset, 1, ctx, dissect_snmp_OCTET_STRING_SIZE_4);

    return offset;

#if 0
    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    uint8_t        ber_class;
    uint8_t        pc, ind;
    uint32_t    tag;
    uint32_t    len;
    int            end_offset;

    /* read header and len for the octet string */
    offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
    offset = dpi_ber_length(pkt, offset, &len, &ind);
    end_offset = offset + len;

    int show_cnt = (len > (sizeof(snmp_info->net_address) - 1) ? (sizeof(snmp_info->net_address) - 1) : len);

    memcpy(snmp_info->net_address, pkt->payload + offset, show_cnt);
    snmp_info->net_address_len = show_cnt;

    return end_offset;
#endif
}

static const struct int_to_string snmp_GenericTrap_vals[] = {
  {   0, "coldStart" },
  {   1, "warmStart" },
  {   2, "linkDown" },
  {   3, "linkUp" },
  {   4, "authenticationFailure" },
  {   5, "egpNeighborLoss" },
  {   6, "enterpriseSpecific" },
  { 0, NULL }
};

static int
dissect_snmp_GenericTrap(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    offset = dpi_ber_integer(implicit_tag, pkt, offset, &snmp_info->general_trap);
    snmp_info->general_trap_f = 1;

    return offset;
}


static int
dissect_snmp_SpecificTrap(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    offset = dpi_ber_integer(implicit_tag, pkt, offset, &snmp_info->special_trap);
    snmp_info->special_trap_f = 1;

    return offset;
}

static int
dissect_snmp_Integer32(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;


    return offset;
}



static int
dissect_snmp_INTEGER_0_4294967295(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    uint32_t v32;
    offset = dpi_ber_integer(implicit_tag, pkt, offset, &v32);
    snmp_info->time_stamp = v32;

    return offset;
}

static int
dissect_snmp_TimeTicks(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_tagged_type(implicit_tag, pkt, offset, 3, ctx, dissect_snmp_INTEGER_0_4294967295);

    return offset;
}

static const ber_sequence_t Trap_PDU_U_sequence[] = {
  {BER_CLASS_UNI, BER_UNI_TAG_OID,      BER_FLAGS_NOOWNTAG, dissect_snmp_EnterpriseOID },
  {BER_CLASS_APP, 0,                    BER_FLAGS_NOOWNTAG, dissect_snmp_NetworkAddress },
  {BER_CLASS_UNI, BER_UNI_TAG_INTEGER,  BER_FLAGS_NOOWNTAG, dissect_snmp_GenericTrap },
  {BER_CLASS_UNI, BER_UNI_TAG_INTEGER,  BER_FLAGS_NOOWNTAG, dissect_snmp_SpecificTrap },
  {BER_CLASS_APP, 3,                    BER_FLAGS_NOOWNTAG, dissect_snmp_TimeTicks },
  {BER_CLASS_UNI, BER_UNI_TAG_SEQUENCE, BER_FLAGS_NOOWNTAG, dissect_snmp_VarBindList },
  { 0, 0, 0, NULL }
};


static int
dissect_snmp_Trap_PDU_U(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_sequence(implicit_tag, pkt, offset, Trap_PDU_U_sequence, ctx);

    return offset;
}


static int
dissect_snmp_Trap_PDU(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_tagged_type(implicit_tag, pkt, offset, 4, ctx, dissect_snmp_Trap_PDU_U);

    return offset;
}
/**********************************************end Trap PDU ************************************************/





/********************************************** GetBulkRequest ************************************************/
static int
dissect_snmp_non_repeaters(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;
    uint32_t v32;
    offset = dpi_ber_integer(implicit_tag, pkt, offset, &v32);

    return offset;
}


static int
dissect_snmp_max_repetitions(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;
    uint32_t v32;
    offset = dpi_ber_integer(implicit_tag, pkt, offset, &v32);

    return offset;
}


static const ber_sequence_t BulkPDU_sequence[] = {
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_request_id },
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_non_repeaters },
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_max_repetitions },
  { BER_CLASS_UNI, BER_UNI_TAG_SEQUENCE, BER_FLAGS_NOOWNTAG, dissect_snmp_VarBindList },
  {  0, 0, 0, NULL }
};


static int
dissect_snmp_BulkPDU(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_sequence(implicit_tag, pkt, offset, BulkPDU_sequence, ctx);

    return offset;
}


static int
dissect_snmp_GetBulkRequest_PDU(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_tagged_type(implicit_tag, pkt, offset, 5, ctx, dissect_snmp_BulkPDU);

    return offset;
}
/********************************************** GetBulkRequest ************************************************/



static int
dissect_snmp_InformRequest_PDU(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_tagged_type(implicit_tag, pkt, offset, 6, ctx, dissect_snmp_PDU);

    return offset;
}


static int
dissect_snmp_SNMPv2_Trap_PDU(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_tagged_type(implicit_tag, pkt, offset, 7, ctx, dissect_snmp_PDU);

    return offset;
}


static int
dissect_snmp_Report_PDU(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_tagged_type(implicit_tag, pkt, offset, 8, ctx, dissect_snmp_PDU);

    return offset;
}


static const ber_choice_t PDUs_choice[] = {
  {   0, BER_CLASS_CON, 0, BER_FLAGS_NOOWNTAG, dissect_snmp_GetRequest_PDU },
  {   1, BER_CLASS_CON, 1, BER_FLAGS_NOOWNTAG, dissect_snmp_GetNextRequest_PDU },
  {   2, BER_CLASS_CON, 2, BER_FLAGS_NOOWNTAG, dissect_snmp_GetResponse_PDU },
  {   3, BER_CLASS_CON, 3, BER_FLAGS_NOOWNTAG, dissect_snmp_SetRequest_PDU },
  {   4, BER_CLASS_CON, 4, BER_FLAGS_NOOWNTAG, dissect_snmp_Trap_PDU },
  {   5, BER_CLASS_CON, 5, BER_FLAGS_NOOWNTAG, dissect_snmp_GetBulkRequest_PDU },
  {   6, BER_CLASS_CON, 6, BER_FLAGS_NOOWNTAG, dissect_snmp_InformRequest_PDU },
  {   7, BER_CLASS_CON, 7, BER_FLAGS_NOOWNTAG, dissect_snmp_SNMPv2_Trap_PDU },
  {   8, BER_CLASS_CON, 8, BER_FLAGS_NOOWNTAG, dissect_snmp_Report_PDU },
  {   0, 0, 0, 0, NULL }
};



static int
dissect_snmp_PDUs(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {
    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;
    int pdu_type = -1;

    offset = dpi_ber_choice(pkt, offset, PDUs_choice, ctx, &pdu_type);
    snmp_info->pdu_type = pdu_type;
    return offset;
}

static const ber_sequence_t Message_sequence[] = {
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_Version },
  { BER_CLASS_UNI, BER_UNI_TAG_OCTETSTRING, BER_FLAGS_NOOWNTAG, dissect_snmp_Community },
  { BER_CLASS_ANY/*choice*/, -1/*choice*/, BER_FLAGS_NOOWNTAG|BER_FLAGS_NOTCHKTAG, dissect_snmp_PDUs },
  { 0, 0, 0, NULL }
};


static int
dissect_snmp_message(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_sequence(implicit_tag, pkt, offset, Message_sequence, ctx);

    return offset;
}


/************************************************************  version 2u *******************************************************************************/

static int
dissect_snmp_Parameters(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;
    int len;
    offset = dpi_ber_byte_string(implicit_tag, pkt, offset, snmp_info->params, sizeof(snmp_info->params), &len);

    return offset;
}


static int
dissect_snmp_Encrypted(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    uint8_t        ber_class;
    uint8_t        pc, ind;
    uint32_t    tag;
    uint32_t    len;
    int            end_offset;

    /* read header and len for the octet string */
    offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
    offset = dpi_ber_length(pkt, offset, &len, &ind);
    end_offset = offset + len;

    int show_cnt = (len > (sizeof(snmp_info->encrypted) - 1) ? (sizeof(snmp_info->encrypted) - 1) : len);

    memcpy(snmp_info->encrypted, pkt->payload + offset, show_cnt);

    return end_offset;
}


static const ber_choice_t T_datav2u_choice[] = {
  {   0, BER_CLASS_ANY/*choice*/, -1/*choice*/, BER_FLAGS_NOOWNTAG, dissect_snmp_PDUs },
  {   1, BER_CLASS_UNI, BER_UNI_TAG_OCTETSTRING, BER_FLAGS_NOOWNTAG, dissect_snmp_Encrypted },
  {   0, 0, 0, 0, NULL }
};


static int
dissect_snmp_T_datav2u(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_choice(pkt, offset, T_datav2u_choice, ctx, NULL);

    return offset;
}

static const ber_sequence_t Messagev2u_sequence[] = {
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_Version },
  { BER_CLASS_UNI, BER_UNI_TAG_OCTETSTRING, BER_FLAGS_NOOWNTAG, dissect_snmp_Parameters },
  { BER_CLASS_ANY/*choice*/, -1/*choice*/, BER_FLAGS_NOOWNTAG|BER_FLAGS_NOTCHKTAG, dissect_snmp_T_datav2u },
  { 0, 0, 0, NULL }
};


static int
dissect_snmp_messagev2u(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_sequence(implicit_tag, pkt, offset, Messagev2u_sequence, ctx);

    return offset;
}



/************************************************************  version 3 *******************************************************************************/

static int
dissect_snmp_T_msgAuthoritativeEngineID(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    int len;
    char str[2048];
    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    memset(str, 0, sizeof(str));
    offset = dpi_ber_byte_string(implicit_tag, pkt, offset, str, sizeof(str), &len);
    if (len > 0) {
        snmp_info->auth_engine_id_len = len;
        memcpy(snmp_info->auth_engine_id, str, len);
        pkt->payload += (pkt->payload_len - len);
        pkt->payload_len = len;
        offset = dissect_snmp_engineid(pkt->payload, len, 0, snmp_info);
    }

    return offset;
}



static int
dissect_snmp_T_msgAuthoritativeEngineBoots(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;
    uint32_t v32;
    offset = dpi_ber_integer(implicit_tag, pkt, offset, &v32);
    snmp_info->msg_engine_boots = v32 & 0xff;

    return offset;
}


static int
dissect_snmp_T_msgAuthoritativeEngineTime(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    offset = dpi_ber_integer(implicit_tag, pkt, offset, &snmp_info->msg_engine_time);

    return offset;
}

static int
dissect_snmp_T_msgUserName(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;
    int len;
    offset = dpi_ber_byte_string(implicit_tag, pkt, offset, snmp_info->msg_username, sizeof(snmp_info->msg_username), &len);

    return offset;
}


static int
dissect_snmp_T_msgAuthenticationParameters(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;
    int len;
    offset = dpi_ber_byte_string(implicit_tag, pkt, offset, snmp_info->msg_auth_params, sizeof(snmp_info->msg_auth_params), &len);

    return offset;
}



static int
dissect_snmp_T_msgPrivacyParameters(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;
    int len;
    offset = dpi_ber_byte_string(implicit_tag, pkt, offset, snmp_info->msg_privacy_parmas, sizeof(snmp_info->msg_privacy_parmas), &len);

    return offset;
}


static const ber_sequence_t UsmSecurityParameters_sequence[] = {
  { BER_CLASS_UNI, BER_UNI_TAG_OCTETSTRING, BER_FLAGS_NOOWNTAG, dissect_snmp_T_msgAuthoritativeEngineID },  //msgAuthoritativeEngineID
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_T_msgAuthoritativeEngineBoots },   //msgAuthoritativeEngineBoots
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_T_msgAuthoritativeEngineTime },    //msgAuthoritativeEngineTime
  { BER_CLASS_UNI, BER_UNI_TAG_OCTETSTRING, BER_FLAGS_NOOWNTAG, dissect_snmp_T_msgUserName },               //msgUserName
  { BER_CLASS_UNI, BER_UNI_TAG_OCTETSTRING, BER_FLAGS_NOOWNTAG, dissect_snmp_T_msgAuthenticationParameters }, //msgAuthenticationParameters
  { BER_CLASS_UNI, BER_UNI_TAG_OCTETSTRING, BER_FLAGS_NOOWNTAG, dissect_snmp_T_msgPrivacyParameters }, //msgPrivacyParameters
  { 0, 0, 0, NULL }
};


static int
dissect_snmp_UsmSecurityParameters(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_sequence(implicit_tag, pkt, offset, UsmSecurityParameters_sequence, ctx);

    return offset;
}


static int
dissect_snmp_INTEGER_0_2147483647(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    offset = dpi_ber_integer(implicit_tag, pkt, offset, &snmp_info->msg_id);
    snmp_info->msg_head_f = 1;

    return offset;
}

static int
dissect_snmp_INTEGER_484_2147483647(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    offset = dpi_ber_integer(implicit_tag, pkt, offset, &snmp_info->msg_max_size);

    return offset;
}


static int
dissect_snmp_T_msgFlags(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    uint8_t        ber_class;
    uint8_t        pc, ind;
    uint32_t    tag;
    uint32_t    len;
    int            end_offset;

    /* read header and len for the octet string */
    offset = dpi_ber_identifier(pkt, offset, &ber_class, &pc, &tag);
    offset = dpi_ber_length(pkt, offset, &len, &ind);
    end_offset = offset + len;

    if (len > 0) {
        uint8_t flag = (uint8_t)pkt->payload[offset];
        snmp_info->msg_flags = flag;
        snmp_info->msg_auth_flag = flag & 0x01;
        snmp_info->msg_encrypt_flag = (flag & 0x02) >> 1;
        snmp_info->msg_report_flag = (flag & 0x04) >> 2;
    }

    return end_offset;
}


static int
dissect_snmp_T_msgSecurityModel(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    offset = dpi_ber_integer(implicit_tag, pkt, offset, &snmp_info->msg_security_mode);

    return offset;
}


static const ber_sequence_t HeaderData_sequence[] = {
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_INTEGER_0_2147483647 },   //msgID
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_INTEGER_484_2147483647 },  //msgMaxSize
  { BER_CLASS_UNI, BER_UNI_TAG_OCTETSTRING, BER_FLAGS_NOOWNTAG, dissect_snmp_T_msgFlags },    //msgFlags
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_T_msgSecurityModel },   //msgSecurityModel
  { 0, 0, 0, NULL }
};

static int
dissect_snmp_HeaderData(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_sequence(implicit_tag, pkt, offset, HeaderData_sequence, ctx);

    return offset;
}


static int
dissect_snmp_T_msgSecurityParameters(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    int len;
    char str[2048];
    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    switch (snmp_info->msg_security_mode){
    case SNMP_SEC_USM: // 3
        offset = dpi_ber_identifier(pkt, offset, NULL, NULL, NULL);
        offset = dpi_ber_length(pkt, offset, NULL, NULL);
        offset = dissect_snmp_UsmSecurityParameters(implicit_tag, pkt, offset, ctx);
        break;
    case SNMP_SEC_ANY:    /* 0 */
    case SNMP_SEC_V1:    /* 1 */
    case SNMP_SEC_V2C:    /* 2 */
    default:
        offset = dpi_ber_byte_string(implicit_tag, pkt, offset, str, sizeof(str), &len);
        break;
    }

    return offset;
}


static int
dissect_snmp_SnmpEngineID(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {
    int len;
    char str[2048];
    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;

    offset = dpi_ber_byte_string(implicit_tag, pkt, offset, str, sizeof(str), &len);
    if (len > 0) {
        pkt->payload += (pkt->payload_len - len);
        pkt->payload_len = len;
        offset = dissect_snmp_engineid(pkt->payload, len, 0, snmp_info);
    }

    return offset;
}


static int
dissect_snmp_contextName(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;
    int len;
    offset = dpi_ber_byte_string(implicit_tag, pkt, offset, snmp_info->context_name, sizeof(snmp_info->context_name), &len);

    return offset;
}


static const ber_sequence_t ScopedPDU_sequence[] = {
  { BER_CLASS_UNI, BER_UNI_TAG_OCTETSTRING, BER_FLAGS_NOOWNTAG, dissect_snmp_SnmpEngineID },  //contextEngineID
  { BER_CLASS_UNI, BER_UNI_TAG_OCTETSTRING, BER_FLAGS_NOOWNTAG, dissect_snmp_contextName },  //contextName
  { BER_CLASS_ANY/*choice*/, -1/*choice*/, BER_FLAGS_NOOWNTAG|BER_FLAGS_NOTCHKTAG, dissect_snmp_PDUs },  //data
  { 0, 0, 0, NULL }
};

static int
dissect_snmp_ScopedPDU(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_sequence(implicit_tag, pkt, offset, ScopedPDU_sequence, ctx);

    return offset;
}


static int
dissect_snmp_T_encryptedPDU(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    st_snmp_info *snmp_info = (st_snmp_info *)ctx->info;


    return offset;
}


static const ber_choice_t ScopedPduData_choice[] = {
  {   0, BER_CLASS_UNI, BER_UNI_TAG_SEQUENCE, BER_FLAGS_NOOWNTAG, dissect_snmp_ScopedPDU },  // plaintext
  {   1, BER_CLASS_UNI, BER_UNI_TAG_OCTETSTRING, BER_FLAGS_NOOWNTAG, dissect_snmp_T_encryptedPDU }, //encryptedPDU
  {   0, 0, 0, 0, NULL }
};

static int
dissect_snmp_ScopedPduData(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_choice(pkt, offset, ScopedPduData_choice, ctx, NULL);

    return offset;
}


static const ber_sequence_t SNMPv3Message_sequence[] = {
  { BER_CLASS_UNI, BER_UNI_TAG_INTEGER, BER_FLAGS_NOOWNTAG, dissect_snmp_Version },
  { BER_CLASS_UNI, BER_UNI_TAG_SEQUENCE, BER_FLAGS_NOOWNTAG, dissect_snmp_HeaderData },  //msgGlobalData
  { BER_CLASS_UNI, BER_UNI_TAG_OCTETSTRING, BER_FLAGS_NOOWNTAG, dissect_snmp_T_msgSecurityParameters },  //msgSecurityParameters
  { BER_CLASS_ANY/*choice*/, -1/*choice*/, BER_FLAGS_NOOWNTAG|BER_FLAGS_NOTCHKTAG, dissect_snmp_ScopedPduData }, //msgData
  { 0, 0, 0, NULL }
};



static int
dissect_snmp_messagev3(uint8_t implicit_tag, struct dpi_pkt_st* pkt, uint32_t offset, ber_ctx_t* ctx) {

    offset = dpi_ber_sequence(implicit_tag, pkt, offset, SNMPv3Message_sequence, ctx);

    return offset;
}



static int
snmp_write_integer64_value(precord_t *record, int *idx, int max_len, const char *data, unsigned int data_len)
{
    uint64_t val=0;
    int i;
    if ((data_len < 1) || (data_len > 9) || ((data_len == 9) && (data[0] != 0))){
        write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, NULL, 0);
        return 0;
    }

    if (data_len > 0) {
        /* extend sign bit for signed fields */
        enum ftenum type  = FT_INT32; /* Default to signed, is this correct? */

        if (data[0] & 0x80 && IS_BER_FT_INT(type)) {
            val = -1;
        }


        for (i=0; i<(int)data_len; i++) {
            val = ((uint64_t)val<<8) | data[i];
        }
    }

    write_uint64_reconds(record, idx,TBL_LOG_MAX_LEN,val);

    return 0;
}


static int write_snmp_type_fields(precord_t *record, int *idx, int index,uint8_t code_type, const uint8_t *data,uint8_t  length)
{
    char __str[64] = {0};

    switch(code_type){
        case MR_SNMP_INT:     // int    0x02
        case MR_SNMP_TIMETICKS: // time ticks 0x43
            snmp_write_integer64_value(record, idx, TBL_LOG_MAX_LEN, (const char*)data, length);
            break;
        case MR_SNMP_OCTSTR:  // string  0x04
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, (const char*)data, length);
            break;
        case MR_SNMP_NULL:     // null   0x05
            write_multi_num_reconds(record, idx, TBL_LOG_MAX_LEN, data, length);
            break;
        case MR_SNMP_OBJID:  // obj id   0x06
            write_multi_num_reconds(record, idx, TBL_LOG_MAX_LEN,data, length);
            break;
        case MR_SNMP_IPADDR:  // ip address  0x40
            if(length==4){
                memset(__str,0,sizeof(__str));
                get_iparray_to_string(__str,sizeof(__str),data);
            }
            else if(length==16){
                memset(__str,0,sizeof(__str));
                get_ip6string(__str,sizeof(__str),data);
            }
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            break;

        default:
            write_multi_num_reconds(record, idx, TBL_LOG_MAX_LEN,data, length);
            break;
    }

    return 1;
}

int encode_oid_data(char *str,const char *data, uint16_t  oid_len)
{
    uint8_t is_first=1;
    uint32_t n=1;
    uint64_t subid = 0;
    uint32_t *subid_overflow;
    int i,j=0;
    uint32_t subids[64]={0};
    char buff[20]={0};
    uint16_t res_len,str_len=0;

    for(i=0;i<oid_len;i++){
        if(!(data[i]&0x80)){
            n++;
        }
    }
    subid_overflow=subids+n;

    if(n==1){return 0;}

    for(i=0;i<oid_len;i++){
        uint8_t byte = data[i];

        subid <<= 7;
        subid |= (byte&0x7F);

        if(byte & 0x80){
            continue;
        }
        if(is_first==1){
            uint32_t subid0=0;
            if(subid>=40){subid0++;subid-=40;}
            if(subid>=40){subid0++;subid-=40;}

            subids[j++] = subid0;
            is_first = 0;
        }

        //if(subid > 0xffffffff){
        //    return 0;
        //}
        if(j>=32){
            return 0;
        }

        subids[j++] = (uint32_t )subid;
        subid=0;
    }

    for(i=0;i<j;i++){
        snprintf(buff,20,"%u.",subids[i]);
        str_len+=strlen(buff);
        if(str_len>64){break;}
        strncat(str,buff, 20);
    }
    str[strlen(str)-1]='\0';

    return 1;
}

static void write_snmp_log(struct flow_info *flow, int direction, st_snmp_info *snmp_info, SdtMatchResult *match_result)
{
    int ret;
    char str[512];
    int i;
    int idx = 0;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return ;
    }

    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "snmp");


    for(i=0;i<EM_SNMP_TOTAL_OID+1;i++){
        memset(str, 0, sizeof(str));

        switch(snmp_field_array[i].index){
        case EM_SNMP_VERSION:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN,snmp_field_array[i].type, NULL,  snmp_info->version);
            break;
        case EM_SNMP_COMMUNITY:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->community, strlen(snmp_info->community));
            break;
        case EM_SNMP_PDU_TYPE:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN,snmp_field_array[i].type, NULL,  snmp_info->pdu_type);
            break;
        case EM_SNMP_REQUEST_ID:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN,snmp_field_array[i].type, NULL,  snmp_info->request_id);
            break;
        case EM_SNMP_ERROR_STATUS:
            if (snmp_info->error_status_f) {
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->error_status);
            }
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SNMP_ENTERPRISE:
            memset(str, 0, sizeof(str));
            ret = encode_oid_data(str, snmp_info->enterprise_oid, snmp_info->enterprise_oid_len);
            if (!ret)
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            else
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
            break;
        case EM_SNMP_AGENT_ADDR: {
            if (snmp_info->net_address_len > 0) {
                int ver = 0;
                if (snmp_info->net_address_len == 4)
                    ver = 4;
                else if (snmp_info->net_address_len == 16)
                    ver = 6;
                write_one_ip_reconds(log_ptr->record, &idx, ver, (const uint8_t *)snmp_info->net_address);
            }
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        }
        case EM_SNMP_GENERIC_TRAP: {
            if (snmp_info->general_trap_f) {
                memset(str, 0, sizeof(str));
                snprintf(str, sizeof(str), "%s(%d)", val_to_string(snmp_info->general_trap, snmp_GenericTrap_vals), snmp_info->general_trap);
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
            }
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        }
        case EM_SNMP_SPECIFIC_TRAP:
            if (snmp_info->special_trap_f)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->special_trap);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SNMP_TIME_STAMP:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->time_stamp);
            break;
        case EM_SNMP_IDENTIFIER:
            if (snmp_info->msg_head_f)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->msg_id);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SNMP_CONTEXT_NAME:
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->context_name, strlen(snmp_info->context_name));
            break;
        case EM_SNMP_CONTEXT_ENGINE:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->engine_enteprise_id);
            break;
        case EM_SNMP_MSG_DIGEST:
        case EM_SNMP_MSG_SALT:
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SNMP_MSG_FLAG_REPORT:
            if (snmp_info->msg_head_f)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (uint32_t)snmp_info->msg_report_flag);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SNMP_MSG_FLAG_ENCRYPT:
            if (snmp_info->msg_head_f)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (uint32_t)snmp_info->msg_encrypt_flag);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SNMP_MSG_FLAG_AUTH:
            if (snmp_info->msg_head_f)
                write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (uint32_t)snmp_info->msg_auth_flag);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SNMP_MSG_SECMODE:
            if (snmp_info->msg_head_f) {
                snprintf(str, sizeof(str), "%s(%d)", val_to_string(snmp_info->msg_security_mode, sec_models), snmp_info->msg_security_mode);
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
            }
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SNMP_ENGINE_BOOTS:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->msg_engine_boots);
            break;
        case EM_SNMP_ENGINE_TIME:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->msg_engine_time);
            break;
        case EM_SNMP_ENGINE_MAX_MSG_SIZE:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->msg_max_size);
            break;
        case EM_SNMP_ENGINE_ID:
            write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->auth_engine_id, snmp_info->auth_engine_id_len);
            break;
        case EM_SNMP_ENGINE_ID_FORMAT:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (uint32_t)snmp_info->engine_id_format);
            break;
        case EM_SNMP_ENGINE_ID_DATA:
            if (snmp_info->engine_id_datalen > 0)
                write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->engine_id_data, snmp_info->engine_id_datalen);
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SNMP_SYS_NAME:
            if ((int)strlen(snmp_info->sysName) > 0)
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->sysName, strlen(snmp_info->sysName));
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SNMP_SYS_DESC:
            if ((int)strlen(snmp_info->sysDesc) > 0)
                write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->sysDesc, strlen(snmp_info->sysDesc));
            else
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        case EM_SNMP_TOTAL_OID:
            write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (uint32_t)snmp_info->oid_count);
            break;
        default:
//            write_snmp_type_fields(log_ptr->record, &idx, i, snmp_info->snmp_data[i].code_type,snmp_info->snmp_data[i].data,snmp_info->snmp_data[i].length);
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        }
    }

    int pos_syns=0, pos_oids=0, pos_vals=0, pos_names=0;
    int obj_num = DPI_MIN(SNMP_OID_NUM, snmp_info->oid_count);
    for(i=0;i<obj_num;i++){
        // OID
        write_multi_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)snmp_info->oid_list[i].obj_name, snmp_info->oid_list[i].name_len);
        if (snmp_info->oid_list[i].ber_class==BER_CLASS_UNI && snmp_info->oid_list[i].val_type == BER_UNI_TAG_OID){
            memset(str, 0, sizeof(str));
            ret = encode_oid_data(str, snmp_info->oid_list[i].value, snmp_info->oid_list[i].val_len);

            if(ret && strlen(str))
            {
                SNPRINTF(snmp_info->oids, sizeof(snmp_info->oids)-1, pos_oids, str);
            }
        }

        // Syntax
        if ((int)strlen(snmp_info->oid_list[i].syntax) > 0){
            SNPRINTF(snmp_info->obj_syns, sizeof(snmp_info->obj_syns)-1, pos_syns, snmp_info->oid_list[i].syntax);
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
        } else
        {
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
        }

        // Object name
        memset(str, 0, sizeof(str));
        ret = encode_oid_data(str, snmp_info->oid_list[i].obj_name, snmp_info->oid_list[i].name_len);
        if (ret == 0)
            write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
        else{
            if(strlen(str) && strlen(snmp_info->oid_list[i].syntax))
            {
                SNPRINTF(snmp_info->obj_names, sizeof(snmp_info->obj_names)-1, pos_names, str);
            }
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
        }

        // Value
        uint8_t type = snmp_info->oid_list[i].val_type;
        if (type == MR_SNMP_INT || type == MR_SNMP_TIMETICKS)
            snmp_write_integer64_value(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->oid_list[i].value, strlen(snmp_info->oid_list[i].value));
        else
            write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->oid_list[i].value, strlen(snmp_info->oid_list[i].value));
        //只处理整型value
        if(val_to_string(snmp_info->oid_list[i].ber_class|(snmp_info->oid_list[i].val_type<<4), val_syntax_values)
        && snmp_info->oid_list[i].val_len > 0 && snmp_info->oid_list[i].val_len<=8){
            uint8_t value_num[8]={0};
            for(int j=0;j<snmp_info->oid_list[i].val_len;j++){
               value_num[snmp_info->oid_list[i].val_len - 1 - j] = snmp_info->oid_list[i].value[j];
            }
            memset(str, 0, sizeof(str));
            sprintf(str, "%lu",*((uint64_t*)value_num));
            SNPRINTF(snmp_info->obj_vals, sizeof(snmp_info->obj_vals)-1, pos_vals, str);
        }
    }

    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 4*(SNMP_OID_NUM - obj_num));


    if ((int)strlen(snmp_info->oids) > 0){
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->oids, strlen(snmp_info->oids));
    } else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    if ((int)strlen(snmp_info->obj_vals) > 0){
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->obj_vals, strlen(snmp_info->obj_vals));
    } else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    if ((int)strlen(snmp_info->obj_syns) > 0){
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->obj_syns, strlen(snmp_info->obj_syns));
    } else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);

    if ((int)strlen(snmp_info->obj_names) > 0){
        write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, snmp_info->obj_names, strlen(snmp_info->obj_names));
    } else
        write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);


    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_SNMP;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    log_ptr->proto_id = PROTOCOL_SNMP;
    log_ptr->flow = flow;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

}




static void
identify_snmp(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    UNUSED(payload);
    UNUSED(payload_len);
    uint16_t s_port = 0, d_port = 0;
    uint32_t offset=0;
    uint16_t u16=0;
    if (payload_len > 32 && payload[0] == 0x30) {
        switch (payload[1]) {
        case 0x81:
          offset = 3;
          break;
        case 0x82:
          offset = 4;
          break;
        default:
          if (payload[1] > 0x82) {
            goto excl;
          }
          offset = 2;
        }
        u16 = get_uint16_ntohs(payload, offset);

        if((u16 != 0x0201) && (u16 != 0x0204)) {
          goto excl;
        }

        if (payload[offset + 2] >= 0x04) {
          goto excl;
        }

        #if 0

        d_port = ntohs(flow->tuple.inner.port_dst);
        if(d_port==SNMP_PORT_DEF || s_port==SNMP_PORT_DEF2){
            flow->real_protocol_id = PROTOCOL_SNMP;
            return;
        }


        #else
        if (flow->packet_stage==0) {
            d_port = ntohs(flow->tuple.inner.port_dst);
            if(d_port==SNMP_PORT_DEF || d_port ==SNMP_PORT_DEF2){
                flow->real_protocol_id = PROTOCOL_SNMP;
                return;
            }

            if (payload[offset + 2] == 3) {
                flow->msg_id = get_uint32_ntohl(payload, offset + 8);
            } else if (payload[offset + 2] == 0) {
                flow->msg_id = get_uint8_t(payload, offset + 15);
            } else {
                flow->msg_id = get_uint16_ntohs(payload, offset + 15);
            }
            flow->packet_stage = 1 + flow->direction;
            return;
        } else if (flow->packet_stage == 1 + flow->direction) {
            if (payload[offset + 2] == 0) {
                if (flow->msg_id != (uint32_t)(get_uint8_t(payload, offset + 15) - 1)) {
                    goto excl;
                }
            }
        } else if (flow->packet_stage == 2 - flow->direction) {
            if (payload[offset + 2] == 3) {
                if (flow->msg_id != get_uint32_ntohl(payload, offset + 8)) {
                      goto excl;
                }
            } else if (payload[offset + 2] == 0) {
                if (flow->msg_id != get_uint8_t(payload, offset + 15)) {
                  goto excl;
                }
            } else {
                if (flow->msg_id != get_uint16_ntohs(payload, offset + 15)) {
                  goto excl;
                }
            }
            flow->real_protocol_id = PROTOCOL_SNMP;
            return;
        }
        #endif
    }
excl:
    DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_SNMP);
    return;
}
/*
PKT_OK,
PKT_STOLEN,
PKT_REASSEMBLE,
PKT_DROP
*/
static int
dissect_snmp(struct flow_info *flow, int direction,uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    uint32_t        offset=0;
    uint32_t        message_length=0;
    uint32_t        version = -1;
    uint32_t        len;

    uint8_t          ber_class;
    uint8_t          pc, ind;
    uint32_t         tag;
    uint32_t        end_offset;

    struct            ber_ctx ctx;
    struct            dpi_pkt_st pkt;
    st_snmp_info    snmp_info;

    pkt.payload = payload;
    pkt.payload_len = payload_len;

    ctx.info = &snmp_info;

    memset(&snmp_info, 0, sizeof(st_snmp_info));

    // default: 0xff, options: 0 or 1
    snmp_info.msg_report_flag = 0xff;
    snmp_info.msg_encrypt_flag = 0xff;
    snmp_info.msg_auth_flag = 0xff;
    snmp_info.msg_security_mode = 0xffffffff;

    // snmp header
    offset = dpi_ber_identifier(&pkt, offset, &ber_class, &pc, &tag);
    if (offset == 0)
        return PKT_DROP;
    offset = dpi_ber_length(&pkt, offset, &len, &ind);
    if (offset == 0)
        return PKT_DROP;

    offset = dpi_ber_integer(0, &pkt, offset, &version);
    offset = 0;
    switch(version){
        case 0:
        case 1:
            dissect_snmp_message(false, &pkt, offset, &ctx);
            break;
        case 2:
            dissect_snmp_messagev2u(false, &pkt, offset, &ctx);
            break;
        case 3:
            dissect_snmp_messagev3(false, &pkt, offset, &ctx);
            break;

        default:
            return 1;
    }

    write_snmp_log(flow, direction, &snmp_info, NULL);

    return PKT_OK;
}




static void init_snmp_dissector(void)
{
    dpi_register_proto_schema(snmp_field_array,EM_SNMP_MAX,"snmp");

    port_add_proto_head(IPPROTO_UDP, SNMP_PORT_DEF, PROTOCOL_SNMP);
    port_add_proto_head(IPPROTO_UDP, SNMP_PORT_DEF2, PROTOCOL_SNMP);

    udp_detection_array[PROTOCOL_SNMP].proto = PROTOCOL_SNMP;
    udp_detection_array[PROTOCOL_SNMP].identify_func = identify_snmp;
    udp_detection_array[PROTOCOL_SNMP].dissect_func = dissect_snmp;

    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_SNMP].excluded_protocol_bitmask, PROTOCOL_SNMP);

    map_fields_info_register(snmp_field_array, PROTOCOL_SNMP, EM_SNMP_MAX, "snmp");
    pschema_t *schema = dpi_pschema_get_proto("snmp");
    pschema_register_field(schema, "engineIdDataStr", YA_FT_STRING, "desc");
    return;
}


static __attribute((constructor)) void    before_init_snmp(void){
    register_tbl_array(TBL_LOG_SNMP, 0, "snmp", init_snmp_dissector);
}


