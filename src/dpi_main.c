/****************************************************************************************
 * 文 件 名 : dpi_main.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <inttypes.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <string.h>
#include <glib.h>
#include <errno.h>
#include <getopt.h>
#include <unistd.h>
#include <sys/time.h>
#include <signal.h>

//#include <netinet/if_ether.h>
#include <ifaddrs.h>
#include <net/if.h>
#include <linux/if_ether.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <netinet/in.h>
#include <linux/sockios.h>
#include <arpa/inet.h>
#include <sys/socket.h>

#include "dpi_dpdk_wrapper.h"
#include <dlfcn.h>


#include "dpi_detect.h"
#include "dpi_typedefs.h"
#include "dpi_tbl_log.h"
#include "dpi_proto_ids.h"
#include "dpi_dissector.h"
#include "dpi_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_common.h"
#include "dpi_utils.h"
#include "dpi_conversation.h"

#include "iniparser/dictionary.h"
#include "iniparser/iniparser.h"
#include "dpi_trailer.h"
#include "dpi_socket.h"
#include "dpi_plugin.h"
#include "ip2region.h"
#include "sdt_action_out.h"
#include <yaSdxWatch/dpi_statistics.h>
#include "yaBasicUtils/allocator.h"
#include "dpi_forward_eth.h"
#include "dpi_sdt_link.h"
#include "yv_sub/sub.h"
#include "dpi_notify.h"
#include "jhash.h"
#include "dpi_pschema.h"
#include "dpi_data_input.h"
#include "dpi_metrics.h"
#include "dpi_flow.h"
#include "flow_flood.h"
#include "dpi_offline.h"
#include "dpi_gtp_control.h"
#include "dpi_numa.h"
#include "sdx/LineConvert.h"
#ifdef ENABLE_ARKIME
#include "arkime/dpi_arkime_if.h"
#endif


#ifndef UINT32_BIT
#define UINT32_BIT 32
#endif

int dpi_do_recv = 0;

//add by xuxn 统计http解析post的总次数、成功率、失败率
volatile uint64_t post_total = 0;        //http 解析.POST文件 总记录条数
volatile uint64_t post_success = 0;        //http 解析.POST文件 解析成功的记录条数

extern GHashTable *https_filter_table;
extern GHashTable *dns_filter_table;
extern GHashTable *x509_filter_table;
extern GHashTable *ipv4_filter_table;

extern rte_atomic64_t drop_pkts;
extern rte_atomic64_t drop_bytes;
extern rte_atomic64_t receive_pkts;
extern rte_atomic64_t receive_bytes;

extern rte_atomic64_t tbl_fail_pkts;
extern rte_atomic64_t tbl_fail_bytes;
extern rte_atomic64_t dpi_fail_pkts;
extern rte_atomic64_t rsm_fail_get;
extern rte_atomic64_t flow_fail_get;
extern rte_atomic64_t log_256k_fail;

extern __thread char    g_protoinfo[MAX_CONTENT_SIZE];
extern __thread uint16_t g_proto_layer[32];
extern __thread uint8_t  g_proto_layer_cnt;
__thread ya_allocator_t *th_alloc;

//ProtoDict  *dict_global;


static void dpi_stop(int sig);
static void _dpi_offline_stop(void);

void update_protocol_record(int flag);
void update_global_time(void);

pthread_mutex_t rdp_mutex;
unsigned int nb_txq;
#define RTE_TEST_RX_DESC_DEFAULT 128
#define RTE_TEST_TX_DESC_DEFAULT 512
#define NB_SOCKETS                      (2)
#define MAX_RX_QUEUE_PER_LCORE 32
#define MAX_PKT_BURST 512
#define MAX_FLOW_BURST  512
#define NB_MBUF (8192 * 16 * 8)
#define ADDR_MAX 20

static uint32_t rss_flow_type[] = {
    //RTE_ETH_FLOW_IPV4,
    //RTE_ETH_FLOW_FRAG_IPV4,
    RTE_ETH_FLOW_NONFRAG_IPV4_TCP,
    RTE_ETH_FLOW_NONFRAG_IPV4_UDP,
    RTE_ETH_FLOW_NONFRAG_IPV4_SCTP,
    //RTE_ETH_FLOW_NONFRAG_IPV4_OTHER,
    //RTE_ETH_FLOW_IPV6,
    //RTE_ETH_FLOW_FRAG_IPV6,
    RTE_ETH_FLOW_NONFRAG_IPV6_TCP,
    RTE_ETH_FLOW_NONFRAG_IPV6_UDP,
    RTE_ETH_FLOW_NONFRAG_IPV6_SCTP,
    //RTE_ETH_FLOW_NONFRAG_IPV6_OTHER,
};

// symmetric rss key
static uint8_t symmetric_rss_key[] = {
    0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a,
    0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a,
    0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a,
    0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a,
    0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a, 0x6d, 0x5a,
};

static uint16_t nb_rxd = RTE_TEST_RX_DESC_DEFAULT;
static uint16_t nb_txd = RTE_TEST_TX_DESC_DEFAULT;
static int numa_on = 1; /**< NUMA is enabled by default. */
struct rte_mempool *pktmbuf_pool[2];
struct rte_port *ports;           /**< For all probed ethernet ports. */

struct rte_ring *packet_flow_ring[RTE_MAX_LCORE];
struct rte_ring *flow_aging_ring[RTE_MAX_LCORE];
struct rte_ring *thread_conv_ring[RTE_MAX_LCORE];

static struct rte_eth_conf port_conf = {
    .rxmode = {
        .mq_mode = RTE_ETH_MQ_RX_RSS,
        // .max_rx_pkt_len = RTE_ETHER_MAX_LEN,
        // .split_hdr_size = 0,
#ifdef _DPI_DPDK_17
        .header_split   = 0, /**< Header Split disabled */
        .hw_ip_checksum = 0, /**< IP checksum offload enabled */
        .hw_vlan_filter = 0, /**< VLAN filtering disabled */
        .jumbo_frame    = 0, /**< Jumbo Frame Support disabled */
        .hw_strip_crc   = 1, /**< CRC stripped by hardware */
#endif
    },
    .rx_adv_conf = {
        .rss_conf = {
            .rss_key = symmetric_rss_key,
            .rss_key_len = RTE_DIM(symmetric_rss_key),
            .rss_hf = RTE_ETH_RSS_IP | RTE_ETH_RSS_TCP | RTE_ETH_RSS_UDP | RTE_ETH_RSS_SCTP,
        },
    },
    .txmode = {
        .mq_mode = RTE_ETH_MQ_TX_NONE,
    },
};

struct lcore_rx_queue {
    uint8_t port_id;
    uint8_t queue_id;
} __rte_cache_aligned;

struct lcore_conf {
    uint8_t nb_rxq;
    struct lcore_rx_queue rxq_list[MAX_RX_QUEUE_PER_LCORE];
} __rte_cache_aligned;

struct lcore_conf lcore_conf[RTE_MAX_LCORE];

struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];  //每个解析线程的数据
struct rte_mempool *tbl_log_mempool;
struct rte_mempool *tbl_log_content_mempool_256k;



pthread_mutex_t mutex_g_config = PTHREAD_MUTEX_INITIALIZER;
struct global_config g_config = {
    .max_conversation_hash_node = (1 << 16),
    .max_hash_node_per_thread   = (1 << 22),
    .max_flow_num               = (1 << 24),
    .tcp_reassemble_mempool_num = (1 << 20),
    .pkt_stream_mempool_num     = (1 << 20),
    .tbl_memool_num             = (16384),
    .tbl_ring_size              = (65536),

    // ES配置默认值
    .es_config = {
        .es_host = "localhost",
        .es_port = 9200,
        .es_username = "",
        .es_password = "",
        .node_name = "localhost",
        .timeout_seconds = 30,
        .enabled = 0
    },
};

struct protocol_record p_record;

extern const char *protocol_name_array[PROTOCOL_MAX];
extern DpiMetrics g_metrics;

struct entherInfo {
    char *ip;
    char *mac;
};

struct voteDevno {
    int addr;
    int num;
};

const struct int_to_string yv_record_type[] =  {
    {0, "tbl"},
    {1, "json"},
    {2, "txt"},
    {3, "xml"},
    {4, "bin"},
    {-1, NULL }
};

/* for plugin protocol */
#define DISMOD_INIT_FUN          "dissect_init"
#define DISMOD_IDENTIFY_FUN      "dissect_identify"
#define DISMOD_PROTOCOL_FUN      "dissect_protocol"


struct ext_proto_st        *prot_tbl = NULL;
uint16_t                   prot_tbl_num = 0;


extern void init_thread_timer(void);
extern void *socket_main(void *args);
extern void socket_main_stop(void);

static int mac_addr_parse_with_token(const char *str, const char *token, uint8_t *mac)
{
	if (!str || !mac)
		return -1;

	unsigned long l_b;
	char *end = NULL;
	int imac = 0;
	char *p_next = NULL;
    char *save_ptr = NULL;

    char buff[64];
    snprintf(buff, sizeof(buff), "%s", str);

	// 解析 MAC
	p_next = strtok_r(buff, token, &save_ptr);
	while (p_next) {
		l_b = strtoul((const char *)p_next, &end, 16);
		if (p_next[0] == '\0' || !end || (*end != '\0'))
			break;

		mac[imac++] = (uint8_t)l_b;
		if (imac == 6) break;

		p_next = strtok_r(NULL, token, &save_ptr);
	}
    int ret = (imac == 6) ? 0 : -1;
	return ret;
}

int mac_addr_parse(const char *str, uint8_t *mac)
{
    int len = 0;
    len =  mac_addr_parse_with_token(str, ":", mac);
    if(0 == len)
    {
        return 0;
    }

    len =  mac_addr_parse_with_token(str, "-", mac);
    if(0 == len)
    {
        return 0;
    }

    // 不支持的类型
    return -1;
}

int parse_forward_mac_pool(const char*str, char *mac, int size)
{
    char buff[1024];
    snprintf(buff, sizeof(buff), "%s", str);
    char *find = NULL;
    char *start=buff;
    char *end = NULL;
    int index = 0;

    while((find=strtok_r(start,",", &end)))
    {
        //防护
        if(index * 6 >= size)
        {
            break;
        }
        int rc = mac_addr_parse(find, (uint8_t *)mac + index*6);
        if(rc < 0)
        {
            printf("MAC地址格式错误[%s]\n", find);
            DPI_LOG(DPI_LOG_WARNING, "MAC地址格式错误[%s]\n", find);
            return 0;
        }
        index++;
        start = NULL;
    }
    return index;
}

static
void forward_init_config(dictionary *ini) {

	if (!ini)
		return;

	const char *str = NULL;

	g_config.fwd_type = FWD_UNKNOWN;

	g_config.sdt_mac_forward_flag = 0;

	g_config.sdt_data_collect_enable = iniparser_getint(ini, ":SDT_DATA_COLLECT_ENABLE", 0);
	if (g_config.sdt_data_collect_enable == 1) {

		str = iniparser_getstring(ini, ":SDT_MAC_PKT_FORWARD_COLLECT", "");
		if (0 == mac_addr_parse(str, g_config.sdt_dst_mac_fwd_collect)) {
			g_config.sdt_data_collect_enable++;
			g_config.fwd_type |= FWD_DATA_COLLECT;
		}
	}

	g_config.nb_txq = iniparser_getint(ini, ":TXQ_NUM", 4);
	g_config.sdt_fwd_mbuf_size = iniparser_getint(ini, ":SDT_PKT_FWD_MBUF_SIZE", RTE_MBUF_DEFAULT_DATAROOM);

//	str = iniparser_getstring(ini, ":SDT_MAC_PKT_FORWARD", "");

}

static void forward_params_parse(void) {
    if (0 == mac_addr_parse(g_config.web_config.mac_addr_dst, g_config.sdt_dst_mac_forward)
        && 0 == mac_addr_parse(g_config.web_config.mac_addr_src, g_config.sdt_src_mac_forward)) {
        g_config.sdt_mac_forward_flag = 1;
        g_config.fwd_type |= FWD_MAC_FRAME;
    }
}

static void parse_low_mode_confg(dictionary *ini)
{
    if (NULL == ini) return;
    const char * str;
    // get env ; env name dpi_config_mode
    char *env = getenv("DPI_CONFIG_MODE");
    if (NULL == env || strcmp(env, "low") != 0) {
        DPI_LOG(DPI_LOG_WARNING, "DPI_CONFIG_MODE is not set, use default mode");
        return;
    }

// SDX_STAT_REPORT_WEB_ADDR    = ""  # 统计信息上报服务器
// SDX_WEB_CONFIG_ADDR         = ""

    g_config.nb_mbuf                    = iniparser_getint(ini, "low:NB_MBUF", NB_MBUF);
    g_config.nb_rxq                     = iniparser_getint(ini, "low:RXQ_NUM", 4);
    g_config.packet_ring_size           = iniparser_getint(ini, "low:PACKET_RING_SIZE", 1024 * 16 * 16);
    g_config.tcp_flow_timeout           = iniparser_getint(ini, "low:TCP_FLOW_TIMEOUT", 30);
    g_config.max_hash_node_per_thread   = iniparser_getint(ini, "low:MAX_HASH_NODE_PER_THREAD"   , g_config.max_hash_node_per_thread);
    g_config.max_flow_num               = iniparser_getint(ini, "low:MAX_FLOW_NUM"               , g_config.max_flow_num);
    g_config.tcp_reassemble_mempool_num = iniparser_getint(ini, "low:TCP_REASSEMBLE_MEMPOOL_NUM" , g_config.tcp_reassemble_mempool_num);
    g_config.tbl_ring_size              = iniparser_getint(ini, "low:TBL_RING_SIZE"        , g_config.tbl_ring_size);
    // g_config.tbl_log_content_256k       = iniparser_getint(ini, "low:TBL_LOG_CONTENT_256K" , g_config.tbl_log_content_256k);

    str = iniparser_getstring(ini, "low:SDT_WEB_ADDR", "");
    if(str){
        snprintf(g_config.sdt_web_addr, COMMON_SOME_TYPE, "%s", str);
    }

}

static void parser_init_config(const char* run_path,const char *filename)
{
    int i;
    int rc = 0;
    dictionary *ini = NULL;
    char protocol_switch_name[64];
    const char *str;

    //ini = iniparser_load(filename);
    ini = g_config.ini;
    if (ini == NULL)
    {
        DPI_LOG(DPI_LOG_ERROR, "cannot parse file: %s", filename);
        exit(-1);
    }

    g_config.dissector_thread_num  = iniparser_getint(ini, "core:DISSECTOR_THREAD_NUM", 4);
    g_config.flow_aging_thread_num = iniparser_getint(ini, "core:FLOW_AGAIN_THREAD_NUM", 2);
    g_config.log_thread_num        = iniparser_getint(ini, "core:OUTPUT_THREAD_NUM", 2);
    g_config.app_match_thread_num  = iniparser_getint(ini, "core:MATCH_THREAD_NUM", 2);

    g_config.nb_rxq = iniparser_getint(ini, ":RXQ_NUM", 4);
    g_config.mbuf_size = iniparser_getint(ini, ":MBUF_SIZE", RTE_MBUF_DEFAULT_DATAROOM);
    g_config.max_pkt_len = iniparser_getint(ini, ":MAX_PKT_LEN", RTE_ETHER_MAX_LEN);
    if (g_config.max_pkt_len < RTE_ETHER_MAX_LEN) {
        fprintf(stderr, "Invalid max-pkt-len=%d - should be >= %d\n", g_config.max_pkt_len, RTE_ETHER_MAX_LEN);
    } else {
        // port_conf.rxmode.max_rx_pkt_len = g_config.max_pkt_len;
#ifdef _DPI_DPDK_17
        port_conf.rxmode.jumbo_frame = 1;
#endif
    }

    g_config.nb_mbuf = iniparser_getint(ini, ":NB_MBUF", NB_MBUF);
    g_config.mempool_cache_size = iniparser_getint(ini, ":MEMPOOL_CACHE_SIZE", 256);
    g_config.packet_ring_size = iniparser_getint(ini, ":PACKET_RING_SIZE", 1024 * 16 * 16);

    /* detect config */
    g_config.max_hash_node_per_thread   = iniparser_getint(ini, ":MAX_HASH_NODE_PER_THREAD"   , g_config.max_hash_node_per_thread);
    g_config.max_flow_num               = iniparser_getint(ini, ":MAX_FLOW_NUM"               , g_config.max_flow_num);
    g_config.tcp_reassemble_mempool_num = iniparser_getint(ini, ":TCP_REASSEMBLE_MEMPOOL_NUM" , g_config.tcp_reassemble_mempool_num);
    g_config.pkt_stream_mempool_num = iniparser_getint(ini, ":PKT_STREAM_MEMPOOL_NUM" , g_config.pkt_stream_mempool_num);
    g_config.flow_conv_thread_num = iniparser_getint(ini, ":FLOW_CONV_THREAD_NUM", 1);

    g_config.enable_sbp = iniparser_getint(ini, ":DPDK_RX_ENABLE_SAVE_BAD_PACKET" , -1);
    if(-1 == g_config.enable_sbp) {printf("配置文件未设置 DPDK_RX_ENABLE_SAVE_BAD_PACKET\n"); exit(0);}

    g_config.disable_crc = iniparser_getint(ini, ":DPDK_TX_DISABLE_HW_APPEND_CRC" , -1);
    if(-1 == g_config.disable_crc) {printf("配置文件未设置 DPDK_TX_DISABLE_HW_APPEND_CRC\n"); exit(0);}

    /* conversation config*/
    g_config.max_conversation_hash_node = iniparser_getint(ini, ":MAX_CONVERSATION_HASH_NODE", g_config.max_conversation_hash_node);


    /* log config */
    g_config.tbl_memool_num             = iniparser_getint(ini, ":TBL_MEMPOOL_NUM"        , g_config.tbl_memool_num);
    g_config.tbl_ring_size              = iniparser_getint(ini, ":TBL_RING_SIZE"        , g_config.tbl_ring_size);
    g_config.tbl_log_content_256k_num   = iniparser_getint(ini, ":TBL_LOG_CONTENT_256K_NUM" , 8192);

    g_config.input_type = iniparser_getint(ini, "input:INPUT_TYPE", 1);
    str = iniparser_getstring(ini, "input:INPUT_PCAP_DIR", "/root/pcaps");
    snprintf(g_config.pcap_dir, sizeof(g_config.pcap_dir), "%s", str);

    g_config.write_tbl_maxtime = iniparser_getint(ini, ":WRITE_TBL_MAXTIME", 60);
    g_config.tcp_flow_timeout = iniparser_getint(ini, ":TCP_FLOW_TIMEOUT", 30);
    g_config.tcp_flow_timeout_del = iniparser_getint(ini, ":TCP_FLOW_DELETE", 15);
    g_config.udp_flow_timeout = iniparser_getint(ini, ":UDP_FLOW_TIMEOUT", 30);
    g_config.sctp_flow_timeout = iniparser_getint(ini, ":SCTP_FLOW_TIMEOUT", 30);
    g_config.tcp_identify_pkt_num = iniparser_getint(ini, ":TCP_IDENTIFY_PKT_NUM", 10);
    g_config.udp_identify_pkt_num = iniparser_getint(ini, ":UDP_IDENTIFY_PKT_NUM", 8);
    g_config.sctp_identify_pkt_num = iniparser_getint(ini, ":SCTP_IDENTIFY_PKT_NUM", 10);
    g_config.tcp_resseamble_max_num = iniparser_getint(ini, ":TCP_RESSEAMBLE_MAX_NUM", 32);
    g_config.log_max_num = iniparser_getint(ini, ":LOG_MAX_NUM", 10000);
    g_config.idle_scan_period = iniparser_getint(ini, ":IDLE_SCAN_PERION", 10000);
    g_config.max_timeout_num  = iniparser_getint(ini, ":MAX_TIMEOUT_NUM", 512);
    g_config.tcp_rsm_out_of_order  = iniparser_getint(ini, ":TCP_RSM_OUT_OF_ORDER", 100);
    g_config.dissector_thread_conv_timeout = iniparser_getint(ini, ":DISSECTOR_THREAD_CONV_TIMEOUT", g_config.tcp_flow_timeout/100);
    if (g_config.dissector_thread_conv_timeout * 100 < g_config.tcp_flow_timeout) {
        g_config.dissector_thread_conv_timeout = g_config.tcp_flow_timeout * 1.5 / 100;
    }
    // 默认将每个网卡的rss设置为RSS_USE_CUSTOM
    for (unsigned i = 0; i < RTE_MAX_ETHPORTS; i++) {
        g_config.rss_use_type[i] = RSS_USE_CUSTOM;
    }
    g_config.ring_rss_mode   = iniparser_getint(ini, ":RING_RSS_MODE", 0);   /* 是否强制使用ip模式队列负载均衡，0-自动检测机制，1-强制使用源目ip模式*/

    str = iniparser_getstring(ini, ":LOG_OUTPUT_LEVEL", "WARN");
    snprintf(g_config.log_output_level_str, sizeof(g_config.log_output_level_str), "%s", str);

    str = iniparser_getstring(ini, ":TBL_OUT_DIR", "/tmp/tbls/");
    snprintf(g_config.tbl_out_dir, sizeof(g_config.tbl_out_dir), "%s/", str);


    str = iniparser_getstring(ini, ":TBL_FIELD_TABLE_DIR", "/tmp");
    snprintf(g_config.dpi_field_dir, sizeof(g_config.dpi_field_dir), "%s", str);

    g_config.wxf_filter=iniparser_getint(ini, ":TBL_WXF_FILTER_SWITCH", 1);

    /*内容还原开关*/
    g_config.pop_content=iniparser_getint(ini,   ":POP_CONTENT_SWITCH",    0);
    g_config.smtp_content=iniparser_getint(ini,  ":SMTP_CONTENT_SWITCH",   0);
    g_config.imap_content=iniparser_getint(ini,  ":IMAP_CONTENT_SWITCH",   0);
    g_config.ftp_content=iniparser_getint(ini,   ":FTP_CONTENT_SWITCH",    0);
    g_config.tftp_content=iniparser_getint(ini,  ":TFTP_CONTENT_SWITCH",   0);
    g_config.telnet_content=iniparser_getint(ini,":TELNET_CONTENT_SWITCH", 0);

    str = iniparser_getstring(ini, ":DEVNAME", "D00");
    snprintf(g_config.devname, sizeof(g_config.devname), "%s", str);

    str = iniparser_getstring(ini, ":DEVNO", "AUTO");
    snprintf(g_config.devno, sizeof(g_config.devno), "%s", str);

    // trailer和运营商相关配置
    str = iniparser_getstring(ini, ":HARDWARE_TYPE", "NONE");
    get_trailer_type(str, &(g_config.trailer_type), &(g_config.net_type), &(g_config.trailer_length));
    g_config.show_operator_switch = iniparser_getint(ini, ":SHOW_OPERATOR_SWITCH", 1);
    str = iniparser_getstring(ini, ":OPERATOR_SWITCH", "AUTO");
    snprintf(g_config.operator_type, sizeof(g_config.operator_type), "%s", str);

    str = iniparser_getstring(ini, ":LOG_OUTPUT_PATH", "./log.txt");
    snprintf(g_config.log_output_path, sizeof(g_config.log_output_path), "%s", str);

    g_config.fp_log = fopen(g_config.log_output_path, "a+");
    if(!g_config.fp_log)
        DPI_SYS_LOG(DPI_LOG_WARNING, "can't open log file");
    str = iniparser_getstring(ini, ":RECORD_PER_MIN_PATH", "/tmp/record_per_min.txt");
    snprintf(g_config.record_permin_path, sizeof(g_config.record_permin_path), "%s", str);

    str = iniparser_getstring(ini, ":RECORD_PER_DAY_PATH", "/tmp/record_per_day.txt");
    snprintf(g_config.record_perday_path, sizeof(g_config.record_perday_path), "%s", str);

    g_config.record_update_time = iniparser_getint(ini, ":RECORD_UPDATE_TIME", 60);
    g_config.max_pkt_burst      = iniparser_getint(ini, ":MAX_PKT_BURST", 512);

    g_config.protocol_switch_reverse = iniparser_getint(ini, ":PROTOCOL_SWITCH_REVERSE", 0);
    for (i = 0; i < PROTOCOL_MAX; i++) {
        snprintf(protocol_switch_name, sizeof(protocol_switch_name), ":PROTOCOL_SWITCH_%s", protocol_name_array[i]);
        g_config.protocol_switch[i] = iniparser_getint(ini, protocol_switch_name, 1);
        g_config.protocol_switch[i] = g_config.protocol_switch_reverse ? !g_config.protocol_switch[i] : g_config.protocol_switch[i];
        if (g_config.protocol_switch[i] != 0)
            g_config.protocol_switch[i] = 1;
    }

#ifdef _DPI_PLUGIN
    g_config.dpi_plugin = iniparser_getint(ini, ":DPI_PLUGIN", 0);
    if(g_config.protocol_switch[PROTOCOL_TEAMVIEWER]){
        if(g_config.dpi_plugin)
            plugin_register();
        else
            g_config.protocol_switch[PROTOCOL_TEAMVIEWER] = 0;
    }
#endif

    g_config.conversation_switch = iniparser_getint(ini, ":CONVERSATION_SWITCH", 1);
    if (g_config.conversation_switch != 0)
        g_config.conversation_switch = 1;

    g_config.conversation_identify_pkt_num = iniparser_getint(ini, ":CONVERSATION_IDENTIFY_PKT_NUM", 1);

    g_config.flow_log_switch = iniparser_getint(ini, ":FLOW_LOG_SWITCH", 0);
    if (g_config.flow_log_switch != 0)
        g_config.flow_log_switch = 1;

    g_config.ip_position_switch = iniparser_getint(ini, ":IP_POSITION_SWITCH", 0);  //0:无,1:ip2region,2:mmdb:
    if (g_config.ip_position_switch > 2)
        g_config.ip_position_switch = 2;

    if (g_config.ip_position_switch)
    {
        printf("IP 地址位置信息转换库 已开启, 非常消耗性能!!!\n");
        sleep(1);
        printf("IP 地址位置信息转换库 已开启, 非常消耗性能!!!\n");
        sleep(1);
        printf("IP 地址位置信息转换库 已开启, 非常消耗性能!!!\n");
        sleep(1);
    }

    g_config.write_l2tp_inner = iniparser_getint(ini, ":WRITE_L2TP_INNER", 1); //是否写l2tp内层IP<data message>,默认写
    g_config.show_traffic_speed_all = iniparser_getint(ini, ":SHOW_TRAFFIC_SPEED_ALL", 1); //是否写完整的收包速率,1写
    g_config.show_task_id = iniparser_getint(ini, ":SHOW_TASK_ID", 0); //tbl文件名是否加task_id,0是,1否
    g_config.ssl_ssh_min_enc_num = iniparser_getint(ini, ":SSL_SSH_MIN_ENC_NUM",  4); //ssl ssh双向加密的前两的报文可写的最小包数

    g_config.offline_read_over_stop = iniparser_getint(ini, ":OFFLINE_READ_OVER_STOP", 0);
    g_config.h323_lazy =iniparser_getint(ini, ":H323_REPEAT", 0);
    g_config.ldap_lazy =iniparser_getint(ini, ":LDAP_REPEAT", 0);
    g_config.cldap_lazy =iniparser_getint(ini, ":CLDAP_REPEAT", 0);

    //sip中from和to相同时,如本开关打开,则不输出
    g_config.sip_filter = iniparser_getint(ini, ":SIP_FROM_TO_NOTEQUAL", 0);
    //此开关置1时，gtp_c只输出有位置信息及三元组的tbl
    g_config.gtp_filter = iniparser_getint(ini, ":GTP_ONLY_FOR_ULI_DEVICE_TAG", 0);
    g_config.dns_filter = iniparser_getint(ini, ":DNS_ONLY_ANS", 0);
    g_config.dns_filter_only_request = iniparser_getint(ini, ":DNS_FILTER_ONLY_REQUEST", 1);
    g_config.ssl_flow_mode = iniparser_getint(ini, ":SSL_FLOW_MODE", 0);
    g_config.ssh_packet_mode = iniparser_getint(ini, ":SSH_PACKET_MODE", 0);
    g_config.rdp_packet_mode = iniparser_getint(ini, ":RDP_PACKET_MODE", 0);
    g_config.rdp_mutex_switch = iniparser_getint(ini, ":RDP_MUTEX_SWITCH", 0);
    g_config.login_success_time = iniparser_getint(ini, ":LOGIN_SUCCESS_LASTED_TIME", 20);

    /* ADD_S By chunli */      // For capture
    str = iniparser_getstring(ini, ":PCAP_FLOW_PRO", "");
    snprintf(g_config.pcap_flow_pro, sizeof(g_config.pcap_flow_pro), "%s", str);
    str = iniparser_getstring(ini, ":PCAP_FLOW_DIR", "/tmp/pcap");
    snprintf(g_config.pcap_flow_dir, sizeof(g_config.pcap_flow_pro), "%s", str);
    g_config.pcap_flow_num  = iniparser_getint(ini, ":PCAP_FLOW_NUM", 10);          //每条流采样包数,默认10包
    g_config.pcap_flow_size = iniparser_getint(ini, ":PCAP_FLOW_SIZE", 10) * (1024*1024);  //单位是1MB, 默认值是10MB
    g_config.pcap_flow_rate = iniparser_getint(ini, ":PCAP_FLOW_RATE", 0);                //协议取样率, 默认为零时不采样


    // HTTP_TCP 设置
    g_config.http.tcp_out_of_order= iniparser_getint(ini, ":HTTP_TCP_OUT_OF_ORDER_NUM", 20); // HTTP_TCP 乱序个数容忍值, 默认容忍抖动间隔20个
    g_config.http.tcp_padding_len = iniparser_getint(ini, ":HTTP_TCP_MISS_PADDING_LEN", 0);  // HTTP_TCP 缺包小于N时,打PADDING字符, 默认0关闭
    g_config.http.error_pcap_dump = iniparser_getint(ini, ":HTTP_TCP_ERROR_PCAP_DUMP", 0);   // HTTP_TCP 无法解析时输出 PCAP,默认关闭
    g_config.http.http_exquisite_switch = iniparser_getint(ini, ":HTTP_EXQUISITE_SWITCH", 0);
    str = iniparser_getstring(ini, ":HTTP_TCP_MISS_PADDING_STR", " ");                       // HTTP_TCP PADDING 字符, 默认空格.
    memcpy(g_config.http.tcp_padding_str, str, strlen(str));

    g_config.http.http_tax_switch = iniparser_getint(ini, ":HTTP_TAX_SWITCH", 0);            //航天金税解析开关

    //记录错误的数据报文, 转储为PCAP文件
    g_config.error_pcap_size= iniparser_getint(ini, ":ERROR_PCAP_SIZE", 0) * (1024*1024); // ERROR PCAP 文件大小限制, 单位MB, 默认关闭
    str = iniparser_getstring(ini, ":ERROR_PCAP_DIR", "/tmp/tbls/ERR");                   // ERROR PCAP 转储路径
    memcpy(g_config.error_pcap_dir, str, strlen(str));

    g_config.x509_write_switch   = iniparser_getint(ini, ":X509_WRITE_SWITCH", 0);        //是否输出x509证书,默认不输出
    g_config.x509_verify_switch  = iniparser_getint(ini, ":X509_VERIFY_SWITCH", 0);       //是否输出x509证书认证字段,默认不输出
    g_config.x509_fingerprint_alg= iniparser_getint(ini, ":X509_FINGERPRINT_ALG", 1);     //是否输出x509证书指纹算法,默认为1,SHA1
    g_config.x509_whitelist_switch = iniparser_getint(ini, ":X509_WHITELIST_SWITCH", 0);  //白名单开关,默认关闭
    if(g_config.x509_whitelist_switch){
        str = iniparser_getstring(ini, ":X509_WHITELIST_FILENAME", NULL);//SSL白名单位置
        if(str){
            snprintf(g_config.x509_whitelist_filename, sizeof(g_config.x509_whitelist_filename), "%s", str);
        }
        else{
            g_config.x509_whitelist_switch = 0; //如果没设置,开关再关掉
        }
    }

    g_config.ssl_pcap  = iniparser_getint(ini, ":SSL_PCAP", 0);    //只输出SSL加密的前两个报文
    g_config.ssh_pcap  = iniparser_getint(ini, ":SSH_PCAP", 0);    //只输出SSH加密的前两个报文

    str = iniparser_getstring(ini, ":HTTP_STATUS_WHITELIST", "*");//SSL白名单位置
    if(str){
        snprintf(g_config.http_status_whitelist, sizeof(g_config.http_status_whitelist), "%s", str);
    }
    else{
        g_config.http_status_whitelist[0] = '*'; //如果没设置,开关再关掉
    }

    //SSL白名单过滤配置
    g_config.https_default_switch = iniparser_getint(ini, ":HTTPS_DEFAULT_SWITCH", 0);        //默认黑白名单，０白名单，１黑名单
    g_config.https_whitelist_switch = iniparser_getint(ini, ":HTTPS_WHITELIST_SWITCH", 0);      //白名单开关,默认关闭
    g_config.https_blacklist_switch = iniparser_getint(ini, ":HTTPS_BLACKLIST_SWITCH", 0);      //黑名单开关,默认关闭
    if(g_config.https_whitelist_switch){
        str = iniparser_getstring(ini, ":HTTPS_WHITELIST_FILENAME", NULL);//SSL白名单位置
        if(str){
            snprintf(g_config.https_whitelist_filename, sizeof(g_config.https_whitelist_filename), "%s", str);
        }
        else{
            g_config.https_whitelist_switch = 0; //如果没设置,开关再关掉
        }
    }
    if(g_config.https_blacklist_switch){
        str = iniparser_getstring(ini, ":HTTPS_BLACKLIST_FILENAME", NULL);//SSL黑名单位置
        if(str){
            snprintf(g_config.https_blacklist_filename, sizeof(g_config.https_blacklist_filename), "%s", str);
        }
        else{
            g_config.https_blacklist_switch = 0; //如果没设置,开关再关掉
        }
    }

    g_config.ipv4_blacklist_switch = iniparser_getint(ini, ":IPV4_BLACKLIST_SWITCH", 0);
    if(g_config.ipv4_blacklist_switch){
        str = iniparser_getstring(ini, ":IPV4_BLACKLIST_FILENAME", NULL);//IPv4黑名单位置
        if(str){
            snprintf(g_config.ipv4_blacklist_filename, sizeof(g_config.ipv4_blacklist_filename), "%s", str);
        }
        else{
            g_config.ipv4_blacklist_switch = 0; //如果没设置,开关再关掉
        }
    }

    g_config.write_one_esp = iniparser_getint(ini, ":WRITE_ONE_ESP", 1);    //1写一条esp,0有多少写多少
    g_config.ssl_or_x509   = iniparser_getint(ini, ":SSL_OR_X509", 1);
    //DNS白名单过滤配置
    g_config.dns_default_switch = iniparser_getint(ini, ":DNS_DEFAULT_SWITCH", 0);        //默认黑白名单，０白名单，１黑名单
    g_config.dns_whitelist_switch = iniparser_getint(ini, ":DNS_WHITELIST_SWITCH", 0);      //白名单开关,默认关闭
    g_config.dns_blacklist_switch = iniparser_getint(ini, ":DNS_BLACKLIST_SWITCH", 0);      //黑名单开关,默认关闭
    if(g_config.dns_whitelist_switch){
        str = iniparser_getstring(ini, ":DNS_WHITELIST_FILENAME", NULL);//DNS白名单位置
        if(str){
            snprintf(g_config.dns_whitelist_filename, sizeof(g_config.dns_whitelist_filename), "%s", str);
        }
        else{
            g_config.dns_whitelist_switch = 0; //如果没设置,开关再关掉
        }
    }
    if(g_config.dns_blacklist_switch){
        str = iniparser_getstring(ini, ":DNS_BLACKLIST_FILENAME", NULL);//DNS黑名单位置
        if(str){
            snprintf(g_config.dns_blacklist_filename, sizeof(g_config.dns_blacklist_filename), "%s", str);
        }
        else{
            g_config.dns_blacklist_switch = 0; //如果没设置,开关再关掉
        }
    }

    g_config.ssh_login_valid_num = iniparser_getint(ini, ":SSH_LOGIN_VALID_NUM", 20);//ssh登录是否成功包数临界值
    g_config.rdp_login_valid_num = iniparser_getint(ini, ":RDP_LOGIN_VALID_NUM", 40);//rdp登录是否成功包数临界值
    g_config.rdp_identify_only_port = iniparser_getint(ini, ":RDP_IDENTIFY_ONLY_PORT", 0);

    g_config.task_name_switch    = iniparser_getint(ini, ":TASK_NAME_SWITCH", 0); //task name开关,目前用于东北

    /* ADD_E By chunli */
    g_config.http_content_limite = iniparser_getint(ini, ":HTTP_CONTENT_LIMITE", 0); // HTTP 还原的限制控制.


    str = iniparser_getstring(ini, ":PROCESS_WORK_MODE", "dpdk");//程序运行模式
    if(str){
        snprintf(g_config.work_mode, sizeof(g_config.work_mode), "%s", str);
    }

    str = iniparser_getstring(ini, ":PROCESS_SOCKET_IP", "127.0.0.1");//程序运行模式
    if(str){
        snprintf(g_config.process_socket_ip, strlen(str)+1, "%s", str);
        g_config.process_socket_ip[strlen(str)]='\0';
    }
    g_config.process_socket_port=iniparser_getint(ini, ":PROCESS_SOCKET_PORT", 10009);
    g_config.vtysh_socket_port=iniparser_getint(ini, ":VTYSH_SOCKET_PORT", 4567);

    g_config.http.http_skip_body           =iniparser_getint(ini, ":HTTP_SKIP_BODY", 1);
    g_config.http.http_strip_cache_size    =iniparser_getint(ini, ":HTTP_STRIP_CACHE_SIZE", 512*1024);
    g_config.http.http_switch_store_file   =iniparser_getint(ini, ":HTTP_SWITCH_STORE_FILE", 0);
    g_config.http.http_switch_response_file=iniparser_getint(ini, ":HTTP_SWITCH_RESPONSE_FILE", 1);
    g_config.http.http_file_size        =iniparser_getint(ini, ":HTTP_STORE_FILE_SIZE", 100*1024);
    if(g_config.http.http_file_size>g_config.http.http_strip_cache_size){
        g_config.http.http_file_size=g_config.http.http_strip_cache_size;
    }
    g_config.http.drop_no_content_type=iniparser_getint(ini, ":HTTP_DROP_NO_CONTENT_TYPE", 0);
    g_config.http.num=0;
    str = iniparser_getstring(ini, ":HTTP_CONTENT_TYPE_FILTER", NULL);
    if(str){
        snprintf(g_config.http.http_content_filter, 512, "%s", str);
        g_config.http.http_content_filter[strlen(str)]='\0';
        split(g_config.http.http_content_filter, ",",g_config.http.filter_split, &g_config.http.num);
        for(i=0;i<g_config.http.num;i++){
            if(regcomp(&g_config.http.reg[i], g_config.http.filter_split[i], REG_EXTENDED)!=0){
                DPI_LOG(DPI_LOG_ERROR, "content-type 过滤规则是正则表达式，你填写的其中一个或多个规则的正则语法有误");
                exit(-1);
            }
        }
    }
    /* 插件协议初始化 */
    prot_tbl_num=iniparser_getsecnkeys(ini, "plugin_protocol");
    if(prot_tbl_num>0){
        prot_tbl=(struct ext_proto_st *)malloc(sizeof(struct ext_proto_st)*prot_tbl_num);
        const char **proto=(const char **)malloc(sizeof(char *)*prot_tbl_num);
        iniparser_getseckeys(ini, "plugin_protocol",proto);
        for(i=0;i<prot_tbl_num;i++){
            str=NULL;
            str=iniparser_getstring(ini, proto[i], NULL);
            if(str){
                prot_tbl[i].handle=dlopen(str, RTLD_LAZY);
                if(prot_tbl[i].handle==NULL){
                    DPI_LOG(DPI_LOG_ERROR, "dlopen %s failed, please check if the so is exist",str);
                    exit(-1);
                }
                /* attach functions */
                prot_tbl[i].proto_init = dlsym(prot_tbl[i].handle, DISMOD_INIT_FUN);
                if (prot_tbl[i].proto_init == NULL) {
                    DPI_LOG(DPI_LOG_ERROR, "In module %s don't exist function %s", str, DISMOD_INIT_FUN);
                    exit(-1);
                }

                prot_tbl[i].proto_identify = dlsym(prot_tbl[i].handle, DISMOD_IDENTIFY_FUN);
                if (prot_tbl[i].proto_identify == NULL) {
                    DPI_LOG(DPI_LOG_ERROR, "In module %s don't exist function %s", str, DISMOD_IDENTIFY_FUN);
                    exit(-1);
                }
                prot_tbl[i].proto_dissector = dlsym(prot_tbl[i].handle, DISMOD_PROTOCOL_FUN);
                if (prot_tbl[i].proto_dissector == NULL) {
                    DPI_LOG(DPI_LOG_ERROR, "In module %s don't exist function %s", str, DISMOD_PROTOCOL_FUN);
                    exit(-1);
                }
                prot_tbl[i].protocol_id=PLUGIN_PROTO_START_ID+i;
                char *p=strrchr(str,'_');
                if(p){
                    char *q=strchr(p,'.');
                    if(q-p-1>0 && q-p-1<COMMON_FILE_NAME){
                        char buff[64]={0};
                        memcpy(buff, p+1,q-p-1);
                        snprintf(prot_tbl[i].ext_proto_name, COMMON_FILE_NAME,"plugin_%s",buff);
                        char **proto_array=NULL;
                        int   array_len=0;
                        proto_array=prot_tbl[i].proto_init( &array_len);
                        if(NULL==proto_array || 0==array_len){
                            DPI_LOG(DPI_LOG_ERROR, "plugin %s fields init failed",str);
                            exit(-1);
                        }
                        if(plugin_write_proto_field_tab(proto_array,array_len, prot_tbl[i].ext_proto_name)==0){
                            DPI_LOG(DPI_LOG_ERROR, "plugin %s write fields  failed",str);
                            exit(-1);
                        }
                        register_plugin_tbl_array(i, 0, prot_tbl[i].ext_proto_name,NULL);
                    }else{
                        DPI_LOG(DPI_LOG_ERROR, "plugin nameing does not conform to the specification (example:plugin_xxx.so),yours %s",str);
                        exit(-1);
                    }
                }else{
                    DPI_LOG(DPI_LOG_ERROR, "plugin nameing does not conform to the specification (example:plugin_xxx.so), yours %s",str);
                    exit(-1);
                }
            }
        }
    }

    //----------------------------------------------SDT 配置项 ------------------------------------------
    str = iniparser_getstring(ini, ":SDT_FORWARD_MAC_POOL", 0);
    g_config.sdx_mac_num = parse_forward_mac_pool(str, (char*)g_config.sdx_mac, sizeof(g_config.sdx_mac));

    /* 规则文件名 */
    g_config.match_status_switch=iniparser_getint(ini, ":SDT_MATCH_STATUS", 0);

    str = iniparser_getstring(ini, ":SDT_RULES_FILE", "./rules.sdt");
    if(str){
        snprintf(g_config.sdt_rules_file, COMMON_FILE_NAME, "%s", str);
    }

    str = iniparser_getstring(ini, ":TBL_OUT_DIR", "/tmp/tbls/");
    if(str){
        snprintf(g_config.sdt_out_pcap_dir, COMMON_FILE_NAME, "%s", str);
        snprintf(g_config.sdt_out_event_dir, COMMON_FILE_NAME, "%s", str);
    }

    str = iniparser_getstring(ini, ":sdt_pcap_identification", NULL);
    if(str){
        snprintf(g_config.sdt_pcap_identification, COMMON_FILE_NAME, "%s", str);
    }
    else
    {
        printf("配置文件 未定义 SDT_PCAP_IDENTIFICATION, 用于标识PCAP的标记名\n");
        exit(0);
    }

    str = iniparser_getstring(ini, ":SDT_OUT_SYSLOG_DIR", "/tmp/tbls/syslog");
    if(str){
        snprintf(g_config.sdt_out_syslog_dir, COMMON_FILE_NAME, "%s", str);
    }

    rc = iniparser_getint(ini,":UNKNOWN_CACHE_MAX", 0);
    g_config.unknown_cache_max = DPI_MIN(rc,CACHE_MAX);
    g_config.sdt_out_ring_size      = iniparser_getint(ini, ":SDT_OUT_RING_SIZE", 65536);
    g_config.sdt_out_pcap_max_MB    = iniparser_getint(ini, ":SDT_OUT_PCAP_MAX_MB", 500);
    g_config.sdt_out_pcap_max_bytes = g_config.sdt_out_pcap_max_MB*1024*1024;
    g_config.sdt_out_pcap_max_time  = iniparser_getint(ini, ":SDT_OUT_PCAP_MAX_TIME", 60);
    g_config.sdt_out_event_max_line = iniparser_getint(ini, ":SDT_OUT_EVENT_MAX_LINE", 10000);
    g_config.sdt_rule_max_num       = iniparser_getint(ini, ":SDT_RULES_MAX_NUM", 10000);
    g_config.sdt_switch_mult_hit    = iniparser_getint(ini, ":SDT_SWITCH_MULT_HIT", 1);
    g_config.sdt_ip_with_mask       = iniparser_getint(ini, ":SDT_IP_WITH_MASK", 1);

    str = iniparser_getstring(ini, ":SDT_RULES_DEFAULT_ACTION", NULL);
    if(str)
    {
        g_config.sdt_rules_default_action = strdup(str);
    }
    else
    {
        printf("SDT_RULES_DEFAULT_ACTION 参数未设置\n");
        exit(-1);
    }

    str = iniparser_getstring(ini, ":SDT_OUT_PRODUCE_DATA_DEV_NAME", "YV");
    if(str){
        snprintf(g_config.sdt_out_produce_data_dev_name, sizeof(g_config.sdt_out_produce_data_dev_name), "%s", str);
    }

    str = iniparser_getstring(ini, ":SDT_OUT_PRODUCE_DATA_DEV_IP", "127.0.0.1");
    if(str){
        snprintf(g_config.sdt_out_produce_data_dev_ip, sizeof(g_config.sdt_out_produce_data_dev_ip), "%s", str);
    }
    strncpy(g_config.sdx_config.sdx_ip_interface, iniparser_getstring(ini, ":SDX_IP_INTERFACE", "ens192"), sizeof(g_config.sdx_config.sdx_ip_interface));

    if (dpi_utils_get_ipv4_addr_of_if(g_config.sdx_config.sdx_ip_interface, g_config.sdx_config.sdx_ip_str))
    {
        g_config.sdx_config.sdx_ip_number = ntohl(inet_addr(g_config.sdx_config.sdx_ip_str));
    }
    else
    {
        printf("  ERROR: <config::SDX_IP_INTERFACE> 没有从端口[%s]获取到有效IP\n", g_config.sdx_config.sdx_ip_interface);
    }

    g_config.sdt_local_port=iniparser_getint(ini, ":SDT_LOCAL_PORT", 8888);
    str = iniparser_getstring(ini, ":SDT_WEB_ADDR", "");
    if(str){
        snprintf(g_config.sdt_web_addr, COMMON_SOME_TYPE, "%s", str);
    }
    g_config.sdt_reload_rule=iniparser_getint(ini, ":SDT_RELOAD_RULE_SWITCH", 0);
    g_config.sdt_reflect_rule=iniparser_getint(ini, ":SDT_REFLECT_RULE_SWITCH", 0);
    if(g_config.sdt_reflect_rule>1){
        g_config.sdt_reflect_rule=1;
    }


    g_config.sdt_user_id  = iniparser_getint(ini, ":SDT_USER_ID", 0);

    g_config.sdt_block_buff_size    = iniparser_getint(ini, ":SDT_BLOCK_BUFF_SIZE", 5000);
    g_config.sdt_block_buff_keep    = iniparser_getint(ini, ":SDT_BLOCK_BUFF_KEEP", 256);
    if(g_config.sdt_block_buff_keep>=g_config.sdt_block_buff_size){
        g_config.sdt_block_buff_keep = 256;
    }
    g_config.sdt_block_unctn_num    = iniparser_getint(ini, ":SDT_BLOCK_UNCONTINUE_NUM", 20);

    g_config.sdt_cache_max_pkts     = iniparser_getint(ini, ":SDT_CACHE_MAX_PKTS", 5000);

    g_config.sdt_match_max_rules    = iniparser_getint(ini, ":SDT_MATCH_MAX_RULES", SDT_MAX_MATCH_RULES);
    if(g_config.sdt_match_max_rules>SDT_MAX_MATCH_RULES){
        g_config.sdt_match_max_rules=SDT_MAX_MATCH_RULES;
    }
    if(g_config.sdt_match_max_rules<1 || 1==g_config.sdt_switch_mult_hit){
        g_config.sdt_match_max_rules=1;
    }
    g_config.sdt_record_match_rule  = iniparser_getint(ini, ":SDT_RECORD_MATCH_RULE", 1);
    g_config.sdt_send_kafka  = iniparser_getint(ini, ":SDT_SEND_KAFKA",0);

    str = iniparser_getstring(ini, ":SDT_KAFKA_IP", "127.0.0.1:8888");
    if(str){
        snprintf(g_config.sdt_kafka_ip, sizeof(g_config.sdt_kafka_ip), "%s", str);
    }

    str = iniparser_getstring(ini, ":SDX_DATA_FROM", "");
    if(str){
        snprintf(g_config.data_from, sizeof(g_config.data_from), "%s", str);
    }
    g_config.sigtype  = iniparser_getint(ini, ":SDX_SIGTYPE",0);
    g_config.ftp_port  = iniparser_getint(ini, ":SDX_FTP_PORT", 21);


    // 设备zd号
    g_config.hardware_zidnumber = iniparser_getint(ini, ":HARDWARE_ZIDNUMBER", 0);

        //元数据密级
    str = iniparser_getstring(ini, ":SDX_MDSECDEG", "UNKNOWN");
    if(str){
      snprintf(g_config.mdsecdeg, COMMON_FILE_NAME, "%s", str);
    }
    //数据文件密级
    str = iniparser_getstring(ini, ":SDX_FILESECDEG", "UNKNOWN");
    if(str){
      snprintf(g_config.filesecdeg, COMMON_FILE_NAME, "%s", str);
    }
    //安全等级保护
    str = iniparser_getstring(ini, ":SDX_SECDEGPRO", "UNKNOWN");
    if(str){
      snprintf(g_config.secdegpro, COMMON_FILE_NAME, "%s", str);
    }
    g_config.sdx_out_all_field = iniparser_getint(ini, ":SDX_OUTPUT_ALL_FIELD", 1);
    g_config.adapt_type    = iniparser_getint(ini, "output:ADAPT_TYPE", 0);
    str = iniparser_getstring(ini, "output:ADAPT_DIR_LUA", "./adapt_lua");
    snprintf(g_config.adapt_dir_lua, sizeof(g_config.adapt_dir_lua), "%s", str);

    str = iniparser_getstring(ini, "output:ADAPT_DIR_CLIBS", "./clibs");
    if (str[0] == '/') {
      snprintf(g_config.adapt_dir_clibs, sizeof(g_config.adapt_dir_clibs), "%s", str);
    } else {
      snprintf(g_config.adapt_dir_clibs, sizeof(g_config.adapt_dir_clibs), "%s/%s", run_path, str);
    }

    parse_low_mode_confg(ini);
    g_config.ini=ini;

    /* 解析sdt包转发相关配置信息 */
    forward_init_config(ini);

    //最后设置全局参数 -- 避免重复设定
    str = iniparser_getstring(ini, ":SDX_MOUNT_DIR", "/mnt/nfs");
    snprintf(g_config.sdx_mount_dir, sizeof(g_config.sdx_mount_dir), "%s", str);
#ifndef DPI_SDT_ZDY
    dpi_utils_get_tbl_out_dir(g_config.sdx_mount_dir,"sdt");
#endif
    //scan模式相关配置
    g_config.data_input_with_yn470_line_info = iniparser_getint(ini, ":DATA_INPUT_WITH_YN470_LINE_INFO", 0);
    g_config.data_input_scanning_infinite    = iniparser_getint(ini, ":DATA_INPUT_SCANNING_INFINITE", 1);
    g_config.data_input_scanning_do_rename   = iniparser_getint(ini, ":DATA_INPUT_SCANNING_DO_RENAME_INFINITE", 1);
    strcpy(g_config.data_input_scanning_ignore, iniparser_getstring(ini, ":DATA_INPUT_SCANNING_IGNORE", ".finish"));

    str = iniparser_getstring(ini, ":FINISH_SUFFIX", ".finish");
    strncpy(g_config.finish_suffix, str, sizeof(g_config.finish_suffix) - 1);
    g_config.data_input_scaning_del_file_flag   = iniparser_getint(ini, ":DATA_INPUT_SCANNING_DEL_FILE_FLAG", 1);
    g_config._327_common_switch = iniparser_getint(ini, ":327_COMMON_HEAD",0);

    // output
    g_config.output_write  = iniparser_getint(ini, "output:OUTPUT_WRITE_SWITCH", 1);

    g_config.decode_identity_switch  = iniparser_getint(ini, ":DECODE_IDENTITY_SWITCH", 0);
    g_config.date_subdir_flag   = iniparser_getint(ini, "output:DATE_SUBDIR_FLAG", 1);
    g_config.yv_record_type  = iniparser_getint(ini, "output:YV_RECORD_TYPE",1);
#ifdef DPI_SDT_ZDY
    g_config.yv_record_type   = 0;
#endif
    strcpy(g_config.yv_data_suffix, val_to_string(g_config.yv_record_type, yv_record_type));

    str = iniparser_getstring(ini, "output:TBL_FILENUM_PERDAY_PATH", "/tmp/.tbl_filenum_per_day_Metedata.txt");
    snprintf(g_config.tbl_filenum_perday_path,sizeof(g_config.tbl_filenum_perday_path), "%s", str);

#ifdef ENABLE_ARKIME
    dpi_arkime_init_config(ini);
#endif

}

static int is_valid_work_mode(const char *strWorkMode)
{
    static const char *valid_work_mode[] = {
        "dpdk",
        "socket",
        "scan",
    };

    for (unsigned int i = 0; i < sizeof(valid_work_mode) / sizeof(valid_work_mode[0]); i++)
    {
        if (strncasecmp(strWorkMode, valid_work_mode[i], strlen(valid_work_mode[i])) == 0)
        {
            return 1;
        }
    }

    return 0;
}

/* Parse the argument given in the command line of the application */
static void print_usage(const char *prgname)
{

    fprintf(stdout, "\n[Usages:]\n");
    fprintf(stdout, "%s [EAL options] -- [dpi options]\n", prgname);
    fprintf(stdout, "\n[dpi options:]\n");
    fprintf(stdout, "  -r <num>             :收包队列\n");
    fprintf(stdout, "  -p <num>             :解析线程\n");
    fprintf(stdout, "  -m <num>             :匹配线程\n");
    fprintf(stdout, "  -a <num>             :老化线程\n");
    fprintf(stdout, "  -l <num>             :tbl输出线程\n");
    fprintf(stdout, "  -o <num>             :pcap输出线程\n");
    fprintf(stdout, "  -f <num>             :flow表输出线程\n");
    fprintf(stdout, "  -c <num>             :组包文件输出线程\n");
    fprintf(stdout, "  --test               :开启测试模式\n");
    fprintf(stdout, "  --pcap_dir <dir>     :设置离线读包目录\n");
    fprintf(stdout, "  -v                   :打印版本号\n");
}

static int
parser_args(int argc, char **argv, char *prgname)
{
#if defined DEBUG || !defined NDEBUG
    #define BUILD_TYPE  "Debug"
#else
    #define BUILD_TYPE  "Release"
#endif

    int opt;
    int i;
    char **argvopt;
    char core_str[256];
    char *tmp;

    argvopt = argv;

    /*
     * -r 收包线程
     * -p 解析线程
     * -m 应用协议字段规则匹配线程
     * -l 解析结果命中字段输出线程
     * -o pcap 报文输出到文件线程
    */
    const char *optstring = "r:p:m:l:o:vW:a:f:c:h";
    struct option long_options[] =
    {
        {"task_id", required_argument, 0, 't'},
        {"work-mode" , required_argument, 0, 'W'},
        {"test", no_argument, NULL, 301},
        {"pcap_dir", required_argument, NULL, 302},
        {"help", no_argument, NULL, 'h'},
        {0, 0, 0, 0}
    };
    while ((opt = getopt_long(argc, argvopt, optstring, long_options, 0)) != EOF) {
        switch (opt) {
        case 'r':
            i = 0;
            strncpy(core_str, optarg, sizeof(core_str));
            tmp = strtok(core_str, ",");
            while (tmp) {
                if(i > RCV_CORE_MAX_NUM - 1){
                    DPI_LOG(DPI_LOG_ERROR, "the max receive packet thread num is %d", RCV_CORE_MAX_NUM);
                    exit(-1);
                }
                g_config.pkt_rcv_core_id[i] = atoi(tmp);
                i++;
                tmp = strtok(NULL, ",");
            }
            g_config.nb_rxq = i;
            break;

        case 'p':
            i = 0;
            strncpy(core_str, optarg, sizeof(core_str));
            tmp = strtok(core_str, ",");
            while (tmp) {
                g_config.dissector_core_id[i++] = atoi(tmp);
                tmp = strtok(NULL, ",");
            }
            g_config.dissector_thread_num = i;

            if (g_config.dissector_thread_num > MAX_FLOW_THREAD_NUM) {
                fprintf(stderr, "the max dissector thread num is %d\n", MAX_FLOW_THREAD_NUM);
                exit(-1);
            }
            break;
        case 'a':
            i = 0;
            strncpy(core_str, optarg, sizeof(core_str));
            tmp = strtok(core_str, ",");
            while(tmp) {
                if(i > TBL_RING_MAX_NUM - 1){
                    DPI_LOG(DPI_LOG_ERROR, "the max aging thread num is 4");
                    exit(-1);
                }
                g_config.aging_core_id[i++] = atoi(tmp);
                tmp = strtok(NULL, ",");
            }
            g_config.flow_aging_thread_num = i;
        case 'm':
            i = 0;
            strncpy(core_str, optarg, sizeof(core_str));
            tmp = strtok(core_str, ",");
            while(tmp) {
                if(i > TBL_RING_MAX_NUM - 1){
                    DPI_LOG(DPI_LOG_ERROR, "the max sdt match thread num is %d", TBL_RING_MAX_NUM);
                    exit(-1);
                }
                g_config.app_match_core_id[i++] = atoi(tmp);
                tmp = strtok(NULL, ",");
            }
            g_config.app_match_thread_num = i;
            break;
        case 'W':
            if (!is_valid_work_mode(optarg))
            {
                printf("error: %s 不是有效的 work-mode 选项, 有效选项为 'dpdk', 'socket', 'scan:<path>'\n", optarg);
                return -1;
            }

            strncpy(g_config.work_mode, optarg, sizeof g_config.work_mode);
            break;

        case 'l':
            i = 0;
            strncpy(core_str, optarg, sizeof(core_str));
            tmp = strtok(core_str, ",");
            while(tmp) {
                if(i > TBL_RING_MAX_NUM - 1){
                    DPI_LOG(DPI_LOG_ERROR, "the max write tbl thread num is %d", TBL_RING_MAX_NUM);
                    exit(-1);
                }
                g_config.log_core_id[i++] = atoi(tmp);
                tmp = strtok(NULL, ",");
            }
            if(g_config.log_thread_type){
              printf("流表模式`-f`与筛选输出模式`-l`互斥,请检查命令行参数\n");
              exit(0);
            }
            g_config.log_thread_type = 1;
            g_config.log_thread_num = i;
            break;

        case 'f':
            i = 0;
            strncpy(core_str, optarg, sizeof(core_str));
            tmp = strtok(core_str, ",");
            while(tmp) {
                if(i > TBL_RING_MAX_NUM - 1){
                    DPI_LOG(DPI_LOG_ERROR, "the max write flowlog thread num is %d", TBL_RING_MAX_NUM);
                    exit(-1);
                }
                g_config.log_core_id[i++] = atoi(tmp);
                tmp = strtok(NULL, ",");
            }
            if(g_config.log_thread_type){
              printf("流表模式`-f`与筛选输出模式`-l`互斥,请检查命令行参数\n");
              exit(0);
            }
            g_config.log_thread_type = 2;
            g_config.log_thread_num = i;
            break;

        case 'o':
            i = 0;
            strncpy(core_str, optarg, sizeof(core_str));
            tmp = strtok(core_str, ",");
            while(tmp) {
                if(i > TBL_RING_MAX_NUM - 1){
                    DPI_LOG(DPI_LOG_ERROR, "the max sdt out thread num is %d", TBL_RING_MAX_NUM);
                    exit(-1);
                }
                g_config.sdt_out_core[i++] = atoi(tmp);
                tmp = strtok(NULL, ",");
            }
            g_config.sdt_out_thead_num=i;
            break;
        case 'v':
            printf("%s %s ["BUILD_TYPE"] build at %s\n", prgname, PROJECT_VERSION_STR, __DATE__);
            exit(0);

        case 't':
            if(optarg)
                strncpy(g_config.task_id, optarg, sizeof(g_config.task_id) - 1);
            break;
        case 301:
            g_config.test_mode = 1;
            break;
        case 302:
            g_config.input_type = 1;
            strcpy(g_config.pcap_dir, optarg);
            break;
        case 'h':
            print_usage(prgname);
            exit(0);
        case 'c':
            i = 0;
            strncpy(core_str, optarg, sizeof(core_str));
            tmp = strtok(core_str, ",");
            while(tmp) {
                g_config.conv_core_id[i++] = atoi(tmp);
                tmp = strtok(NULL, ",");
            }
            g_config.flow_conv_thread_num = i;
            if (g_config.flow_conv_thread_num > MAX_FLOW_THREAD_NUM) {
                fprintf(stderr, "the max aging thread num is 32\n");
                exit(-1);
            }

            break;
        default:
            print_usage(prgname);
            return -1;
        }
    }

    if(!strlen(g_config.task_id))
        strcpy(g_config.task_id, "0");


    //检测启动参数的完备性
    struct {
        const char*work;
        const char*name;
        int        value;
    } list[]={
        { "收包", "-r", g_config.nb_rxq                },
        { "解析", "-p", g_config.dissector_thread_num  },
        { "匹配", "-m", g_config.app_match_thread_num  },
        { "TBL ", "-l", g_config.log_thread_num        },
        { "FLOW_LOG ", "-f", g_config.log_thread_num   },
        { "PCAP", "-o", g_config.sdt_out_thead_num     },
        { NULL, NULL, 0                              },
    };
    for(int i = 0; list[i].name; i++)
    {
        if(0 == list[i].value)
        {
            //2024-10-31 牡丹江现场出现 sdt_out_thead_num 参数为空导致除0崩溃
            printf("ERROR:%s线程的参数 %s 是空的啊...\n", list[i].work, list[i].name);
            exit(-1);
        }
    }

    return 0;
}


static void sig_to_master(int sig)
{
   g_config.exit_process = 1;
   if(pthread_self() == g_config.master_threadid){
       dpi_stop(sig);
   }
   else{
        //sdt_clean_rule_data_status();
       pthread_kill(g_config.master_threadid, sig);
       //pthread_exit(0);
   }
}

static void exit_signal_handle(int sig)
{
    log_debug("收到信号: %d", sig);
    // 中断主线程事件循环
    socket_main_stop();
}


static int get_flow_hash_nums(void)
{
    unsigned int i = 0;
    int          total_num = 0;
    for (i = 0; i < g_config.dissector_thread_num; i++) {
        struct work_process_data *process = &flow_thread_info[i];
        total_num += dpi_flow_hash_size(process->hash);
    }
    return total_num;
}

// #define XXHASH(value) (XXH32(value, sizeof(uint32_t), 0))

// static uint32_t _get_ring_id(uint32_t saddr, uint32_t daddr, uint16_t sport, uint16_t dport, uint8_t protocol)
// {
//     uint32_t hash = XXHASH(&saddr) ^ XXHASH(&daddr) ^ XXHASH(&sport) ^ XXHASH(&dport) ^ XXHASH(&protocol);
//     return hash % g_config.dissector_thread_num;
// }
//uint64_t hash_index[10]; //debug
int pkt_arrive(const unsigned char *raw_packet, unsigned int raw_packet_len, int use_rss, struct rte_mbuf *mb)
{
    int i, ret, check;
    int cnt_recv_frames;
    unsigned ring_id, free_space;
    uint16_t type;
    uint16_t ip_len, ip_offset = 0;
    const struct dpi_ethhdr  *ethhdr;
    const struct dpi_iphdr   *iph4;
    const struct dpi_ipv6hdr *iph6;
    uint8_t portid, queueid, proto;
    uint16_t sport = 0;
    uint16_t dport = 0;

    const unsigned char *packet = raw_packet;
    unsigned int     packet_len = raw_packet_len;

    /* =========================== sdx  特殊mac头信息 =========================== */
    if (1 == g_config.sdx_config.sdx_mac_packet_header_flag) {

        const uint16_t mac_header_len = sizeof(struct mac_packet_header);
        if (packet_len < mac_header_len) {
            rte_atomic64_inc(&drop_pkts);
            rte_atomic64_add(&drop_bytes, mb->data_len);
            rte_pktmbuf_free(mb);
            return PKT_DROP;
        }

        const struct mac_packet_header *mh = (const struct mac_packet_header *)packet;

        packet     += mac_header_len;
        packet_len -= mac_header_len;

        switch(mh->DataType){
        case 0x47:   // 通联日志
            break;
        case 0x40:  // 匹配命中Ipv4数据
        case 0x41:  // 未匹配命中Ipv4数据
        case 0x45:  // 匹配命中IPv6数据
        case 0x46:  // 未匹配命中IPv6数据
        case 0x42:  // 非IP数据
        case 0xC0:  // 牡丹江现场是UNKNOWN类型.
            break;
        default:
            break;
        }

        /* 特殊MAC头之后的链路层解析 */
        switch (mh->bLinkType) {
            case 0x00:  /* 无链路层信息字段,Data即纯粹的通联日志数据 */
                type = ETH_P_IP;
                goto outer;
            case 0X01:  /* VPI VCI */
            case 0X04:  /* DLCI (FR) */
                packet += mh->bLinkLen;
                packet_len -= mh->bLinkLen;
                break;
            case 0X06:  /* MAC 地址 */
                break;
            case 0X0A:  /* PPP */
            case 0X0B:  /* Cisco PPP */
            case 0X02:  /* 废弃 */
            case 0X03:  /* 废弃 */
            case 0X05:  /* 废弃 */
            case 0X07:  /* 废弃 */
            case 0X08:  /* 废弃 */
            case 0X09:  /* 废弃 */
            case 0XFF:  /* 未定义的链路层信息 */
            default:
                rte_atomic64_inc(&drop_pkts);
                rte_atomic64_add(&drop_bytes, mb->data_len);
                rte_pktmbuf_free(mb);
                return PKT_DROP;
        }
    }

    if(READ_FROM_PCAP == g_config.data_source){
        if((get_uint32_ntohl(packet, 0) == 0x0f000800)){
            if(is_ip4(packet+4, packet_len - 4)){
                type = ETH_P_IP;
                ip_offset = 4;
                goto outer;
            }
            else if(is_ip6(packet+4, packet_len - 4)){
                type = ETH_P_IPV6;
                ip_offset = 4;
                goto outer;
            }
        }
        else if(is_ip4(packet, packet_len)){
            type = ETH_P_IP;
            ip_offset = 0;
            goto outer;
        }
        else if(is_ip6(packet, packet_len)){
            type = ETH_P_IPV6;
            ip_offset = 0;
            goto outer;
        }
    }

    ethhdr = (const struct dpi_ethhdr *)&packet[0];
    ip_offset = sizeof(struct dpi_ethhdr);
    check = ntohs(ethhdr->h_proto);

    //Magic Number, read the fucking IEEE 802.3 documentation
    if (check >= 0x0600){
        type = check;
    }
    else if(check <= 1500){
        ret = rte_ring_mp_enqueue_burst(packet_flow_ring[0], (void * const*)&mb, 1, NULL); //enqueue ring 0
        if (ret < 1) {
            rte_atomic64_inc(&dpi_fail_pkts);
            rte_pktmbuf_free(mb);
            log_trace("队列分发失败:队列满");
        }
        return -1;
    }
    else{
        DPI_LOG(DPI_LOG_DEBUG, "unknown eth packet type");
        rte_atomic64_inc(&drop_pkts);
        rte_atomic64_add(&drop_bytes, mb->data_len);
        rte_pktmbuf_free(mb);
        log_trace("队列分发失败:不支持的链路类型");
        return -1;
    }

//剥洋葱-手艺
strip_again:
    switch(type) {
        case VLAN:
            type = (packet[ip_offset + 2] << 8) + packet[ip_offset + 3];
            ip_offset += 4;
            goto strip_again; //解决 VLAN IN VLAN, ARP IN VLAN


        case MPLS_UNI:
        case MPLS_MULTI:
            {
                uint8_t mpls_cnt;
                ret = skip_mpls((const char*)(packet+ip_offset), packet_len - ip_offset, &type, NULL, &mpls_cnt);
                if (ret <= 0)
                    return PKT_DROP;
                ip_offset += ret;
                goto strip_again; //解决 /eth/vlan/mpls/vlan/mpls/ipv4
            }

        case PPPoE:
            type = ETH_P_IP;
            ip_offset += 8;
            break;

        case ARP:
        case RARP:
            ring_id = mb->hash.rss % g_config.dissector_thread_num;
            ret = rte_ring_mp_enqueue_burst(packet_flow_ring[ring_id], (void * const*)&mb, 1, &free_space);
            if (ret < 1) {
                rte_atomic64_inc(&dpi_fail_pkts);
                rte_pktmbuf_free(mb);
                log_trace("队列分发失败:ARP 对应队列满");
            }
            return -1;

        case VMLAB:
            if (ip_offset + VMLAB_SIZE >= (int)packet_len)
                break;

            type = get_uint16_ntohs(packet + ip_offset, VMLAB_ENCPTYPE_OFFSET);
            ip_offset += VMLAB_SIZE;
            goto strip_again;

        default:
            break;
    }
outer:
    // LLC 在此丢弃!
    // LLDP在此丢弃!
    // 华为交换机 私有协议(0x88a7) 在此丢弃!
    if (type != ETH_P_IP && type != ETH_P_IPV6) {
        rte_atomic64_inc(&drop_pkts);
        rte_atomic64_add(&drop_bytes, mb->data_len);
        rte_pktmbuf_free(mb);
        log_trace("丢弃未知类型报文 %04x", type);
        return -1;
    }


    iph4 = (const struct dpi_iphdr *) &packet[ip_offset];

    if (iph4->version == 4) {
        ip_len = ((uint16_t)iph4->ihl * 4);
        proto = iph4->protocol;
        iph6 = NULL;
    } else if (iph4->version == 6) {
        iph6 = (const struct dpi_ipv6hdr *)&packet[ip_offset];
        proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
        ip_len = sizeof(struct dpi_ipv6hdr);
        iph4 = NULL;
        if (proto == IPPROTO_DSTOPTS /* IPv6 destination option */) {
            const uint8_t *options = (const uint8_t*)&packet[ip_offset + ip_len];
            proto = options[0];
            ip_len += 8 * (options[1] + 1);
        }
    } else if(0==g_config.sdx_config.sdx_mac_packet_header_flag){
        DPI_LOG(DPI_LOG_DEBUG, "unknown ip type");
        rte_atomic64_inc(&drop_pkts);
        rte_atomic64_add(&drop_bytes, mb->data_len);
        rte_pktmbuf_free(mb);
        log_trace("队列分发失败:不是IPV4, 不是IPV6 sdx_mac_packet_header_flag=0");
        return -1;
    }

    /*gtp协议按照内层ip地址分发*/
    if (proto == IPPROTO_UDP) {
        const struct dpi_udphdr *udp = (const struct dpi_udphdr *)&packet[ip_offset + ip_len];
        sport = ntohs(udp->source);
        dport = ntohs(udp->dest);

        if (((sport == GTP_U_V1_PORT) || (dport == GTP_U_V1_PORT))
                && (ip_offset + ip_len + sizeof(struct dpi_udphdr) + 8 + 20 <= packet_len)) {

            u_int offset = ip_offset + ip_len + sizeof(struct dpi_udphdr);
            uint8_t flags = packet[offset];
            uint8_t message_type = packet[offset + 1];

            if ((((flags & 0xE0) >> 5) == 1 /* GTPv1 */)
                && (message_type == 0xFF /* T-PDU */))
            {
                uint16_t gtp_offset = 0;
                dpi_gtp_skip_pdu_head(packet + offset, packet_len - offset, &gtp_offset);

                ip_offset = offset + gtp_offset; /* GTPv1 header len */

                iph4 = (const struct dpi_iphdr *) &packet[ip_offset];

                if (iph4->version == 6) {
                    iph6 = (const struct dpi_ipv6hdr *)&packet[ip_offset];
                    iph4 = NULL;
                } else if (iph4->version != 4) {
                    rte_atomic64_inc(&drop_pkts);
                    rte_atomic64_add(&drop_bytes, mb->data_len);
                    rte_pktmbuf_free(mb);
                    log_trace("队列分发失败:GTP内层不是IPV4, 不是IPV4");
                    return -1;
                }
            }
        }
    } else if (proto == IPPROTO_TCP) {
      const struct dpi_tcphdr *tcp = (const struct dpi_tcphdr *)&packet[ip_offset + ip_len];
      sport                        = ntohs(tcp->source);
      dport                        = ntohs(tcp->dest);
    }
    if (proto == IPPROTO_TCP) {
        const struct dpi_tcphdr *tcp = (const struct dpi_tcphdr *)&packet[ip_offset + ip_len];
        sport = ntohs(tcp->source);
        dport = ntohs(tcp->dest);
    }

    if (iph4) {
        if(use_rss)
        {
            ring_id = mb->hash.rss % g_config.dissector_thread_num;
        }

        else
        {
            //协信办公司网络数据 -- 均衡效果
            //hash_idnex[0]=162641
            //hash_idnex[1]=171898
            //hash_idnex[2]=150243
            //hash_idnex[3]=146331
            //hash_idnex[4]=181789
            //hash_idnex[5]=147452
            // 一种正反向 队列均衡分配 实现算法 -- Power By chunli.
            uint32_t shash = JHASH_INITVAL;
            shash  = jhash(iph4->saddr, 4, shash);
            shash  = jhash(iph4->daddr, 4, shash);
            shash  = jhash(&sport, 2, shash);
            shash  = jhash(&dport, 2, shash);

            uint32_t dhash = JHASH_INITVAL;
            dhash  = jhash(iph4->daddr, 4, dhash);
            dhash  = jhash(iph4->saddr, 4, dhash);
            dhash  = jhash(&dport, 2, dhash);
            dhash  = jhash(&sport, 2, dhash);

            uint32_t hash = shash ^ dhash;
            //ATOMIC_FETCH_ADD(hash_index + (hash % 6)); //debug
            ring_id = hash % g_config.dissector_thread_num;
        }

        ret = rte_ring_mp_enqueue_burst(packet_flow_ring[ring_id], (void * const*)&mb, 1, &free_space);
        if (ret < 1) {
            rte_atomic64_inc(&dpi_fail_pkts);
            rte_pktmbuf_free(mb);
            log_trace("队列分发失败:队列满 ipv4 队列 %d 已满", ring_id);
        }

    } else if (iph6) {
        if(use_rss)
        {
            ring_id = mb->hash.rss % g_config.dissector_thread_num;
        }
        else
        {
            // 一种正反向 队列均衡分配 实现算法 -- Power By chunli.
            uint32_t shash = JHASH_INITVAL;
            shash  = jhash(iph6->ip6_src, 8, shash);
            shash  = jhash(iph6->ip6_dst, 8, shash);
            shash  = jhash(&sport, 2, shash);
            shash  = jhash(&dport, 2, shash);

            uint32_t dhash = JHASH_INITVAL;
            dhash  = jhash(iph6->ip6_dst, 8, dhash);
            dhash  = jhash(iph6->ip6_src, 8, dhash);
            dhash  = jhash(&dport, 2, dhash);
            dhash  = jhash(&sport, 2, dhash);
            uint32_t hash = shash ^ dhash;
            //ATOMIC_FETCH_ADD(hash_index  + (hash % 6)); //debug
            ring_id = hash % g_config.dissector_thread_num;
        }
        ret = rte_ring_mp_enqueue_burst(packet_flow_ring[ring_id], (void * const*)&mb, 1, &free_space);
        if (ret < 1) {
            rte_atomic64_inc(&dpi_fail_pkts);
            rte_pktmbuf_free(mb);
            log_trace("队列分发失败:队列满 ipv6 队列 %d 已满", ring_id);
        }
    } else {
        rte_atomic64_inc(&drop_pkts);
        rte_atomic64_add(&drop_bytes, mb->data_len);
        rte_pktmbuf_free(mb);
        log_trace("队列分发失败: DROP 不是IPV4, 不是IPV6");
    }
    return 0;
}

int flow_flood_performance_test(const char *dirname)
{
    //装载
    struct flood_context_t *ctx = flow_flood_init(dirname);
    struct flood_pkt_t *pkt = NULL;

    //放大
    while((pkt = flow_flood_next(ctx)))
    {
        struct rte_mbuf *mbuf = NULL;
        while(NULL == mbuf)
        {
            mbuf = rte_pktmbuf_alloc(pktmbuf_pool[g_config.socketid]);
        }

        if(pkt->pkt_len < g_config.mbuf_size)
        {
            uint8_t *packet = (uint8_t *)mbuf->buf_addr + mbuf->data_off;
            mbuf->data_len = pkt->pkt_len;
            mbuf->pkt_len  = pkt->pkt_len;

            memcpy(packet, pkt->pkt_ptr, pkt->pkt_len);
            pkt_arrive(packet, pkt->pkt_len, 0, mbuf);
        }
        else
        {
            printf("嘿嘿, 遇到了巨帧, 丢掉. mb=%u pkt.len=%u\n", g_config.mbuf_size, pkt->pkt_len);
        }
    }

    //结束
    flow_flood_free(ctx);
    return 0;
}

int pcap_pkt_cb(unsigned char *p, int l, void *user)
{
    struct rte_mbuf *mbuf = NULL;
    while(NULL == mbuf)
    {
        mbuf = rte_pktmbuf_alloc(pktmbuf_pool[g_config.socketid]);
    }

    if(l < g_config.mbuf_size)
    {
        uint8_t *packet = (uint8_t *)mbuf->buf_addr + mbuf->data_off;
        mbuf->data_len = l;
        mbuf->pkt_len  = l;

        memcpy(packet, p, l);
        pkt_arrive(packet, l, 0, mbuf);
    }
    else
    {
        printf("嘿嘿, 遇到了巨帧, 丢掉. mb=%u pkt.len=%u\n", g_config.mbuf_size, l);
    }

    return 0;
}

// 线程信号
static uint8_t  pkt_receiving_running = 0;
static uint16_t pkt_receiving_lcores[RCV_CORE_MAX_NUM] = {0};

static uint8_t  pkt_decode_running = 0;
static uint16_t pkt_decode_lcores[MAX_FLOW_THREAD_NUM] = {0};

static uint8_t   flow_aging_running = 0;
static pthread_t flow_aging_lcores[MAX_FLOW_THREAD_NUM] = {0};

uint8_t   flow_conv_running = 0;
pthread_t flow_conv_lcores[MAX_FLOW_THREAD_NUM] = {0};

/*收包线程处理函数*/
static int eth_dev_rx_burst(void *ptr_data)
{
    int i, ret;

    uint16_t         packet_len;
    const uint8_t   *packet;

    struct rte_mbuf  *mb;
    struct rte_mbuf  *pkts_burst[512];

    int      burst_num = DPI_MIN(512, g_config.max_pkt_burst);
    int      cnt_recv_frames;
    uint64_t receive_bytes_per_burst;

    uint8_t  portid;
    unsigned queueid = (unsigned long)ptr_data;
    unsigned lcore_id = rte_lcore_id();
    printf("在收包线程 %u 工作在所有网卡的第%u队列上\n", lcore_id, queueid);

    // 确定从哪些网口收包
    int port_index;
    struct {
        uint8_t    *ary;
        int         cnt;
    } recv_ports;

    if (READ_FROM_PCAP == g_config.data_source)
    {
        recv_ports.ary = &g_config.pcap_port_id;
        recv_ports.cnt = 1;
    }
    else
    {
        recv_ports.ary = g_config.sdx_config.sdx_rx_port_list;
        recv_ports.cnt = g_config.sdx_config.sdx_rx_port_num;
    }

    //报文放大器 极限测试 内测用
    //flow_flood_performance_test("/root/pcaps/template");

    //报文目录读取(不丢包)
    //read_pcap("/home/<USER>/", pcap_pkt_cb, NULL);

    /*
    *每个收包线程从每个网卡对应的队列中取出数据包
    *如果有4个收包线程，则每个网卡有四个收包队列，4个收包线程分别从这4个一一对应的队列中取出数据包
    */

    //清空启动前的 网卡芯片 寄存器的计数
    for (port_index = 0; port_index < recv_ports.cnt; port_index++)
    {
        rte_eth_stats_reset(port_index);
    }
    while (1) {

        if (unlikely(pkt_receiving_running == 0))
            break;

        //收包 暂停 信号, 启动时默认为 0
        if(unlikely(dpi_do_recv))
        {
            sleep(1);
            continue;
        }

        //PCAP 模式下等待 vtysh 下发收包指令
        if(READ_FROM_PCAP == g_config.data_source && g_config.start_offline_dissect)
        {
            usleep(1000 *500);
            continue;
        }
        for (port_index = 0; port_index < recv_ports.cnt; port_index++)
        {
            portid = recv_ports.ary[port_index];

            cnt_recv_frames = rte_eth_rx_burst(portid, queueid, pkts_burst, burst_num);
            if (cnt_recv_frames < 0)//区分cnt_recv_frames < 0 或 == 0
                continue;
            if(cnt_recv_frames){
                rte_atomic64_add(&receive_pkts, cnt_recv_frames);
                receive_bytes_per_burst = 0;
            }

            for (i = 0; i < cnt_recv_frames; i++) {
              if (likely(i < cnt_recv_frames - 1)) {
                rte_prefetch0(rte_pktmbuf_mtod(pkts_burst[i + 1], void *));
              }
                mb = pkts_burst[i];
                receive_bytes_per_burst += mb->data_len;
                packet = (const uint8_t *)mb->buf_addr + mb->data_off;
                packet_len = mb->data_len;

                int use_rss = !(g_config.rss_use_type[portid] == RSS_USE_CUSTOM);
                pkt_arrive(packet, packet_len, use_rss, mb);
                //rte_pktmbuf_free(mb); //debug
            }

            //集够了一起加减少原子操作
            if(cnt_recv_frames)
                rte_atomic64_add(&receive_bytes, receive_bytes_per_burst);

            //离线检测上一轮cnt_recv_frames是否小于最大值,如小于说明包已读完,如配置停止开关,则退出程序
            if(READ_FROM_PCAP == g_config.data_source
                    && g_config.offline_read_over_stop
                    && cnt_recv_frames == 0
                    && 0 == strncmp((const char*)pkts_burst, "PCAPOVER", 8))
            {
                _dpi_offline_stop();
            }
        }
    }
    return 0;
}

/* 启动网卡收包线程 */
int eth_dev_rx_start(unsigned int *start_lcore, unsigned int total_threads)
{
    long         thread_id;
    unsigned int lcore_id;

    pkt_receiving_running = 1;

    for(thread_id = 0, lcore_id = *start_lcore;
        thread_id < total_threads && lcore_id < RTE_MAX_LCORE;
        thread_id++, lcore_id = rte_get_next_lcore(lcore_id, 1, 0))
    {
        if (0 != rte_eal_remote_launch(eth_dev_rx_burst, (void*)thread_id, lcore_id))
            break;

        pkt_receiving_lcores[thread_id] = lcore_id;
    }

    *start_lcore = lcore_id;
    return thread_id;
}

/* 停止网卡收包线程 */
void eth_dev_rx_stop(void)
{
    int i;

    pkt_receiving_running = 0;

    for (i = 0; pkt_receiving_lcores[i] > 0; i++)
    {
        rte_eal_wait_lcore(pkt_receiving_lcores[i]);
        pkt_receiving_lcores[i] = 0;
    }
}

/**启动收包线程 */
int pkt_receiving_start(unsigned int *start_lcore, unsigned int total_threads)
{
    if (g_config.input_type == 0)
        return eth_dev_rx_start(start_lcore, total_threads);

    if (g_config.input_type == 1)
    {
        dpi_offline_init();
        dpi_offline_start();
        return total_threads;
    }

    return 0;
}

/** 停止收包线程 */
void pkt_receiving_stop(void)
{
    if (g_config.input_type == 0)
        eth_dev_rx_stop();

    if (g_config.input_type == 1)
        dpi_offline_stop();

    log_trace("收包线程退出");
}

/*解析线程的处理函数*/
static int pkt_decode(void *_thread_id)
{
    int  i, cnt_recv_frames = 0;
    long thread_id = (long) _thread_id;
    struct work_process_data *workflow = NULL;
    struct rte_mbuf *pkts_burst[MAX_PKT_BURST * 2];
    struct rte_mbuf  *mb;
    uint64_t mb_timestamp        = 0;
    time_t time_cycle = 0;

    workflow = &flow_thread_info[thread_id];

    unsigned lcore_id = rte_lcore_id();
    log_info("process func locore_id = %d, thread_id = %ld", lcore_id, thread_id);

    th_alloc = ya_allocator_create_pool(YA_ALLOC_FLAG_NONE);
    workflow->pEngine=sdtEngine_init(thread_id, g_config.sdt_switch_mult_hit);
    if(!workflow->pEngine){
        log_error("[ERROR]sdtEngine_init sdt init failed");
        exit(-1);
    }

    while (1) {
        if (unlikely(pkt_decode_running == 0)) {
            if(rte_ring_empty(packet_flow_ring[thread_id]))
                break;
        }

      if (g_config.g_now_time - time_cycle > 3) {
        rte_timer_manage();
        time_cycle = g_config.g_now_time;
      }
      cnt_recv_frames = rte_ring_sc_dequeue_burst(packet_flow_ring[thread_id], (void **)pkts_burst, MAX_PKT_BURST * 2, NULL);

      if (cnt_recv_frames <= 0) {
          usleep(1000*20);
          continue;
      }

      g_metrics.mbuf[thread_id].total += cnt_recv_frames;

      if (unlikely(g_config.stop_rcv_pkts)) {
        for (i = 0; i < cnt_recv_frames; i++) {
          mb = pkts_burst[i];
          rte_pktmbuf_free(mb);
        }
        do_all_flow_free(thread_id);  // 全部立即超时
        continue;                     // of while
      }

        for (i = 0; i < cnt_recv_frames; i++) {
            mb = pkts_burst[i];
            // mb_timestamp = dpi_mbuf_get_timestamp(mb);
            mb_timestamp = g_config.g_now_time_usec;
            g_proto_layer_cnt = 0;
            workflow->mbuf = mb;

            workflow_process_packet2(workflow,
                    g_config.g_now_time_usec,
                    (const u_char *)mb->buf_addr + mb->data_off,
                    mb->data_len, mb_timestamp,
                    0);
            rte_pktmbuf_free(mb);
        }
        /*超时检测*/
        workflow->last_time = g_config.g_now_time_usec;
    }

    // 收尾, 将会话全部超时
    do_all_flow_timeout(workflow);

    sdtEngine_fini(workflow->pEngine);

    /*此处destroy会导致的后果:
        匹配线程和tbl输出线程在precord_destroy时, 使用的th_alloc已经被free了
      解决: 不手动释放内存, 在进程结束后,由操作系统回收
    */
    // ya_allocator_destroy(th_alloc);

    return 0;
}

/*启动组包文件io线程*/
unsigned pkt_conv_start(unsigned int *start_lcore, unsigned int total_threads)
{
    long         thread_id;
    unsigned int lcore_id; 

    flow_conv_running = 1;

    for(thread_id = 0, lcore_id = *start_lcore;
        thread_id < total_threads && lcore_id < RTE_MAX_LCORE;
        thread_id++, lcore_id = rte_get_next_lcore(lcore_id, 1, 0))
    {
        if (0 != rte_eal_remote_launch(conversation_thread_func, (void *)thread_id, lcore_id))
            break;

        flow_conv_lcores[thread_id] = lcore_id;
    }

    *start_lcore = lcore_id;
    return thread_id;
}

/* 启动报文解析线程 */
unsigned pkt_decode_start(unsigned int *start_lcore, unsigned int total_threads)
{
    long         thread_id;
    unsigned int lcore_id; 

    pkt_decode_running = 1;

    for(thread_id = 0, lcore_id = *start_lcore;
        thread_id < total_threads && lcore_id < RTE_MAX_LCORE;
        thread_id++, lcore_id = rte_get_next_lcore(lcore_id, 1, 0))
    {
        if (0 != rte_eal_remote_launch(pkt_decode, (void *)thread_id, lcore_id))
            break;

        pkt_decode_lcores[thread_id] = lcore_id;
    }

    *start_lcore = lcore_id;
    return thread_id;
}

/* 停止报文解析线程 */
void pkt_decode_stop(void)
{
    int i;

    pkt_decode_running = 0;

    for (i = 0; pkt_decode_lcores[i] > 0; i++)
    {
        rte_eal_wait_lcore(pkt_decode_lcores[i]);
        pkt_decode_lcores[i] = 0;
    }

    log_trace("解析线程退出");
}

/* 停止报文解析线程 */
void pkt_conv_stop(void)
{
    int i;

    flow_conv_running = 0;

    for (i = 0; flow_conv_lcores[i] > 0; i++)
    {
        rte_eal_wait_lcore(flow_conv_lcores[i]);
        flow_conv_lcores[i] = 0;
    }

    log_trace("解析线程退出");
}

static void init_flow_id(void)
{
    int i, fd, ifs;
    struct ifreq buf[16];
    struct ifconf ifc;

    if( (fd = socket(AF_INET, SOCK_DGRAM, 0)) < 0){
        goto random;
    }

    ifc.ifc_len = sizeof(buf);
    ifc.ifc_buf = (caddr_t)buf;

    if(ioctl(fd, SIOCGIFCONF, (char*)&ifc)){
        close(fd);
        goto random;
    }

    ifs = ifc.ifc_len / sizeof(struct ifreq);

    for(i=0; i<ifs; i++){
        printf("device name is %s\n", buf[i].ifr_name);
        if(strcmp(buf[i].ifr_name, "lo") == 0)
            continue;
        if(!(ioctl(fd, SIOCGIFHWADDR, (char*)&buf[i]))){
            memcpy((uint8_t*)&g_config.mac, &buf[i].ifr_hwaddr.sa_data[2], 4);
            if(g_config.mac)
                break;
        }
    }

    close(fd);
    if(g_config.mac)
        return;
random:
    srand(g_config.master_threadid);
    g_config.mac = rand();
    return;
}

static int init_packet_cache_mem(unsigned nb_mbuf)
{
    int socketid;
    unsigned lcore_id;
    char s[64];

    for (lcore_id = 0; lcore_id < RTE_MAX_LCORE; lcore_id++) {
        if (rte_lcore_is_enabled(lcore_id) == 0)
            continue;

        if (numa_on)
            socketid = rte_lcore_to_socket_id(lcore_id);
        else
            socketid = 0;

        if (socketid >= NB_SOCKETS) {
            DPI_LOG(DPI_LOG_ERROR, "Socket %d of lcore %u is out of range %d",
                    socketid, lcore_id, NB_SOCKETS);
            exit(-1);
        }

        if (pktmbuf_pool[socketid] == NULL) {
            snprintf(s, sizeof(s), "mbuf_pool_%d", socketid);
            pktmbuf_pool[socketid] =
                rte_pktmbuf_pool_create(s, nb_mbuf,
                    g_config.mempool_cache_size, 0,
                    g_config.mbuf_size + RTE_PKTMBUF_HEADROOM, socketid);
            if (pktmbuf_pool[socketid] == NULL) {
                DPI_LOG(DPI_LOG_ERROR, "Cannot init mbuf pool on socket %d", socketid);
                exit(-1);
            } else
                DPI_LOG(DPI_LOG_DEBUG, "Allocated mbuf pool on socket %d", socketid);
        }
    }
    return 0;
}

static void init_flow_obj(struct rte_mempool *mp, __attribute__((unused)) void *arg,
        void *obj, unsigned i)
{
    UNUSED(i);
    memset(obj, 0, mp->elt_size);
}

static int init_tcp_reassemble_mempool(void)
{
    return 0;
}

static int init_flow_module(int thread_num) {
    int thread_id;
    char hash_name[64] = {0};
    int i;

    /* setup config */
    for(thread_id = 0; thread_id < thread_num; thread_id++) {
        memset(&flow_thread_info[thread_id], 0, sizeof(flow_thread_info[thread_id]));
        flow_thread_info[thread_id].stats.min_packet_len = 1500;
        flow_thread_info[thread_id].thread_id = thread_id;
        flow_thread_info[thread_id].hash = dpi_flow_hash_new(thread_id);
        if (flow_thread_info[thread_id].hash == NULL) {
            DPI_LOG(DPI_LOG_ERROR, "create flow hash failed");
            exit(-1);
        }

        flow_thread_info[thread_id].block_buff=(uint8_t *)malloc(sizeof(char)*g_config.sdt_block_buff_size);
        if(NULL==flow_thread_info[thread_id].block_buff){
            DPI_LOG(DPI_LOG_ERROR, "malloc sdt block buff failed");
            exit(-1);
        }

        for (i = 0; i < TIMEOUT_MAX; i++) {
            INIT_LIST_HEAD(&flow_thread_info[thread_id].timeout_head[i]);
        }
    }

    dpi_flow_init();

    return 0;
}

static void * flow_aging_thread(void *arg)
{
    struct flow_info *flow_burst[MAX_FLOW_BURST];
    struct flow_info *flow           = NULL;
    uint32_t          flow_burst_cnt = 0;
    uint8_t           core_id        = 0;
    uint8_t           ring_id = 0;
    pthread_t         thread         = pthread_self();
    int               ret            = 0;
    char              thread_name[16];
    DpiThreadArgs *thread_args = (DpiThreadArgs *)arg;

    th_alloc = ya_allocator_create_pool(YA_ALLOC_FLAG_NONE);

    core_id = thread_args->core_id;
    ring_id = thread_args->ring_id;

    snprintf(thread_name, 16, "dpi_aging_%u", core_id);
    pthread_setname_np(thread, thread_name);

    ret = dpi_pthread_setaffinity(thread, core_id);
    if (!ret) {
        DPI_LOG(DPI_LOG_ERROR, "Flow aging thread failed to bind core_id!!!");
    }

    log_info("Flow aging running thread %ld, thread_id %ld, ring_id %u . on core %u",
            syscall(SYS_gettid), thread, ring_id, core_id);

    while (true) {

        if (unlikely(flow_aging_running == 0)) {
            if (rte_ring_empty(flow_aging_ring[ring_id]))
                break;
        }

        flow_burst_cnt = rte_ring_sc_dequeue_burst(flow_aging_ring[ring_id], (void **)flow_burst, MAX_FLOW_BURST, NULL);
        if (flow_burst_cnt == 0) {
            // printf("flow_again receive cnt 0\n");
            // sleep(1);
            continue;
        }
        for (uint32_t i = 0; i < flow_burst_cnt; ++i) {
            flow = flow_burst[i];

            dpi_flow_timeout(flow);
        }
    }

    // ya_allocator_destroy(th_alloc);

    return NULL;
}
static void init_flow_aging(void)
{
    int       status;
    char flow_aging_ring_name[64] = {0};
    uint8_t   core_id;

    flow_aging_running = 1;

    for (unsigned int i = 0; i < g_config.flow_aging_thread_num; ++i) {

      char flow_aging_ring_name[64] = {0};
      snprintf(flow_aging_ring_name, sizeof(flow_aging_ring_name), "flow_aging_ring_%d_%d", g_config.socketid, i);
      flow_aging_ring[i] =
          rte_ring_create(flow_aging_ring_name, g_config.packet_ring_size, g_config.socketid, RING_F_SC_DEQ);
      if (flow_aging_ring[i] == NULL) {
        DPI_LOG(DPI_LOG_ERROR, "error while create packet ring");
        exit(-1);
      }
      if (rte_ring_lookup(flow_aging_ring_name) != flow_aging_ring[i]) {
        DPI_LOG(DPI_LOG_ERROR, "Cannot lookup ring from its name");
        exit(-1);
      }
    }

    for (unsigned int i = 0; i < g_config.flow_aging_thread_num; ++i) {
        DpiThreadArgs *args = dpi_malloc(sizeof(DpiThreadArgs));

        if (g_config.aging_core_id[i] != 0) {
            args->core_id = g_config.aging_core_id[i];
        } else {
            args->core_id = dpi_numa_get_suitable_core(g_config.socketid);
        }
        printf("flow again core id %d\n", args->core_id);

        args->ring_id = i;
        status = pthread_create(&args->thread_id, NULL, flow_aging_thread, (void *)args);
        if (status != 0) {
            DPI_SYS_LOG(DPI_LOG_ERROR, "error on create flow aging thread");
            exit(-1);
        }
        dpi_numa_set_used_core(args->core_id);
    }
}

void flow_aging_stop(void)
{
    int i;

    flow_aging_running = 0;

    for (i=0; flow_aging_lcores[i] > 0; i++)
    {
        pthread_join(flow_aging_lcores[i], NULL);
    }

    log_trace("会话老化线程退出");
}


static void decode_initial(void)
{
    for(int i = 0; decode[i]; i++)
    {
        struct decode_t  *d = decode[i];
        d->decode_initial(d);
#ifndef DPI_SDT_ZDY 
        if (g_config.decode_identity_switch){
            d->identify_type  =   DPI_IDENTIFY_PORT_CONTENT;
        }
#endif
    }
}

static void decode_destroy(void)
{
    for(int i = 0; decode[i]; i++)
    {
        struct decode_t  *d = decode[i];
        d->decode_destroy(d);
    }
}

static void init_dissector_module(void)
{
    int i;
    for(i=0;i<TBL_LOG_MAX;i++){
        if(tbl_log_array[i].init_func!=NULL){
            tbl_log_array[i].init_func();
        }
    }
}

/*
 * 解析线程初始化创建
*/
static void init_dissector_thread(void)
{
    int status;
    unsigned int i;

    for (i = 0; i < g_config.dissector_thread_num; i++) {
        char packet_ring_name[64] = {0};
        snprintf(packet_ring_name, sizeof(packet_ring_name), "packet_ring_%d_%d",g_config.socketid, i);
        packet_flow_ring[i] = rte_ring_create(packet_ring_name, g_config.packet_ring_size, g_config.socketid, RING_F_SC_DEQ);
        if (packet_flow_ring[i] == NULL) {
            DPI_LOG(DPI_LOG_ERROR, "error while create packet ring");
            exit(-1);
        }
        if (rte_ring_lookup(packet_ring_name) != packet_flow_ring[i]) {
            DPI_LOG(DPI_LOG_ERROR, "Cannot lookup ring from its name");
            exit(-1);
        }
    }
}


/*
 * 应用协议解析的fields结果进行sdt规则匹配线程初始化创建
*/
static inline
void init_app_match_thread(void)
{
    sdt_match_start();
}

static void free_eInfo(struct entherInfo* eInfo, int len)
{
    if (NULL == eInfo || len < 0)
        return;

    int iter = 0;
    for (iter = 0; iter < len; iter++)
    {
        if (eInfo[iter].ip != NULL)
        {
            dpi_free(eInfo[iter].ip);
            eInfo[iter].ip = NULL;
        }

        if (eInfo[iter].mac != NULL)
        {
            dpi_free(eInfo[iter].mac);
            eInfo[iter].mac = NULL;
        }
    }
}

static void sortvote(struct voteDevno* voteArr, int len)
{
    // shell sort
    if (NULL == voteArr || len < 0)
        return;

    int i = 0;
    int j = 0;
    int gap = len;
    struct voteDevno temp = { -1,-1 };

    do
    {
        gap = gap / 3 + 1;

        for (i = gap; i < len; i += gap)
        {
            int k = i;
            temp.addr = voteArr[k].addr;
            temp.num = voteArr[k].num;

            for (j = i - gap; j >= 0; j -= gap)
            {
                if (voteArr[j].num < temp.num)
                {
                    voteArr[j + gap].addr = voteArr[j].addr;
                    voteArr[j + gap].num = voteArr[j].num;
                    k = j;
                }
            }
            voteArr[k].addr = temp.addr;
            voteArr[k].num = temp.num;
        }
    } while (gap > 1);

    return;
}

static void addvote(struct voteDevno* voteArr, int len, int devno)
{
    if (NULL == voteArr || len < 0 || devno < 0)
        return;

    int iter = 0;
    int flag = 0;

    for (iter = 0; iter < len; iter++)
    {
        if(voteArr[iter].addr == -1)
            continue;

        if (voteArr[iter].addr == devno)
        {
            voteArr[iter].num++;
            flag = 1;
            break;
        }
    }

    if (!flag)
    {
        for (iter = 0; iter < len; iter++)
        {
            if (voteArr[iter].addr == -1)
            {
                voteArr[iter].addr = devno;
                voteArr[iter].num++;
                break;
            }
        }
    }

    sortvote(voteArr, len);

    return;
}

static int Generate_devno(struct entherInfo* eInfo, int len)
{
    if (NULL == eInfo || len < 0)
        return -1;

    int iter = 0;
    int flag = 0;
    int devno = -1;
    char tmpstr[64];
    memset(tmpstr, 0, sizeof(tmpstr));

    struct voteDevno vote4[ADDR_MAX];
    for (iter = 0; iter < ADDR_MAX; iter++)
    {
        vote4[iter].addr = -1;
        vote4[iter].num = 0;
    }
    struct voteDevno vote6[ADDR_MAX];
    for (iter = 0; iter < ADDR_MAX; iter++)
    {
        vote6[iter].addr = -1;
        vote6[iter].num = 0;
    }

    for (iter = 0; iter < len; iter++)
    {
        if(NULL == eInfo[iter].ip)
            continue;

        if (!strlen(eInfo[iter].ip))
            continue;

        if (strrchr(eInfo[iter].ip, '.'))
        {
            // IP4
            flag = 1;
            strcpy(tmpstr, strrchr(eInfo[iter].ip, '.') + 1);
            devno = atoi(tmpstr);
            addvote(vote4, ADDR_MAX, devno);
        }
        else if (strrchr(eInfo[iter].ip, ':') && strstr(eInfo[iter].ip, "::"))
        {
            // IP6
            flag = 1;
            strcpy(tmpstr, strrchr(eInfo[iter].ip, ':') + 1);
            devno = (int)strtol(tmpstr, NULL, 16);
            addvote(vote6, ADDR_MAX, devno);
        }
    }

    if (flag)
        return vote4[0].num >= vote6[0].num ? vote4[0].addr : vote6[0].addr;
    else
        return -1;
}

static int Get_mac_addr(char *enther, struct entherInfo* eInfo)
{
    if (NULL == enther || NULL == eInfo)
        return -1;

    int ret;
    int sockfd;
    int iter;
    struct ifreq req;
    char tmpstr[32];
    memset(tmpstr, 0, sizeof(tmpstr));
    unsigned char macaddr[ETH_ALEN];
    memset(&macaddr, 0, sizeof(macaddr));

    sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (-1 == sockfd)
        return -1;

    strcpy(req.ifr_name, enther);

    ret = ioctl(sockfd, SIOCGIFHWADDR, &req);
    if (ret != -1)
    {
        eInfo->mac = (char *)dpi_malloc(64);
        if (NULL == eInfo->mac)
            return -1;
        memset(eInfo->mac, 0, 64);
        memcpy(macaddr, req.ifr_hwaddr.sa_data, ETH_ALEN);
        for (iter = 0; iter < ETH_ALEN; iter++)
        {
            sprintf(tmpstr, "%02x:", macaddr[iter]);
            strcat(eInfo->mac, tmpstr);
        }
        if (!strlen(eInfo->mac))
            return -1;
        if (eInfo->mac[strlen(eInfo->mac) - 1] == ':')
            eInfo->mac[strlen(eInfo->mac) - 1] = 0;
    }
    close(sockfd);
    return 0;
}

static int Get_Addr(struct entherInfo* eInfo, int len)
{
    if (NULL == eInfo || len < 0)
        return -1;

    int ret = 0;
    int iter = 0;
    struct ifaddrs *ifaddr = NULL;    // 链表地址
    struct ifaddrs *ifa = NULL;        // 遍历指针
    void *tmpaddrptr = NULL;

    ret = getifaddrs(&ifaddr);
    if (ret != 0)
        return -1;

    for (ifa = ifaddr; ifa != NULL; ifa = ifa->ifa_next)
    {
        // 跳过本地回环和虚拟网卡
        if (strncmp(ifa->ifa_name, "lo", strlen("lo")) == 0 || strncmp(ifa->ifa_name, "virbr", 3) == 0)
            continue;

        if (ifa->ifa_addr->sa_family == AF_INET)
        {
            tmpaddrptr = &((struct sockaddr_in *)ifa->ifa_addr)->sin_addr;
            eInfo[iter].ip = (char *)dpi_malloc(INET_ADDRSTRLEN);
            if (NULL == eInfo[iter].ip)
                return -1;
            memset(eInfo[iter].ip, 0, INET_ADDRSTRLEN);
            inet_ntop(AF_INET, tmpaddrptr, eInfo[iter].ip, INET_ADDRSTRLEN);
            ret = Get_mac_addr(ifa->ifa_name, &(eInfo[iter]));
            if (ret != 0)
                return -1;
            iter++;
        }
        else if (ifa->ifa_addr->sa_family == AF_INET6)
        {
            tmpaddrptr = &((struct sockaddr_in6 *)ifa->ifa_addr)->sin6_addr;
            eInfo[iter].ip = (char *)dpi_malloc(INET6_ADDRSTRLEN);
            if (NULL == eInfo[iter].ip)
                return -1;
            memset(eInfo[iter].ip, 0, INET6_ADDRSTRLEN);
            inet_ntop(AF_INET6, tmpaddrptr, eInfo[iter].ip, INET6_ADDRSTRLEN);
            ret = Get_mac_addr(ifa->ifa_name, &(eInfo[iter]));
            if (ret != 0)
                return -1;
            iter++;
        }
        else
        {
            // *重要* : sa_family != AF_INET or sa_family != AF_INET6
            continue;
        }
    }

    if (ifaddr != NULL) { freeifaddrs(ifaddr); }
    return 0;
}

static void init_deviceinfo(void)
{
    int ret = 0;
    int devno = -1;
    int iter = 0;
    struct entherInfo eInfo[ADDR_MAX];
    for (iter = 0; iter < ADDR_MAX; iter++)
    {
        eInfo[iter].ip = NULL;
        eInfo[iter].mac = NULL;
    }

    if (strncmp(g_config.devno, "AUTO", strlen("AUTO")) != 0)
    {
        if (strlen(g_config.devno) > 0)
            return;
        else
            DPI_LOG(DPI_LOG_WARNING, "devno don't have any data which read from config.ini");
    }
    else
    {
        // config.ini => DEVNO = AUTO
        ret = Get_Addr(eInfo, ADDR_MAX);
        if (ret != 0)
        {
            free_eInfo(eInfo, ADDR_MAX);
            memset(g_config.devno, 0, sizeof(g_config.devno));
            snprintf(g_config.devno, sizeof(g_config.devno), "%s", "unknow");
            return;
        }

        devno = Generate_devno(eInfo, ADDR_MAX);
        if (devno != -1)
        {
            memset(g_config.devno, 0, sizeof(g_config.devno));
            if(devno > 255)
                snprintf(g_config.devno, sizeof(g_config.devno), "%x", devno);    //IP6
            else
                snprintf(g_config.devno, sizeof(g_config.devno), "%d", devno);    //IP4
        }
        else
        {
            free_eInfo(eInfo, ADDR_MAX);
            memset(g_config.devno, 0, sizeof(g_config.devno));
            snprintf(g_config.devno, sizeof(g_config.devno), "%s", "unknow");
            return;
        }
    }

    free_eInfo(eInfo, ADDR_MAX);
    return;
}

void init_ip_location_db(int db_type)
{
    unsigned i;
    if(db_type == 0)
        return;
    //1为只查询ip2region库
    if(db_type == 1)
    {
         if(ip2region_create(&g_config.entry, "./ip2region.db")){
             datablock_entry entry;
             ip2region_memory_search(&g_config.entry, 0, &entry);
             g_config.ip2region_switch = 1;
         }
         goto mmdb_asn;
    }

    //2为优先查询MMDB_CITY库，若未查到值则查询ip2region库
    const char* MMdbPath  = GEOIPDBPATH;
    int status = MMDB_open(MMdbPath, MMDB_MODE_MMAP, &g_config.mmdb_city);

    g_config.mmdb_city_switch = MMDB_SUCCESS != status ? 0 : 1;

mmdb_asn:
    status = MMDB_open(GEOIPASNPATH, MMDB_MODE_MMAP, &g_config.mmdb_asn);
    g_config.mmdb_asn_switch = MMDB_SUCCESS != status ? 0 : 1;

    if( g_config.mmdb_city_switch|| g_config.mmdb_asn_switch)
    {
        g_config.mmdb_switch = 1;
    }

}


// 按行读取
static int readline(int fd, char* buf, int bufSize)
{
    if (-1 == fd || NULL == buf || bufSize <= 0)
        return 0;

    char ch;
    int i = 0;

    while (read(fd, &ch, 1) > 0 && ch != '\n')
    {
        if (i > bufSize - 1)
        {
            i = bufSize - 1;
            break;
        }

        buf[i] = ch;
        i++;
    }

    buf[i] = '\0';

    return i;
}

static void get_history_record(int fd, struct protocol_record *ptr_record, int flag)
{
    if (NULL == ptr_record)
        return;

    char *p1 = NULL;
    char *p2 = NULL;
    char *p3 = NULL;
    int i = PROTOCOL_UNKNOWN;
    char line_buf[64] = { 0 };
    char num1[64] = { 0 };
    char num2[64] = { 0 };

    while (readline(fd, line_buf, sizeof(line_buf)) && i < PROTOCOL_MAX)
    {
        p1 = strchr(line_buf, ':');
        p2 = strchr(line_buf, '+');
        p3 = strchr(line_buf, '*');

        if (p1 != NULL && NULL == p2 && NULL == p3)
        {
            if (flag == 1)
                ptr_record->record_permin[i] = (uint64_t)atoll(p1 + 2);
            else if(flag == 0)
                ptr_record->record_perday[i] = (uint64_t)atoll(p1 + 2);
        }
        else if(p1 != NULL && p2 != NULL && p3 != NULL)
        {
            strncpy(num1, p1 + 2, p2 - p1 - 2 - 1);
            strncpy(num2, p2 + 2, p3 - p2 - 2 - 1);

            if (flag == 1)
            {
                ptr_record->record_permin[i] = (uint64_t)atoll(num1);
                ptr_record->record_permin_circle[i] = (uint64_t)atoll(num2);
            }
            else if (flag == 0)
            {
                ptr_record->record_perday[i] = (uint64_t)atoll(num1);
                ptr_record->record_perday_circle[i] = (uint64_t)atoll(num2);
            }
            memset(num1, 0, 64);
            memset(num2, 0, 64);
        }
        else
        {
            DPI_LOG(DPI_LOG_WARNING, "statistic file format error");
            break;
        }

        i++;
    }
    return;
}

static void init_protocol_record(void)
{
    int i = PROTOCOL_UNKNOWN;

    p_record.fd_min = open(g_config.record_permin_path, O_RDWR | O_CREAT);
    if (-1 == p_record.fd_min)
        return;

    get_history_record(p_record.fd_min, &p_record, 1);

    p_record.fd_day = open(g_config.record_perday_path, O_RDWR | O_CREAT);
    if (-1 == p_record.fd_day)
        return;

    get_history_record(p_record.fd_day, &p_record, 0);

    p_record.last_record_time = time(NULL);

    for (i = PROTOCOL_UNKNOWN; i < PROTOCOL_MAX; i++)
    {
        p_record.proto_name[i] = (char*)malloc(64);
        if (NULL == p_record.proto_name[i])
        {
            DPI_LOG(DPI_LOG_DEBUG, "func init_protocol_record() malloc error");
            return;
        }
        memset(p_record.proto_name[i], 0, 64);

        if (i == PROTOCOL_UNKNOWN)
            strcpy(p_record.proto_name[i], "UNKNOW");
        else
            strcpy(p_record.proto_name[i], protocol_name_array[i]);
    }

    return;
}

#define MAX_NAME_LEN 128
static int init_whitelist_filter(const char* filename, GHashTable** hash_table, long white_or_black)
{
    int i;
    char white_ip[MAX_NAME_LEN];
    char *dump;

    if(*hash_table == NULL){
        *hash_table = g_hash_table_new_full(g_str_hash, g_str_equal, free, NULL);
        if(*hash_table == NULL){
            DPI_LOG(DPI_LOG_ERROR, "fail create whitelist hash table");
            return 1;
        }
    }

    FILE* fp = fopen(filename, "r");
    if(!fp){
        g_hash_table_destroy(*hash_table);
        DPI_SYS_LOG(DPI_LOG_ERROR, "fail open %s file", filename);
        return 1;
    }

    while(fgets(white_ip, MAX_NAME_LEN, fp)){
        //首字母#表注释
        if(white_ip[0] == '#')
            continue;
        i = strnlen(white_ip, MAX_NAME_LEN);
        if(i + 1 == MAX_NAME_LEN){
            printf("ERROR: domain name too long %d in %s\n", i, filename);
            continue;
        }

        //去掉空白
        while(i-- > 0){
            if(isspace(white_ip[i]))
                        white_ip[i] = 0;
            else
                break;
        }
        if(i < 3)
            continue;

        //去掉通配符
        dump = strdup(white_ip[0] == '*' ?  white_ip + 1 : white_ip);
        if(dump){
            g_hash_table_insert(*hash_table, dump, (gpointer)white_or_black);
        }
        else{
            DPI_LOG(DPI_LOG_WARNING, "malloc fail");
        }
    }

    fclose(fp);
    return 0;
}

static int init_ipv4_blacklist(const char* filename)
{
    int i, addr, line = 0;
    char white_ip[MAX_NAME_LEN];
    ipv4_filter_table = g_hash_table_new(g_direct_hash,  g_direct_equal);
    if(!ipv4_filter_table){
         DPI_LOG(DPI_LOG_ERROR, "fail create IPv4 blacklist hash table");
         return 1;
    }

    FILE* fp = fopen(filename, "r");
    if(!fp){
        g_hash_table_destroy(ipv4_filter_table);
        DPI_SYS_LOG(DPI_LOG_ERROR, "fail open %s file", filename);
        return 1;
    }

    while(fgets(white_ip, MAX_NAME_LEN, fp)){
        line += 1;
        //首字母#表注释
        if(white_ip[0] == '#')
            continue;
        i = strnlen(white_ip, MAX_NAME_LEN);
        if(i + 1 == MAX_NAME_LEN){
            printf("ERROR: %dth line IPv4 too long %d in %s\n", line, i, filename);
            continue;
        }

        //去掉空白
        while(--i > 0){
            if(isspace(white_ip[i]))
                white_ip[i] = 0;
            else
                break;
        }
        if(i < 7)
            continue;

        addr = inet_network(white_ip);
        if(addr == -1)
            printf("ERROR: %dth line wrong IPv4 format: %s\n", line, white_ip);
        else{
            g_hash_table_add(ipv4_filter_table, GINT_TO_POINTER(addr));
        }
    }

    fclose(fp);
    return 0;
}



static char* flowtype_to_str(uint16_t ftype)
{
    uint16_t i;
    static struct {
        char str[16];
        uint16_t ftype;
    } ftype_table[] = {
        {"ipv4", RTE_ETH_FLOW_IPV4},
        {"ipv4-frag", RTE_ETH_FLOW_FRAG_IPV4},
        {"ipv4-tcp", RTE_ETH_FLOW_NONFRAG_IPV4_TCP},
        {"ipv4-udp", RTE_ETH_FLOW_NONFRAG_IPV4_UDP},
        {"ipv4-sctp", RTE_ETH_FLOW_NONFRAG_IPV4_SCTP},
        {"ipv4-other", RTE_ETH_FLOW_NONFRAG_IPV4_OTHER},
        {"ipv6", RTE_ETH_FLOW_IPV6},
        {"ipv6-frag", RTE_ETH_FLOW_FRAG_IPV6},
        {"ipv6-tcp", RTE_ETH_FLOW_NONFRAG_IPV6_TCP},
        {"ipv6-udp", RTE_ETH_FLOW_NONFRAG_IPV6_UDP},
        {"ipv6-sctp", RTE_ETH_FLOW_NONFRAG_IPV6_SCTP},
        {"ipv6-other", RTE_ETH_FLOW_NONFRAG_IPV6_OTHER},
        {"l2_payload", RTE_ETH_FLOW_L2_PAYLOAD},
        {"port", RTE_ETH_FLOW_PORT},
        {"vxlan", RTE_ETH_FLOW_VXLAN},
        {"geneve", RTE_ETH_FLOW_GENEVE},
        {"nvgre", RTE_ETH_FLOW_NVGRE},
    };

    for (i = 0; i < RTE_DIM(ftype_table); i++) {
        if (ftype_table[i].ftype == ftype)
            return ftype_table[i].str;
    }

    return NULL;
}

pthread_mutex_t http_mutex;
char init_mutex_flag = 0;

// 全局变量初始化
static void global_initializer(void)
{
    int iter = 0;

    //初始化互斥锁
    if (0 != pthread_mutex_init(&http_mutex, NULL))
    {
        DPI_SYS_LOG(DPI_LOG_DEBUG, "http init mutex error");
        init_mutex_flag = '1';
    }

    for (iter = PROTOCOL_UNKNOWN; iter < PROTOCOL_MAX; iter++)
    {
        p_record.record_permin[iter] = 0;
        p_record.record_permin_circle[iter] = 0;
        p_record.record_perday[iter] = 0;
        p_record.record_perday_circle[iter] = 0;
        p_record.proto_name[iter] = NULL;
    }

    p_record.last_record_time = time(NULL);
    p_record.fd_min = -1;
    p_record.fd_day = -1;
    pthread_mutex_init(&p_record.record_mutex, NULL);

    return;
}

// 全局变量销毁
static void global_destroyer(void)
{
    int i = 0;

    //销毁 Decode
    decode_destroy();

    pthread_mutex_destroy(&http_mutex);
    pthread_mutex_destroy(&p_record.record_mutex);

    for (i = PROTOCOL_UNKNOWN; i < PROTOCOL_MAX; i++)
    {
        if (p_record.proto_name[i] != NULL)
        {
            free(p_record.proto_name[i]);
            p_record.proto_name[i] = NULL;
        }
    }
    close(p_record.fd_min);
    close(p_record.fd_day);

    if(g_config.ip_position_switch == 1)
       ip2region_destroy(&g_config.entry);

    if(g_config.mmdb_city_switch){
        MMDB_close(&g_config.mmdb_city);
    }

    if (g_config.mmdb_asn_switch) {
        MMDB_close(&g_config.mmdb_asn);
    }
#ifdef ENABLE_ARKIME

    dpi_arkime_clean();
#endif


    return;
}

static void show_stop_statics_info(void)
{
        int len=2048;
        char msg[2048]={0};

        int idx = 0;
        unsigned port;
#ifdef _DPI_DPDK_17
        unsigned nb_ports = rte_eth_dev_count();
#else
        unsigned nb_ports = rte_eth_dev_count_avail();
#endif
        unsigned long long forward_pkts = 0,
                   missed_pkts  = 0,
                   error_pkts   = 0,
                   dpi_receive, dpi_fail, tbl_pkts, tbl_bytes;
        struct rte_eth_stats stat_info;

        for(port=0; port < nb_ports; port++){
            if(rte_eth_stats_get(port, &stat_info) == 0){
                forward_pkts += stat_info.ipackets;
                missed_pkts  += stat_info.imissed;
                error_pkts   += stat_info.ierrors;
            }
        }

        dpi_receive = rte_atomic64_read(&receive_pkts);
        dpi_fail    = rte_atomic64_read(&dpi_fail_pkts);
        tbl_pkts    = rte_atomic64_read(&tbl_fail_pkts);
        tbl_bytes   = rte_atomic64_read(&tbl_fail_bytes);

        idx += snprintf(msg+idx, len-idx, "DPDK     : total forward  %llupkts,\ttotal missed  %llupkts,\ttotal errors  %llupkts, fail percent  %lf%%\n",
                            forward_pkts, missed_pkts, error_pkts, forward_pkts + missed_pkts + error_pkts ==  0 ? 0 : (missed_pkts + error_pkts) * 100.0 / (forward_pkts + missed_pkts + error_pkts));
        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "DPI      : total receive  %llupkts,\tfail enqueue  %llupkts,\tfail percent  %lf%%\n",
                                dpi_receive, dpi_fail, dpi_receive ? dpi_fail * 100.0 / dpi_receive : 0);

        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "TBL      : fail  enqueue  %llupkts,%llubytes,%lluKB,%lluMB,%lluGB\n",
                                tbl_pkts, tbl_bytes, tbl_bytes / 1024, tbl_bytes / (1024 * 1024), tbl_bytes / (1024 * 1024 * 1024));

        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "RSM      : fail  get      %ld\n",  rte_atomic64_read(&rsm_fail_get));

        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "Flow     : fail  get      %ld\n",  rte_atomic64_read(&flow_fail_get));

        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "256K LOG : fail  get      %ld\n",  rte_atomic64_read(&log_256k_fail));

        printf("%s\n",msg);
        return;
}

static void dpi_rx_switch(int sig)
{
    dpi_do_recv = !dpi_do_recv;
    printf("信号 [%s]\n", dpi_do_recv ? "暂停收包":"恢复收包");
}


static void dpi_stop(int sig)
{
    g_config.exit_process  = 1;
    pkt_decode_running = 0;
    pkt_receiving_running = 0;
    flow_aging_running = 0;
    //update_protocol_record(1);
    //global_destroyer();
    usleep(10);

    sdt_out_threads_stop();
    sdt_clean_rule_data_status();
    tbl_log_file_close_writing();
#ifdef ENABLE_ARKIME

    dpi_arkime_clean();
#endif

    show_stop_statics_info();

    exit(0);
    //此处 exit 带来的负面影响, 其他线程正在 持有trie::SDTRuleManager内的数据, 引发崩溃
    //(gdb) signal SIGINT
    //(gdb) bt
    //#0  trie::SDTRuleManager::~SDTRuleManager
    //#1  0x00007ffff5f3da69 in __run_exit_handlers ()
    //#2  0x00007ffff5f3dab5 in exit ()
    //#3  0x00000000004de64f in dpi_stop (sig=12)
    //#4  0x00000000004d8991 in sig_to_master (sig=2)
    //#5  <signal handler called>
    //#6  0x00007ffff5ff47a3 in select ()
    //#7  0x000000000063e953 in socket_main (args=0x0)
    //#8  0x00000000004dfb8e in work_dpdk_mode (argc=11, argv=0x7fffffffe3a0)
    //#9  0x00000000004dfe7c in main (argc=14, argv=0x7fffffffe388)
    //(gdb)
}

static void _dpi_offline_stop(void)
{
    while(get_flow_total_num()>0)
    {
        usleep(100);
    }

    sdt_clean_rule_data_status();
    tbl_log_file_close_writing();

    show_stop_statics_info();
    exit(0);
}

static void dpi_module_stop(void)
{
    // 各个模块依次退出
    pkt_receiving_stop();
    pkt_decode_stop();
    flow_aging_stop();
    pkt_conv_stop();
    sdt_match_stop();
    tbl_out_stop();
    sdt_out_threads_stop();

    sdt_clean_rule_data_status();
    tbl_log_file_close_writing();
    show_stop_statics_info();
}


static void dpi_sdt_engine_field_load()
{
    char *value_string = NULL;
    char *rule_proto_name = NULL;
    char str[128] = { 0 };
    // foreach pschema
    for (pschema_t *schema = dpi_pschema_get_first(); schema; schema = dpi_pschema_get_next(schema))
    {
        const char * proto_name = pschema_get_proto_name(schema);
        ya_fvalue_t *rule_value = pschema_get_attribute(schema, "rule_proto_name");
        ya_fvalue_t *alias_value = pschema_get_attribute(schema, "alias");

        if (rule_value == NULL) continue;

        rule_proto_name = ya_fvalue_to_string_repr(rule_value, BASE_NONE);

        for (pfield_desc_t *fdesc = pschema_fdesc_get_first(schema); fdesc; fdesc = pschema_fdesc_get_next(schema, fdesc))
        {
          ya_fvalue_t *fvalue = pfdesc_get_attribute(fdesc, "rule_name");
          const char * field_name = pfdesc_get_name(fdesc);
          if (fvalue == NULL) continue;

          value_string = ya_fvalue_to_string_repr(fvalue, BASE_NONE);
        //   printf("proto_name = %s, rule_proto_name = %s, vlaue_string == %s, field_name = %s\n",proto_name, rule_proto_name, value_string, field_name);
          mapping_rule(rule_proto_name, value_string, proto_name, field_name);

          fvalue = pfdesc_get_attribute(fdesc, "alias");
        //   if (fvalue != NULL) {
          if (alias_value != NULL && value_string != NULL) {
            snprintf(str, sizeof(str), "%s_%s", proto_name, value_string);//MDJ 要求全是下划线.
            mapping_rule(NULL, value_string, NULL, str);
            // printf("alias vlaue_string == %s, field_name = %s\n", str, value_string);
          }

          ya_fvalue_free_string_repr(value_string);
        }

        ya_fvalue_free_string_repr(rule_proto_name);
    }
}


static int dpi_sdx_init(void)
{
    int ret=0;
    char tmp[32]={0};
    char *p=NULL;
    /* 加载编译sdt字段表文件 */
    // if(is_file_exist(g_config.sdt_rules_field)==0){ // json字段转换表文件目录不存在
    //     sdtEngine_field_load(NULL, 0);// 禁用 字段表转换功能
    // }else{
    //     sdtEngine_field_load(g_config.sdt_rules_field, 1);// 启用 字段表转换功能
    // }

    dpi_sdt_engine_field_load();


    /* 选择规则匹配算法 */
    ret=sdtEngine_selectMatchEngine(g_config.sdx_config.sdx_match_algorithm);

    /* 启动规则管理线程      */
    SdtHttpServerArgs_t http_args;
    memset(&http_args, 0, sizeof(SdtHttpServerArgs_t));

    http_args.listen_port=g_config.sdt_local_port;
    snprintf(http_args.case_name, sizeof(http_args.case_name), "%s",g_config.sdx_config.sdx_stat_case_name);
    snprintf(http_args.web_addr, sizeof(http_args.web_addr), "%s", g_config.sdt_web_addr);
    http_args.is_reload_rule=g_config.sdt_reload_rule;
    http_args.is_reflec_rule=g_config.sdt_reflect_rule;

    snprintf(http_args.app_version, sizeof(http_args.app_version),"%s",PROJECT_VERSION_STR);
    p=strchr(http_args.app_version, '_');
    if(p){
        http_args.app_version[p-http_args.app_version] = '\0';
    }
    printf("app_version:%s\n",http_args.app_version);

    uint16_t engineCnt=g_config.dissector_thread_num+g_config.app_match_thread_num;

    sdtEngine_startManageThread_v2(g_config.socketid,
                                   engineCnt,
                                   g_config.sdt_rule_max_num,
                                   &http_args, 1);

    //为libsdt 引擎设定默认的 Action
    ret = sdt_rule_default_action_load(g_config.sdt_rules_default_action);
    if(ret < 0)
    {
        printf("默认Action语法不正确[%s]\n", g_config.sdt_rules_default_action);
        exit(-1);
    }

    //为libsdt 设置是否强制ip带上掩码
    ip_with_mask_set(g_config.sdt_ip_with_mask);

    /* 注册规则事件处理器 */
    SdtRuleEventOps rule_hash_event;
    memset(&rule_hash_event,0,sizeof(SdtRuleEventOps));

    rule_hash_event.rule_event_ops_on_transact_execute_start=SdtAppRuleHash_ruleTransactStart;
    rule_hash_event.rule_event_ops_on_transact_execute_finish=SdtAppRuleHash_ruleTransactFinish;

    rule_hash_event.rule_event_ops_on_rules_cleared=SdtAppRuleHash_RuleInitClear;
    rule_hash_event.rule_event_ops_on_editing_start=SdtAppRuleHash_RuleInitStart;
    rule_hash_event.rule_event_ops_on_editing_finish=SdtAppRuleHash_ruleInitEnd;
    rule_hash_event.rule_event_ops_on_rule_added=SdtAppRuleHash_insertRules;

    sdtEngine_registerRuleEventOps(&rule_hash_event);



    return 0;

}

static int parse_command_line_app_args(int argc, char **argv)
{
    int argc_app;
    char **argv_app;
    int ret = 0;

    /* skip the dpdk args */
    for (argc_app = 1; argc_app < argc && strcmp(argv[argc - argc_app], "--") != 0; argc_app++);
    argv_app = argv + argc - argc_app;

    /* parse app args, argv_app may begin with "--" */
    return parser_args(argc_app, argv_app, argv[0]);
}

static void init_core_id()
{
    dpi_numa_init();

    uint32_t lcore;

    RTE_LCORE_FOREACH(lcore)
    {
        dpi_numa_set_used_core(lcore);
    }
}

static void dpi_init()
{
    init_core_id();

    dpi_metrics_init();
}

static int dpi_dpdk_port_config(void) {
  int ret = 0;
  struct rte_eth_dev_info dev_info;
  struct rte_eth_txconf  *txconf;
  uint16_t portid;
  unsigned int i = 0;

  RTE_ETH_FOREACH_DEV(portid) {
    // 跳过绑定的网卡,只接受离线
    if (g_config.data_source == 3 && portid != g_config.pcap_port_id) continue;

    // DPDK 网卡硬件 RX 接收错误帧 [0: 不接受坏帧. 1:接受坏帧]
    // DPDK 网卡硬件 TX 禁止填充CRC[0: 填充CRC.    1:不填充CRC]
    //  if(g_config.enable_sbp) { port_conf.rxmode.offloads |= DEV_RX_OFFLOAD_SAVE_BAD_PACKET;}
    //  if(g_config.disable_crc){ port_conf.txmode.offloads |= DEV_TX_OFFLOAD_DISABLE_APPEND_CRC;}

    rte_eth_dev_info_get(portid, &dev_info);
    port_conf.rx_adv_conf.rss_conf.rss_hf &= dev_info.flow_type_rss_offloads;
    // 设置收包队列和发包队列
    ret = rte_eth_dev_configure(portid, g_config.nb_rxq, (uint16_t)nb_txq, &port_conf);
    if (ret < 0) {
      DPI_LOG(DPI_LOG_ERROR, "Cannot configure device: err=%d, port=%d", ret, portid);
      exit(-1);
    }
    ret = rte_eth_dev_adjust_nb_rx_tx_desc(portid, &nb_rxd, &nb_txd);
    if (ret < 0) {
      DPI_LOG(DPI_LOG_ERROR, "Cannot adjust number of descriptors: err=%d, port=%d", ret, portid);
      exit(-1);
    }
    /* init one TX queue per couple (lcore,port) */
    for (i = 0; i < nb_txq; i++) {
      // socketid = rte_eth_dev_socket_id(portid);
      rte_eth_dev_info_get(portid, &dev_info);
      txconf = &dev_info.default_txconf;
      ret    = rte_eth_tx_queue_setup(portid, i, nb_txd, g_config.socketid, txconf);
      if (ret < 0) {
        DPI_LOG(DPI_LOG_ERROR, "rte_eth_tx_queue_setup: err=%d, port=%d", ret, portid);
        exit(-1);
      }
    }

    for (i = 0; i < g_config.nb_rxq; i++) {
      // socketid = rte_eth_dev_socket_id(portid);
      ret = rte_eth_rx_queue_setup(portid, i, nb_rxd, g_config.socketid, NULL, pktmbuf_pool[g_config.socketid]);
      if (ret < 0) {
        DPI_LOG(DPI_LOG_ERROR, "rte_eth_rx_queue_setup: err=%d, port=%d", ret, portid);
        exit(-1);
      }
    }
  }

  /* start ports */
  RTE_ETH_FOREACH_DEV(portid) {
    // 跳过绑定的网卡,只接受离线
    if (g_config.data_source == 3 && portid != g_config.pcap_port_id) continue;

    // 设置巨帧支持
    int r = rte_eth_dev_set_mtu(portid, g_config.max_pkt_len);
    if (r == 0) {
      DPI_LOG(DPI_LOG_DEBUG, "Port %i: MTU set to %i", portid, g_config.max_pkt_len);
    } else if (r == -ENOTSUP) {
      DPI_LOG(DPI_LOG_ERROR, "Port %i: Set MTU not supported\n", portid);
      fprintf(stderr, "Port %i: Set MTU not supported\n", portid);
      exit(-1);
    } else {
      DPI_LOG(DPI_LOG_ERROR, "Port %i: Error setting MTU\n", portid);
      fprintf(stderr, "Port %i: Error setting MTU\n", portid);
      exit(-1);
    }

    ret = rte_eth_dev_start(portid);
    if (ret < 0) {
      DPI_LOG(DPI_LOG_ERROR, "rte_eth_dev_start: err=%d, port=%d", ret, portid);
      exit(-1);
    }
    // 开启混杂模式
    rte_eth_promiscuous_enable(portid);
  }

  return 0;
}

static void init_dpdk_eal(int argc, char **argv)
{
    int ret;
    int sid=0;

    ret = rte_eal_init(argc, argv);
    if (ret < 0) {
        DPI_LOG(DPI_LOG_ERROR, "Invalid EAL parameters");
        exit(-1);
    }

    for (sid = 0; sid < RTE_MAX_LCORE; sid++) {
        if (rte_lcore_is_enabled(sid) == 0)
            continue;

        if (numa_on)
            g_config.socketid = rte_lcore_to_socket_id(sid);
        else
            g_config.socketid = 0;

        if (g_config.socketid >= NB_SOCKETS) {
            DPI_LOG(DPI_LOG_ERROR, "CONFIG Socket %d of lcore %u is out of range %d",
                    g_config.socketid, sid, NB_SOCKETS);
            exit(-1);
        }
    }
}

static void init_black_white_list(void)
{
    //白名单标志为１,黑名单标志为2
    if(g_config.https_blacklist_switch && init_whitelist_filter(g_config.https_blacklist_filename, &https_filter_table, 2)){
            printf("fail to initialize HTTPS black list\n");
            g_config.https_blacklist_switch = 0;
    }
    if(g_config.https_whitelist_switch && init_whitelist_filter(g_config.https_whitelist_filename, &https_filter_table, 1)){
            printf("fail to initialize HTTPS white list and delite hash table\n");
            g_config.https_blacklist_switch = 0;
            g_config.https_whitelist_switch = 0;
    }

    if(g_config.dns_blacklist_switch && init_whitelist_filter(g_config.dns_blacklist_filename, &dns_filter_table, 2)){
            printf("fail to initialize DNS black list\n");
            g_config.dns_blacklist_switch = 0;
    }
    if(g_config.dns_whitelist_switch && init_whitelist_filter(g_config.dns_whitelist_filename, &dns_filter_table, 1)){
            printf("fail to initialize DNS white list and delete hash table\n");
            g_config.dns_blacklist_switch = 0;
            g_config.dns_whitelist_switch = 0;
    }

    if(g_config.x509_whitelist_switch && init_whitelist_filter(g_config.x509_whitelist_filename, &x509_filter_table, 1)){
        printf("fail to initialize X509 white list\n");
        g_config.x509_whitelist_switch = 0;
    }

    if(g_config.ipv4_blacklist_switch && init_ipv4_blacklist(g_config.ipv4_blacklist_filename)){
        printf("fail to initialize IPv4 black list\n");
        g_config.ipv4_blacklist_switch = 0;
    }
}

static void init_all_modules(void)
{
    /* 当前必须在 init_dissector_module 之前进行初始化，因为需要先准备好 common 部分 schema */
    dpi_pschema_module_init();
    //获取本机mac后四个字节用作UUID
    init_flow_id();
    //初始化数据包内存池
    init_packet_cache_mem(g_config.nb_mbuf - 1);
    //初始化定时器
    init_thread_timer();
    //初始化会话全局数组
    init_flow_module(g_config.dissector_thread_num);
    init_app_match_ring();
    init_app_match_thread(); /* 应用协议解析结果进行规则匹配线程 */
    // 初始化 流表老化信息
    init_flow_aging();
    init_flow_conv();
    //初始化tcp重组内存池
    init_tcp_reassemble_mempool();
    //初始化各个应用协议解析模块
    init_dissector_module();
    //初始化 decode
    decode_initial();
    //初始化tbl日志相关
    init_tbl_log();
    sdt_io_init();
    // sdx init
    dpi_sdx_init();
    //初始化解析线程
    init_dissector_thread();
    //初始化关联协议
    init_conversation();
    //初始化设备信息
    init_deviceinfo();
    //初始化协议记录
    init_protocol_record();
    init_ip_location_db(g_config.ip_position_switch);

    dpi_sdx_config_after_init_nic(g_config.ini, &g_config.sdx_config);

     /* 命中的sdt包转发的初始化 */
    init_ether_forward(g_config.fwd_type);

    if (strlen(g_config.sdx_config.sdx_web_config_addr) > 0 &&
        g_config.sdx_config.sdx_web_config_listen_port > 0)
    {
        sub_init(g_config.sdx_config.sdx_web_config_addr,
                g_config.sdx_config.sdx_web_config_listen_port,
                g_config.sdx_config.sdx_web_config_topic_name);
    }

    dpi_notify_init();

    LineConvertInit();  //[SDX] 初始化线路转换库
#ifdef ENABLE_ARKIME

    // 注册所有字段到Elasticsearch
    if (g_config.es_config.enabled == 2) {
        dpi_arkime_register_all_fields();
    }
#endif

}

static void init_sdt_module()
{
    init_stat_restful_server(g_config.sdx_config);   /* 统计信息上报初始化 */
}

static int work_dpdk_mode(int argc, char **argv)
{
    uint8_t portid;
    uint8_t socketid;
    int ret = 0;
    unsigned int i;
    unsigned int nb_ports;
    unsigned int lcore_id;
    uint32_t nb_lcores;
    struct rte_eth_txconf *txconf;
    struct rte_eth_dev_info dev_info;
    long         thread_id = 0;

    uint32_t flow_type, idx, offset;
    // struct rte_eth_hash_filter_info info;
    const char* str;
    const char* status;

    init_dpdk_eal(argc, argv);

#ifdef _DPI_DPDK_17
    nb_ports = rte_eth_dev_count();
#else
    nb_ports = rte_eth_dev_count_avail();
#endif
    if (nb_ports == 0) {
      printf("No Ethernet ports - bye\n");
    }
    printf("有效端口个数 :%d\n",nb_ports);
    nb_lcores = rte_lcore_count();
    g_config.nb_rxq = g_config.nb_rxq > nb_lcores ? nb_lcores : g_config.nb_rxq;
    printf("avail nports number:%d, lcores = %d, nb_rxq = %d\n", nb_ports, nb_lcores, g_config.nb_rxq);

    dpi_init();
    init_all_modules();
    init_black_white_list();

    //这里的g_config.sdt_mac_forward_flag导致 X722的VF网卡,PDDK在dev_start()启动过程导致了崩溃
    //nb_txq恢复原先的赋值
    nb_txq = g_config.nb_txq;
    if (g_config.sdt_mac_forward_flag) {
        g_config.nb_txq = g_config.nb_txq > nb_lcores ? nb_lcores : g_config.nb_txq;
        nb_txq = g_config.nb_txq;
    }

    if(g_config.rdp_mutex_switch)
        pthread_mutex_init(&rdp_mutex, 0);

    dpi_dpdk_port_config();

    //注册信号
    g_config.master_threadid = pthread_self();
    signal(SIGINT,   exit_signal_handle);
    signal(SIGTERM,  exit_signal_handle);
    signal(SIGUSR1,  sig_to_master);
    signal(SIGUSR2,  dpi_rx_switch);

    init_sdt_module();

    lcore_id = rte_get_next_lcore(-1, 1, 0);

    if (pkt_receiving_start(&lcore_id, g_config.nb_rxq) != g_config.nb_rxq)
    {
        printf("ERROR: 收包线程无法启动, 参数 逻辑核心数 不够用\n");
        abort();
    }

    if (pkt_decode_start(&lcore_id, g_config.dissector_thread_num) !=
            g_config.dissector_thread_num)
    {
        printf("ERROR: 解析线程无法启动, 参数 逻辑核心数 不够用\n");
        abort();
    }
    if(pkt_conv_start(&lcore_id, g_config.flow_conv_thread_num) !=
            g_config.flow_conv_thread_num)
    {
        printf("ERROR: 组包文件io线程无法启动, 参数 逻辑核心数 不够用\n");
        abort();
    }
    if (g_config.test_mode) {
        // 给测试主进程一个初始化完成的信号
        printf("INIT_DONE:%d\n", getpid());
        fflush(stdout);
    }

    //master线程，用来更新全局时间戳，处理消息
    socket_main(NULL);

    dpi_module_stop();

    for (portid = 0; portid < nb_ports; portid++) {
        rte_eth_dev_stop(portid);
        rte_eth_dev_close(portid);
    }
    /* clean up the EAL */
	// rte_eal_cleanup();

    DPI_LOG(DPI_LOG_DEBUG, "Bye...");

    return ret;
}

int scan_thread_func(void *args)
{
    const char *scan_dir = &g_config.work_mode[5]; /* eg: 'scan:/mnt/dpi_scan' */
    dpi_input_loop_scanning_pcap_in_dir(scan_dir,
                                        g_config.data_input_scanning_infinite,
                                        g_config.data_input_scanning_do_rename,
                                        g_config.data_input_scanning_ignore);
    dpi_stop(0);
    return 0;
}

static int work_scan_mode(int argc, char **argv)
{
    uint8_t portid;
    uint8_t socketid;
    int ret;
    unsigned int i;
    struct rte_eth_txconf *txconf;
    struct rte_eth_dev_info dev_info;
    uint32_t nb_lcores;
    uint32_t flow_type, idx, offset;
    // struct rte_eth_hash_filter_info info;
    const char* str;
    const char* status;
    unsigned int lcore_id;
    long         thread_id = 0;

    init_dpdk_eal(argc, argv);
    nb_lcores = rte_lcore_count();

    dpi_init();
    init_all_modules();
    init_black_white_list();

    //这里的g_config.sdt_mac_forward_flag导致 X722的VF网卡,PDDK在dev_start()启动过程导致了崩溃
    //nb_txq恢复原先的赋值
    nb_txq = g_config.nb_txq;
    if (g_config.sdt_mac_forward_flag) {
        g_config.nb_txq = g_config.nb_txq > nb_lcores ? nb_lcores : g_config.nb_txq;
        nb_txq = g_config.nb_txq;
    }

    g_config.data_source = READ_FROM_PCAP;
    //PCAP 模式下 限制 nb_mbuf, 保证不丢包
    if(READ_FROM_PCAP == g_config.data_source && g_config.nb_mbuf > g_config.packet_ring_size)
    {
        printf("PCAP 模式下, nb_mbuf:%u 大于 packet_ring_size:%u 可能会导致丢包\n", g_config.nb_mbuf, g_config.packet_ring_size);
        exit(-1);
    }

    //注册信号
    g_config.master_threadid = pthread_self();
    signal(SIGINT,   sig_to_master);
    signal(SIGTERM,  sig_to_master);
    signal(SIGUSR1,  sig_to_master);
    signal(SIGUSR2,  dpi_stop);

    //等待解析线程启动后再启动收包线程
    sleep(1);

    init_sdt_module();

    //开启扫描线程
    // rte_eal_mp_remote_launch(scan_thread_func, NULL, SKIP_MAIN);

    unsigned int recv_cnt = 0;
    unsigned int process_cnt = 0;
    thread_id = 0;
    RTE_LCORE_FOREACH_WORKER(lcore_id) {
      printf("======= lcorea_id = %d\n", lcore_id);
      if (recv_cnt < g_config.nb_rxq ) {
        ret = rte_eal_remote_launch(scan_thread_func, NULL, lcore_id);
        recv_cnt++;
        printf("create receive func ret = %d\n", ret);
        continue;
      }

      if (process_cnt < g_config.dissector_thread_num) {
        ret = rte_eal_remote_launch(pkt_decode, (void *)thread_id, lcore_id);
        process_cnt++;
        thread_id++;
        printf("create dissect func ret = %d\n", ret);
      }
    }

    //master线程，用来更新全局时间戳，处理消息
    socket_main(NULL);

    DPI_LOG(DPI_LOG_DEBUG, "Bye...");

    return ret;
}



#ifdef ENABLE_SMART_NIC
#include "dpi_smart_nic.h"
static int work_smart_nic_mode(int argc, char **argv)
{
#if RTE_VER_YEAR != 19
    fprintf(stderr, "使用 smart_nic 需要 dpdk 19, 当前版本为: %s\n", rte_version());
    exit(EXIT_FAILURE);
#endif // RTE_VER_YEAR

    int ret;
    int nb_ports, port_id;
    unsigned int lcore, queue_id;
    int nic_num = 0;

    init_dpdk_eal(argc, argv);
    dpi_metrics_init();
    // 各模块初始化, 包括mbuf内存池
    init_all_modules();
    init_black_white_list();

    // 初始化网卡, 获得网卡个数
    if (dpi_smart_nic_init(&nic_num, g_config.nb_rxq) || nic_num <= 0) {
        rte_exit(EXIT_FAILURE,
                "Error: can not get smart nic.\n");
    }

    // 检测是否有足够的cpu core
    if ( (1 + g_config.dissector_thread_num + nic_num * g_config.nb_rxq) < rte_lcore_count()) {
        rte_exit(EXIT_FAILURE,
                "No enough lcore, yaDpiSdt at least need %d lcore.\n",
                1 + g_config.dissector_thread_num + nic_num * g_config.nb_rxq);
    }

    init_sdt_module();

    lcore = rte_get_next_lcore(-1, 1, 0);

    // 启动解析线程
    for (uint64_t i = 0; i < g_config.dissector_thread_num; i++)
    {
        if (rte_eal_remote_launch(pkt_decode, (void *)i, lcore) < 0) {
            rte_exit(EXIT_FAILURE, "Could not launch dissect process on lcore %u.\n", lcore);
        }

        printf("Create dissect process on lcore %u\n", lcore);
        lcore = rte_get_next_lcore(lcore, 1, 0);
    }

    // 启动收包
    if (dpi_smart_nic_start(&lcore, g_config.nb_rxq) != 0)
    {
        return -1;
    }

    //注册信号
    g_config.master_threadid = pthread_self();
    signal(SIGINT,   sig_to_master);
    signal(SIGTERM,  sig_to_master);
    signal(SIGUSR1,  sig_to_master);

    socket_main(NULL);

    dpi_smart_nic_stop();

    return 0;
}
#else   // ifndef ENABLE_SMART_NIC
static int work_smart_nic_mode(int argc _U_, char **argv _U_)
{
    fprintf(stderr, "编译选项 ENABLE_SMART_NIC 未开启, 无法使用智能网卡收包模式\n");
    return 0;
}
#endif  // ENABLE_SMART_NIC


static void get_task_name(const char *src)
{
   char *ret, *begin = NULL, *end = NULL;
   if( (ret = strstr(src, "dir=")) ){
       while( (ret = strstr(ret+1, "/")) )
           begin = ret + 1;
       if(begin)
           end = strstr(begin, ",");
       if(end){
           g_config.task_name_len = DPI_MIN(end - begin, 64);
           memcpy(g_config.task_name, begin, g_config.task_name_len);
       }
   }
   return;
}

int entry(int argc, char **argv)
{
    update_global_time();
    g_config.g_now_time = time(NULL);

    char path_cfg[256];
    char *path = get_owner_path();
    if (path == NULL)
        return -1;

    global_initializer();

    snprintf(path_cfg, sizeof(path_cfg), "%s/config.ini", path);
    parser_init_config(path,path_cfg);


    dpi_log_set_level_str(g_config.log_output_level_str);

    dpi_sdx_init_config(g_config.ini, &g_config.sdx_config);
    dpi_web_init_config(&g_config.web_config);

    forward_params_parse();

    if(g_config.task_name_switch && argc > 2)
    {
        get_task_name(argv[1]);
    }

    /* 解析命令行参数中的 app args 部分("--" 之后的参数)
     * 命令行参数配置可以覆盖配置文件中的参数配置，如 work_mode 参数;
     */
    int lSts = parse_command_line_app_args(argc, argv);
    if (lSts < 0)
    {
        printf("error: 命令行参数解析出错\n");
        return -1;
    }

    signal(SIGPIPE, SIG_IGN);
    //dict_global=sdtAppProtoDict_Init();

    if (strcasecmp(g_config.work_mode, "dpdk") == 0){
        printf("RUNNING MODE DPDK\n");
        g_config.work_mode_flag=WMF_dpdk;
        work_dpdk_mode(argc, argv);
    } else if (strncasecmp(g_config.work_mode, "scan", 4) == 0){
        printf("RUNNING MODE SCAN\n");
        g_config.work_mode_flag = WMF_scan;
        work_scan_mode(argc, argv);
    } else if (strcasecmp(g_config.work_mode, "smart_nic") == 0) {
        printf("RUNNING MODE SMART_NIC\n");
        g_config.work_mode_flag = WMF_smart_nic;
        work_smart_nic_mode(argc, argv);
    } else {
        printf("error: 未配置 work_mode\n");
    }

    global_destroyer();

    return 0;
}

static __attribute((constructor)) void     before_init_config(void)
{
    const char *str;
    char path_cfg[256];
    char *path = get_owner_path();
    if (path == NULL)
        exit(-1);

    snprintf(path_cfg, sizeof(path_cfg), "%s/config.ini", path);

    g_config.ini = iniparser_load(path_cfg);
    if (g_config.ini == NULL)
    {
        DPI_LOG(DPI_LOG_ERROR, "cannot parse %s file", path);
        exit(-1);
    }
    g_config.sdx_config.sdx_mac_packet_header_flag = iniparser_getint(g_config.ini, ":SDX_MAC_PACKET_HEADER_FLAG",0);

    str = iniparser_getstring(g_config.ini, ":SDX_STAT_PROGRAM_CASE_NAME", "yaDpiSdt_01");
    if(str){
        snprintf(g_config.sdx_config.sdx_stat_case_name, 64, "%s", str);
    }

    str = iniparser_getstring(g_config.ini, ":SDT_FIELDS_MAP_DIR", "./fields_json");
    if(str){
        snprintf(g_config.dpi_field_json_dir, sizeof(g_config.dpi_field_json_dir), "%s", str);
        snprintf(g_config.sdt_rules_field, sizeof(g_config.sdt_rules_field), "%s", str);
    }
}
