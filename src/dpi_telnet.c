/****************************************************************************************
 * 文 件 名 : dpi_telnet.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/31
编码: wangy            2018/07/31
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <jhash.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_sdp.h"
#include "dpi_dissector.h"
#include "dpi_conversation.h"
#include "utils/dpi_utils.h"

#define TN_IAC   255
#define TN_DONT  254
#define TN_DO    253
#define TN_WONT  252
#define TN_WILL  251
#define TN_SB    250
#define TN_GA    249
#define TN_EL    248
#define TN_EC    247
#define TN_AYT   246
#define TN_AO    245
#define TN_IP    244
#define TN_BRK   243
#define TN_DM    242
#define TN_NOP   241
#define TN_SE    240
#define TN_EOR   239
#define TN_ABORT 238
#define TN_SUSP  237
#define TN_EOF   236
#define TN_ARE     1


extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern struct rte_mempool *tbl_log_content_mempool_256k;



static const char *options[] = {
    "Binary Transmission",                      /* RFC 856 */
    "Echo",                                     /* RFC 857 */
    "Reconnection",                             /* DOD Protocol Handbook */
    "Suppress Go Ahead",                        /* RFC 858 */
    "Approx Message Size Negotiation",          /* Ethernet spec(!) */
    "Status",                                   /* RFC 859 */
    "Timing Mark",                              /* RFC 860 */
    "Remote Controlled Trans and Echo",         /* RFC 726 */
    "Output Line Width",                        /* DOD Protocol Handbook */
    "Output Page Size",                         /* DOD Protocol Handbook */
    "Output Carriage-Return Disposition",       /* RFC 652 */
    "Output Horizontal Tab Stops",              /* RFC 653 */
    "Output Horizontal Tab Disposition",        /* RFC 654 */
    "Output Formfeed Disposition",              /* RFC 655 */
    "Output Vertical Tabstops",                 /* RFC 656 */
    "Output Vertical Tab Disposition",          /* RFC 657 */
    "Output Linefeed Disposition",              /* RFC 658 */
    "Extended ASCII",                           /* RFC 698 */
    "Logout",                                   /* RFC 727 */
    "Byte Macro",                               /* RFC 735 */
    "Data Entry Terminal",                      /* RFC 732, RFC 1043 */
    "SUPDUP",                                   /* RFC 734, RFC 736 */
    "SUPDUP Output",                            /* RFC 749 */
    "Send Location",                            /* RFC 779 */
    "Terminal Type",                            /* RFC 1091 */
    "End of Record",                            /* RFC 885 */
    "TACACS User Identification",               /* RFC 927 */
    "Output Marking",                           /* RFC 933 */
    "Terminal Location Number",                 /* RFC 946 */
    "Telnet 3270 Regime",                       /* RFC 1041 */
    "X.3 PAD",                                  /* RFC 1053 */
    "Negotiate About Window Size",              /* RFC 1073, DW183 */
    "Terminal Speed",                           /* RFC 1079 */
    "Remote Flow Control",                      /* RFC 1372 */
    "Linemode",                                 /* RFC 1184 */
    "X Display Location",                       /* RFC 1096 */
    "Environment Option",                       /* RFC 1408, RFC 1571 */
    "Authentication Option",                    /* RFC 2941 */
    "Encryption Option",                        /* RFC 2946 */
    "New Environment Option",                   /* RFC 1572 */
    "TN3270E",                                  /* RFC 1647 */
    "XAUTH",                                    /* XAUTH  */
    "CHARSET",                                  /* CHARSET  */
    "Remote Serial Port",                       /* Remote Serial Port */
    "COM Port Control",                         /* RFC 2217 */
    "Suppress Local Echo",                      /* draft-rfced-exp-atmar-00 */
    "Start TLS",                                /* draft-ietf-tn3270e-telnet-tls-06 */
    "KERMIT",                                   /* RFC 2840 */
    "SEND-URL",                                 /* draft-croft-telnet-url-trans-00 */
    "FORWARD_X",                                /* draft-altman-telnet-fwdx-03 */
};

#define NOPTIONS array_length(options)
#define TELNET_LOGIN_NUMBER   array_length(telnet_login)
#define TELNET_LOGIN_SUCC_NUMBER   array_length(telnet_login_succ)

static const char *telnet_login_succ[]=
{
"Last login"
};

static const char *telnet_login[]=
{
    "Username:",
    "login:",
    "Login:",
    "username:",
    "user:",
};

static const char *telnet_passwd[]=
{
    "Password:",
    "password:",
    "passwd:",
    "pass:",
};
#define TELNET_PASSWD_NUMBER   array_length(telnet_passwd)

static const char *telnet_status[]=
{
    "Error:",
//    "Failed",
//    "failed",
    "incorrect",
//    "fail",
    "incrrect",
    "timeout",
    "Bad password",
    "User not exist",
    "Login invalid",
    "Please retry after",
    "Access denied",

};

#define TELNET_LOGIN_STATUS_NUMBER   array_length(telnet_status)

/* conversation for telnet add by liugh*/
enum TELNET_STATUS{
  TELNET_STATUS_CMD,
  TELNET_STATUS_DATA
};

#define TELNET_LOGIN_STATUS_NUMBER   array_length(telnet_status)
#define  TELNET_CACHE_MAX 3
struct telnet_cache
{
    char     *addr;
    int       size;
    int       hold;
};

struct telnet_session
{
    enum  TELNET_STATUS status;
    struct telnet_cache cache;
    char     filepath[COMMON_FILE_PATH];
    char     negotiations[64];
    char     terminal_type[COMMON_NAME_PASSWD_LEN];
    char     login_status[COMMON_NAME_PASSWD_LEN];

    //服务端回显用户名标识
    uint8_t  echo_direction;   //上一次回显的方向
    char     telnet_echo_space;   //用于存储服务端回显的字符
};

struct telnet_info
{
    const uint8_t *telnet_data;
    uint32_t telnet_data_len;

    uint8_t nego_flag;
    char negotiations[64];
    char file_path[COMMON_FILE_PATH];
    char username[COMMON_NAME_PASSWD_LEN];
    char password[COMMON_NAME_PASSWD_LEN];
    char terminal_type[COMMON_NAME_PASSWD_LEN];
    uint8_t login_flag;
    uint8_t write_flag;
    char    login_status[COMMON_NAME_PASSWD_LEN];
};

enum telnet_index_em{

    EM_TELNET_TELNETDATA,
    EM_TELNET_NEGOTIATIONS_C,
    EM_TELNET_NEGOTIATIONS_S,
    EM_TELNET_FILEPATH,
    EM_TELNET_TERMINALTYPE,
    EM_TELNET_USERNAME,
    EM_TELNET_PASSWD,
    EM_TELNET_LOGIN_STATUS,
    EM_TELNET_BANNER,
    EM_TELNET_LOCAL_FILEPATH,
    EM_TELNET_MAX
};


static dpi_field_table  telnet_field_array[] = {

    DPI_FIELD_D(EM_TELNET_TELNETDATA,                       YA_FT_STRING,       "TelnetData"),
    DPI_FIELD_D(EM_TELNET_NEGOTIATIONS_C,                   YA_FT_STRING,       "Negotiations_c"),
    DPI_FIELD_D(EM_TELNET_NEGOTIATIONS_S,                   YA_FT_STRING,       "Negotiations_s"),
    DPI_FIELD_D(EM_TELNET_FILEPATH,                         YA_FT_STRING,       "FilePath"),
    DPI_FIELD_D(EM_TELNET_TERMINALTYPE,                     YA_FT_STRING,       "Terminaltype"),
    DPI_FIELD_D(EM_TELNET_USERNAME,                         YA_FT_STRING,       "Username"),
    DPI_FIELD_D(EM_TELNET_PASSWD,                           YA_FT_STRING,       "Passwd"),
    DPI_FIELD_D(EM_TELNET_LOGIN_STATUS,                     YA_FT_STRING,       "LoginStatus"),
    DPI_FIELD_D(EM_TELNET_LOCAL_FILEPATH,                     YA_FT_STRING,       "localFilepath"),
    DPI_FIELD_D(EM_TELNET_BANNER,                           YA_FT_STRING,       "Banner"),

};


static int get_copy_data(char *dst, const char  *raw, const char *sub, int len)
{
    if(dst==NULL || raw==NULL || sub==NULL || len<=0){
        return 0;
    }
    const char *p=NULL;
    if(len>=COMMON_NAME_PASSWD_LEN){
        len=COMMON_NAME_PASSWD_LEN;
    }
    p=(const char *)memstr((const uint8_t *)raw, sub, len);
    if(p!=NULL){
        int data_len=p-raw;
        strncpy(dst, raw, data_len);
        return data_len;
    }

    return 0;
}


static int search_login_index(const char *data, uint32_t data_len, int *key_len)
{
    uint32_t i=0;
    char *p=NULL;
    for(i=0;i<TELNET_LOGIN_NUMBER;i++){
        p=memmem(data,data_len,telnet_login[i],strlen(telnet_login[i]));
        if(p!=NULL){
            *key_len=strlen(telnet_login[i]);
            return (p-data);
        }
    }

    return -1;
}

static int search_passwd_index(const char *data, uint32_t data_len,int *key_len)
{
    uint32_t i=0;
    char *p=NULL;
    for(i=0;i<TELNET_PASSWD_NUMBER;i++){
        p=memmem(data,data_len,telnet_passwd[i],strlen(telnet_passwd[i]));
        if(p!=NULL){
            *key_len=strlen(telnet_passwd[i]);
            return (p-data);
        }
    }

    return -1;
}

static int search_login_status_index(const char *data, uint32_t data_len,int *key_len)
{
    uint32_t i=0;
    char *p=NULL;
    for(i=0;i<TELNET_LOGIN_STATUS_NUMBER;i++){
        p=memmem(data,data_len,telnet_status[i],strlen(telnet_status[i]));
        if(p!=NULL){
            *key_len=strlen(telnet_status[i]);
            return (p-data);
        }
    }

    return -1;
}



static int
parse_telnet_target(char *data, int length, struct telnet_info *info,
                    int (*search_func)(const char *, uint32_t, int *), char *dst_buff)
{
    int key_idx = 0;
    int key_len = 0;
    int offset = 0;

    if (data == NULL || length <= 0)
        return -1;

    key_idx = search_func(data, length, &key_len);

    if (key_idx < 0)
        return -1;

    offset = key_idx + key_len;

    if (offset >= length)
        return -1;

    if (-1 == parse_telnet_target(data + offset, length - offset, info, search_func, dst_buff))
    {
        // 返回 -1 表示后面没有了, 当前就是最后一个
        get_copy_data(dst_buff, data+offset, "\n", length - offset);
    }

    return 0;
}

static void
parse_telnet_username(char *data, int length, struct telnet_info *info)
{
    parse_telnet_target(data, length, info, search_login_index, info->username);
}

static void
parse_telnet_password(char *data, int length, struct telnet_info *info)
{
    parse_telnet_target(data, length, info, search_passwd_index, info->password);
}



static int
parse_telnet_login(char *data, int length, struct telnet_info *info)
{
    if(data==NULL || length <=0){
        return 0;
    }

    int passwd_i=0, passwd_key_len=0;
    int status_i=0, status_key_len=0;
    int offset=0;
    int left_len=length;


    /*  解决: bug 2283
            在重组后的data中, 出现多个登录命令时, 提取最后一个登录用户名
        例如: login: TTeellnneettuusseerr
            login: Telnetuser
            password: 12345678
    */
    passwd_i = search_passwd_index(data + offset, left_len, &passwd_key_len);

    if (passwd_i > 0)
    {
        parse_telnet_username(data + offset, passwd_i, info);
    }
    else if (passwd_i < 0)
    {
        parse_telnet_username(data + offset, left_len, info);
        return 0;
    }

    offset += passwd_i;         // 不要加 passwd_key_len;
    if (offset >= length) {
        return 0;
    }

    left_len = length - offset;
    status_i = search_login_status_index(data + offset, left_len, &status_key_len);

    if (status_i > 0)
    {
        parse_telnet_password(data + offset, status_i, info);
    }
    else if (status_i < 0)      // 没有出现登录异常的信息
    {
        parse_telnet_password(data + offset, left_len, info);
        if (strlen(info->username) > 0 || strlen(info->password) > 0)
            info->login_flag = 1;
        return 0;
    }

    // bzero(info->username,sizeof(info->username));
    // bzero(info->password,sizeof(info->password));
    offset += status_i + status_key_len;
    if (offset >= length) {
        return 0;
    }

    return parse_telnet_login(data + offset, length - offset, info);
}

static int check_special_byte(char *data, char byte)
{
    if(data==NULL){
        return 0;
    }
    int len=strlen(data);
    int i;
    for(i=0;i<len;i++){
        if(data[i]==byte){
            return 1;
        }
    }

    return 0;
}

static int check_username_passwd(char *data)
{
    if(data==NULL){
        return 0;
    }

    char *p=NULL;
    char *q=NULL;
    char *m=NULL;
    p=strcasestr(data,"fail");
    q=strcasestr(data,"incrrect");
    m=strcasestr(data,"timeout");
    if(p!=NULL || q!=NULL || m!=NULL){
        return 0;
    }

    int i;
    int first_flag=0;
    char *temp=data;
    for(i=0;i<(int)strlen(data);i++){
        if(!isdigit(data[i]) && !isalpha(data[i])){
            return 0;
        }
    }

    return 1;
}


static char *strim(char *str)
{
    char *end,*sp,*ep;
    int len=0;
    sp = str;
    end = str + strlen(str) -1;
    ep = end;

    while(sp<=end && isspace(*sp))
        sp++;
    while(ep>=sp && isspace(*ep))
        ep--;

    len = (ep < sp) ? 0:(ep-sp)+1;
    sp[len] = '\0';
    snprintf(str, strlen(str), "%s", sp);

    return sp;
}


static int find_unescaped_iac(const uint8_t *payload, const uint32_t payload_len)
{
    int iac_offset = -1;
    uint32_t offset  = 0;

    if(payload_len <= 1)
        return iac_offset;

    /* If we find an IAC (0XFF), make sure it is not followed by another 0XFF.
    Such cases indicate that it is not an IAC at all */
    while (offset < payload_len)
    {
        if ((get_uint8_t(payload, offset)) == TN_IAC && (get_uint8_t(payload, offset + 1) != TN_IAC)) {
            iac_offset = offset;
            break;
        }

        offset++;
    }
    return iac_offset;
}

static uint8_t search_iac(const uint8_t *payload, const uint16_t payload_len)
{
    uint16_t a;

    if (payload_len < 3) {
        return 0;
    }

    if (!(payload[0] == 0xff && payload[1] > 0xf9 && payload[1] != 0xff && payload[2] < 0x28)) {
        return 0;
    }

    a = 3;

    while (a < payload_len - 2) {
        // commands start with a 0xff byte followed by a command byte >= 0xf0 and < 0xff
        // command bytes 0xfb to 0xfe are followed by an option byte <= 0x28
        if (!(payload[a] != 0xff
                || (payload[a] == 0xff && (payload[a + 1] >= 0xf0) && (payload[a + 1] <= 0xfa))
                || (payload[a] == 0xff && (payload[a + 1] >= 0xfb) && (payload[a + 1] != 0xff) && (payload[a + 2] <= 0x28)))) {
            return 0;
        }
        a++;
    }

    return 1;
}
static int identify_telnet(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_TELNET] == 0)
        return PROTOCOL_UNKNOWN;
    uint16_t s_port = 0, d_port = 0;
    s_port = ntohs(flow->tuple.inner.port_src);
    d_port = ntohs(flow->tuple.inner.port_dst);

    if(s_port!=23 && d_port!=23
        && s_port!= 2323 &&  d_port!=2323){
        return PROTOCOL_UNKNOWN;
    }

    if (search_iac(payload, payload_len) == 1) {
        if(flow->packet_stage==2){
            flow->real_protocol_id = PROTOCOL_TELNET;
            return PROTOCOL_TELNET;
        }
        flow->packet_stage++;
    }

    return PROTOCOL_UNKNOWN;
}

static void
telnet_suboption_name(char *optname, int max_len, const uint8_t *payload, uint32_t *offset, const char *type)
{
    uint8_t opt_byte;
    const char *opt;

    opt_byte = get_uint8_t(payload, *offset);
    if (opt_byte >= NOPTIONS) {
        opt = "<unknown option>";
    }
    else {
        opt = options[opt_byte];
    }

    (*offset)++;
    snprintf(optname, max_len, "%s %s", type, opt);
}

static void
telnet_suboption_stringvalue(const uint8_t *payload,const uint32_t payload_len,
                                     uint32_t *offset, struct telnet_info *info)
{
    uint8_t opt_byte;
    const char *opt;
    uint8_t  cmd;
    int len=0,i;

    opt_byte = get_uint8_t(payload, *offset);

    if(opt_byte==24){  // terminal type
        (*offset)++;
        cmd=get_uint8_t(payload, *offset);
        (*offset)++;
        if(cmd==0){
            for(i=0;i<(int)(payload_len-*offset);i++){
                if(payload[*offset+i]==0xff){
                    break;
                }
                len++;
            }
            if(len>=COMMON_SOME_TYPE){
                len=COMMON_SOME_TYPE;
            }
            strncpy((char *)info->terminal_type,(const char *)&payload[*offset],len);
        }
    }

    return;
}



static uint32_t telnet_command(const uint8_t *payload, const uint32_t payload_len, uint32_t start_offset, struct telnet_info *info)
{
    uint32_t offset = start_offset;
    uint8_t optcode;

    if (offset + 2 > payload_len) {
        return offset;
    }

    offset += 1;  /* skip IAC */
    optcode = get_uint8_t(payload, offset);

    offset++;

    switch(optcode) {
        case TN_WILL:
            telnet_suboption_name(info->negotiations, 64, payload, &offset, "Will");
            break;

        case TN_WONT:
            telnet_suboption_name(info->negotiations, 64, payload, &offset, "Won't");
            break;

        case TN_DO:
            telnet_suboption_name(info->negotiations, 64, payload, &offset, "Do");
            break;

        case TN_DONT:
            telnet_suboption_name(info->negotiations, 64, payload, &offset, "Don't");
            break;

        case TN_SB:
            //telnet_suboption_name(info->negotiations, 64, payload, &offset, "Suboption");
            telnet_suboption_stringvalue(payload, payload_len, &offset, info);
            break;

        default:
            info->nego_flag=1;
            snprintf(info->negotiations, sizeof(info->negotiations), "%s", "<unknown option>");
            break;
    }


    if (optcode == TN_SB) {
        //todo
        //offset = telnet_sub_option(pinfo, subopt_tree, subopt_item, tvb, start_offset);
    }

  return offset;
}



static int telnet_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, struct telnet_info *info, int *idx, int i)
{
    switch(i){

    case EM_TELNET_TELNETDATA:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, info->telnet_data, info->telnet_data_len);
        break;
    case EM_TELNET_NEGOTIATIONS_C:
        if (direction == FLOW_DIR_SRC2DST) {
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, (const uint8_t *)info->negotiations,
                    strlen(info->negotiations));
        } else {
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }
        break;
    case EM_TELNET_NEGOTIATIONS_S:
        if (direction == FLOW_DIR_DST2SRC) {
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, (const uint8_t *)info->negotiations,
                    strlen(info->negotiations));
        } else {
                write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        }
        break;
    case EM_TELNET_FILEPATH:
        // if(strlen(info->file_path)>0){
        //     char filename[128]={0};
        //     if(get_filename(info->file_path, filename)){
        //             write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING,
        //                 (const uint8_t *)info->file_path + strlen(g_config.tbl_out_dir) + 1,
        //                 strlen(info->file_path + strlen(g_config.tbl_out_dir) + 1));
        //             break;
        //     }
        // }
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING,
            (const uint8_t *)info->file_path + strlen(g_config.tbl_out_dir) + 1,
            strlen(info->file_path + strlen(g_config.tbl_out_dir) + 1));
        break;
    case EM_TELNET_LOCAL_FILEPATH:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, (const uint8_t *)info->file_path, strlen(info->file_path));
        break;
    case EM_TELNET_TERMINALTYPE:
        if(strlen(info->terminal_type)>0)
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, (const uint8_t *)info->terminal_type, strlen(info->terminal_type));
        else
            write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    case EM_TELNET_USERNAME:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, (const uint8_t *)info->username, strlen(info->username));
        break;
    case EM_TELNET_PASSWD:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, (const uint8_t *)info->password, strlen(info->password));
        break;
    case EM_TELNET_LOGIN_STATUS:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_STRING, (const uint8_t *)info->login_status, strlen(info->login_status));
        break;
    default:
        write_coupler_log(log_ptr->record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    }

    return 0;
}


static int write_telnet_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
//    char __str[64] = {0};
    int idx = 0;
    struct tbl_log *log_ptr;

    struct telnet_info *info=(struct telnet_info *)field_info;
    if(!info){
        return 0;
    }
    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory");
        return 0;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "telnet");

    int i;

    #if 0
    int *telnet_reflect_array=map_fields_get_array(PROTOCOL_TELNET);
    for(i=0;i<map_fields_get_num(PROTOCOL_TELNET);i++){
        telnet_field_element(log_ptr,flow, direction, info, &idx, telnet_reflect_array[i]);
    }
    #else
    for(i=0;i<EM_TELNET_MAX;i++){
        telnet_field_element(log_ptr,flow, direction, info, &idx, i);
    }
    #endif

    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_TELNET;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    log_ptr->flow        = flow;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }
    return 1;
}


static int send_to_sdt(ProtoRecord *pRec)
{
    int i;
    if(pRec==NULL){
        return -1;
    }
    return 0;
}

/*
 * convert 0x00 char into spaces (0x20) so we can
 * use str*() functions on the buffer...
 */
static void telnet_convert_zeros(unsigned char *ptr, uint16_t len)
{
    uint8_t flag;
    int i;
    /*
     * walk the entire buffer, but skip the last
     * char, if it is 0x00 it is actually the string
     * terminator
     */
    for(i=0;i<len;i++){
        if(ptr[i]==0x00){
            if(flag && ptr[i-1]=='\r'){
                ptr[i]='\n';
            }else{
                ptr[i]=' ';
            }
        }
        flag=1;
    }
}
int parse_telnet_password_v2(struct flow_info *flow, int direction, char *data, int length, struct telnet_info *info){
  //按行解析
  char *line_end = data;
  char *line_start = data;
  char *end_of_header = data + length;
  int remain_len = length;
  int flag = 0;//0 未找到用户名 1 已找到用户名 2 已找到密码 3 已找到状态
  uint8_t end_flag = 0;
  while (line_start < end_of_header)
  {
      line_end = (char*)dpi_hybrid_strnstr(line_start, end_of_header - line_end, "\r\n");
      if (line_end == NULL)//结束可能没有换行
      {
        //处理最后一行
        end_flag = 1;
        line_end = end_of_header;
      }
      int line_len = line_end - line_start;
      switch (flag) {
        case 0:
          for (size_t i = 0; i < TELNET_LOGIN_NUMBER ; i++)
          {
            char* p = (char*)dpi_hybrid_strncasestr(line_start,line_len, telnet_login[i]);
            if (p != NULL && p < line_end)
            {
              int len = strlen(telnet_login[i]);
              bzero(info->username, sizeof(info->username));
              int ret = get_copy_data(info->username, p + len, "\r", line_end - p - len + 2);
              if (ret >= 0)
              {
                strim(info->username);
                flag = 1;
              }
              break;
            }
          }
        break;
        case 1:
          for (size_t i = 0; i < TELNET_PASSWD_NUMBER ; i++)
          {
            char* p = (char*)dpi_hybrid_strncasestr(line_start,line_len, telnet_passwd[i]);
            if (p != NULL && p < line_end)
            {
              int len = strlen(telnet_passwd[i]);
              bzero(info->password, sizeof(info->password));
              int ret = get_copy_data(info->password, p + len, "\r", line_end - p - len + 2);
              if (ret >= 0)
              {
                strim(info->password);
                flag = 2;
              }
              break;
            }
          }
        break;
        case 2:
          for (size_t i = 0; i < TELNET_LOGIN_STATUS_NUMBER ; i++)
          {
            char* p =  (char*)dpi_hybrid_strncasestr(line_start,line_len, telnet_status[i]);
            if (p != NULL && p < line_end)
            {
              int len = strlen(telnet_status[i]);
              int ret = get_copy_data(info->login_status, p + len, "\r", line_end - p - len + 2);
              if (ret >= 0)
              {
                info->login_flag = 0;
                flag = 3;
              }
              break;
            }
          }
          for(size_t i = 0; i < TELNET_LOGIN_SUCC_NUMBER; i++)
          {
            char* p =  (char*)dpi_hybrid_strncasestr(line_start,line_len, telnet_login_succ[i]);
            if (p != NULL && p < line_end)
            {
              int len = strlen(telnet_status[i]);
              int ret = get_copy_data(info->login_status, p + len, "\r", line_end - p - len + 2);
              if (ret >= 0)
              {
                info->login_flag = 1;
                flag = 3;
              }
              break;
            }
          }
        break;
        case 3:
          // if (strlen(info->username) > 0 || strlen(info->password) > 0)
          // {
          //     telnet_record_password(flow, info);
          // }
          flag = 0;
        break;
      }
      if (end_flag)
      {
        break;
      }
      line_start = line_end + 2;
  }
  //如果没有登录状态，但是用户名和密码都有，也要输出
  if (flag == 2)
  {
    // if (strlen(info->username) > 0 || strlen(info->password) > 0)
    // {
    //     telnet_record_password(flow, info);
    // }
  }
  return 0;
}

static int dissect_telnet(struct flow_info *flow, uint8_t direction, const uint8_t *payload, uint32_t payload_len) {
    /* add by liugh */
    struct telnet_session *session;
    if (flow->app_session == NULL) {
        flow->app_session = dpi_malloc(sizeof(struct telnet_session));
        if (flow->app_session == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "telnet session malloc failed");
            return PKT_OK;
        }
        memset(flow->app_session, 0, sizeof(struct telnet_session));
        session = (struct telnet_session *)flow->app_session;
        session->echo_direction = direction;
    }
    session = (struct telnet_session *)flow->app_session;

    uint32_t offset = 0;
    int iac_offset;
    struct telnet_info info;
    memset(&info, 0, sizeof(info));
    //判断是否为telnet关键字
    while (offset < payload_len) {
        iac_offset = find_unescaped_iac(payload + offset, payload_len - offset);
        if (iac_offset < 0) {
            // 没有找到IAC
            payload += offset;
            payload_len -= offset;
            session->status = TELNET_STATUS_DATA;
            break;
        } else {  // find an IAC (0XFF)
            offset += telnet_command(payload + offset, payload_len - offset, (uint32_t)iac_offset, &info);
            if (strlen(info.terminal_type) > 0) {
                strncpy(session->terminal_type, info.terminal_type, strlen(info.terminal_type));
            }
            if (info.nego_flag != 1) {
                strncpy(session->negotiations, info.negotiations, strlen(info.negotiations));
            }
            session->status = TELNET_STATUS_CMD;
        }
    }
    //解析账号密码状态机
    switch (session->status) {
        case TELNET_STATUS_DATA: {
          //文本只需要处理回显
          if(session->telnet_echo_space == payload[payload_len - 1] && session->echo_direction != direction){
            goto TELNET_DONE;
          }
        }
        break;
      case TELNET_STATUS_CMD:
      //如果是cmd状态，不缓存
          goto TELNET_DONE;
    }


    struct telnet_cache *c = &session->cache;
    if(NULL == c->addr)
    {
        c->size = 1024*200; //200K
        c->hold = 0;
        c->addr = dpi_malloc(c->size);
    }
    if(c->addr &&session->status != TELNET_STATUS_CMD)
    {
        if((int)payload_len >= (c->size - c->hold))
        {
          // 缓存撑爆前重新申请内存
            // 创建临时缓冲区
            char *new_cache =(char *) realloc(c->addr, c->size + payload_len + 1000);
            if (NULL == new_cache) {
              goto TELNET_DONE;
            }
            c->addr = new_cache;
            c->size += payload_len + 1000;
        }
        //正常 拼装
        for(unsigned int i = 0 ; i < payload_len; i++ ){
          if(dpi_is_utf8((const char*)payload+i,1)){
            c->addr[c->hold + i] = payload[i];
          }else{
            c->addr[c->hold + i] = ' ';
          }
        }
        c->hold  += payload_len;
    }
TELNET_DONE:
    //保存当前的缓存区最后一个字符
    if (session->status != TELNET_STATUS_CMD) {
        if (payload_len > 0) {
            session->telnet_echo_space = payload[payload_len - 1];
            session->echo_direction = direction;
        }
    }

    return 0;
}

static int dissect_telnet_miss(struct flow_info *flow, uint8_t direction, uint32_t miss_len) {
    struct telnet_session *session;
      if (flow->app_session == NULL)  {
    return 0;
    }
    session = (struct telnet_session *)flow->app_session;
    session->status = TELNET_STATUS_CMD;
    return 0;
}
static void timeout_telnet(struct flow_info *flow) {
     struct telnet_session *session = (struct telnet_session *)flow->app_session;
    if(!session)
      return ;
    char *payload = NULL;
    int   payload_len = 0;
    struct telnet_info info;
    memset(&info, 0, sizeof(info));

    struct telnet_cache *c = &session->cache;
    if(c->addr)
    {
        payload     = c->addr;
        payload_len = c->hold;
    }



    flow->match_data_len = payload_len;

    /* 提取用户名密码*/
      parse_telnet_password_v2(flow, 0, (char *)payload, (int)payload_len, &info);
    // if (dpi_is_utf8((const char*)info.username, strlen(info.username)) == 0 ||
    //         check_special_byte(info.username,'/')==1){
    //     memset(info.username, 0, sizeof(info.username));
    // }

    // if (dpi_is_utf8((const char*)info.password, strlen(info.password)) == 0||
    //         check_special_byte(info.password,'/')==1 ){
    //     memset(info.password, 0, sizeof(info.password));
    // }

    // if (strlen(info.username)>0) {
    //     strim(info.username);
    //     if(!check_username_passwd(info.username))
    //     {
    //         memset(info.username, 0, sizeof(info.username));
    //     }
    // }

    // if(strlen(info.password)>0){
    //     strim(info.password);
    //     if(!check_username_passwd(info.password))
    //     {
    //         memset(info.username, 0, sizeof(info.username));
    //         memset(info.password, 0, sizeof(info.password));
    //     }
    // }

    if(g_config.telnet_content){
        memset(session->filepath, 0, sizeof(session->filepath));
        get_special_filename(NULL, "telnet", "bin", session->filepath, sizeof(session->filepath), 1);

        FILE *fp=NULL;
        fp=fopen(session->filepath, "a");
        if(fp){
            fwrite(payload,payload_len, 1, fp);
            fclose(fp);
            fp=NULL;
            strncpy(info.file_path,session->filepath,strlen(session->filepath));
        }
    }

    strncpy(info.negotiations, session->negotiations, strlen(session->negotiations));
    strncpy(info.terminal_type, session->terminal_type, strlen(session->terminal_type));
    if (1 == info.login_flag) {
        strncpy(info.login_status, "YES", COMMON_STATUS_LEN);
    } else {
        strncpy(info.login_status, "NO", COMMON_STATUS_LEN);
    }
    write_telnet_log(flow, flow->direction, &info, NULL);

    if(NULL != c->addr)
    {
        free(c->addr);
        c->addr = NULL;
        c->hold = 0;
    }
    return ;
}
extern struct decode_t decode_telnet;

static int init_telnet_dissector(struct decode_t *decode)
{
    dpi_register_proto_schema(telnet_field_array,EM_TELNET_MAX,"telnet");

    decode_on_port_tcp(23, &decode_telnet);
    decode_on_port_tcp(2323, &decode_telnet);

    register_tbl_array(TBL_LOG_TELNET, 1, "telnet", NULL);

    map_fields_info_register(telnet_field_array,PROTOCOL_TELNET, EM_TELNET_MAX,"telnet");
	return 0;
}

static int telnet_destroy(struct decode_t *decode) { return 0; }

struct decode_t decode_telnet = {
    .name           =   "telnet",
#ifdef DPI_SDT_ZDY
    .identify_type  =   DPI_IDENTIFY_PORT_CONTENT,
#endif
    .decode_initial =   init_telnet_dissector,
    .pkt_identify   =   identify_telnet,
    .pkt_dissect    =   dissect_telnet,
    .pkt_miss       =   dissect_telnet_miss,
    .flow_finish    =   timeout_telnet,
    .decode_destroy =   telnet_destroy,
};
