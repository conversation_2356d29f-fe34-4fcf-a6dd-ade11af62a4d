/****************************************************************************************
 * 文 件 名 : dpi_conversation.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/08/20
编码: wangy            2018/08/20
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <pcap.h>
#include <string.h>
#include <stdlib.h>
#include <netinet/in.h>
#include <rte_mempool.h>
//#include <linux/if_ether.h>

#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_conversation.h"

#define CONVERSATION_ONCE_TIMEOUT_NUM 10000

struct rte_hash *conversation_hash_exact;
struct rte_hash *conversation_hash_no_addr2;
struct rte_hash *conversation_hash_no_port2;

extern struct global_config g_config;
extern struct rte_ring *thread_conv_ring[RTE_MAX_LCORE];
extern  uint8_t   flow_conv_running;
extern  pthread_t flow_conv_lcores[MAX_FLOW_THREAD_NUM];

#define MAX_FLOW_BURST  512
#define MAX_CONV_THREAD_NUM    32
int session_protocol_st_size[PROTOCOL_MAX]={0};

static struct rte_hash_parameters conversation_hash_params = {
    .key_len = sizeof(struct conversation_tuple),
    .hash_func = rte_jhash,
    .hash_func_init_val = 0,
    .socket_id = 0,
#ifdef _DPI_DPDK_17
    .extra_flag = RTE_HASH_EXTRA_FLAGS_MULTI_WRITER_ADD
#else
    .extra_flag = RTE_HASH_EXTRA_FLAGS_RW_CONCURRENCY
#endif
};
    
struct conversation_value * find_conversation(struct conversation_tuple *tuple, uint8_t options)
{
    struct conversation_value *conversation = NULL;
    struct conversation_tuple tuple_reverse;
    struct conversation_tuple tuple_original;

    if (!g_config.conversation_switch)
        return NULL;

    int pos;
    
    memcpy(&tuple_original, tuple, sizeof(tuple_original));

    tuple_reverse.port_dst = tuple->port_src;
    tuple_reverse.port_src = tuple->port_dst;
    memcpy(&tuple_reverse.ip_dst, &tuple->ip_src, sizeof(tuple_reverse.ip_dst));
    memcpy(&tuple_reverse.ip_src, &tuple->ip_dst, sizeof(tuple_reverse.ip_src));
    tuple_reverse.proto = tuple->proto;

    if (!(options & (NO_ADDR_B|NO_PORT_B))) {
        /*
        * Neither search address B nor search port B are wildcarded,
        * start out with an exact match.
        */
        
        pos = rte_hash_lookup_data(conversation_hash_exact, &tuple_original, (void **)&conversation);
        if (pos < 0) {
            pos = rte_hash_lookup_data(conversation_hash_exact, &tuple_reverse, (void **)&conversation);
        }

        if (pos >= 0 && conversation != NULL)
            return conversation;
    }
    
    if (!(options & NO_ADDR_B)) {
        tuple_original.port_dst = 0;
        pos = rte_hash_lookup_data(conversation_hash_no_port2, &tuple_original, (void **)&conversation);
        if (pos < 0) {
            tuple_reverse.port_dst = 0;
            pos = rte_hash_lookup_data(conversation_hash_no_port2, &tuple_reverse, (void **)&conversation);
        }

        if (pos >= 0 && conversation != NULL)
            return conversation;
    }

    if (!(options & NO_PORT_B)) {
        tuple_original.port_dst = tuple->port_dst;
        memset(&tuple_original.ip_dst, 0, sizeof(tuple_original.ip_dst));
        pos = rte_hash_lookup_data(conversation_hash_no_addr2, &tuple_original, (void **)&conversation);
        if (pos < 0) {
            tuple_reverse.port_dst = tuple->port_src;
            memset(&tuple_reverse.ip_dst, 0, sizeof(tuple_reverse.ip_dst));
            pos = rte_hash_lookup_data(conversation_hash_no_addr2, &tuple_reverse, (void **)&conversation);
        }

        if (pos >= 0 && conversation != NULL)
            return conversation;
    }


    return NULL;
}

struct conversation_value *find_or_create_conversation(struct conversation_tuple *tuple, uint8_t options, uint16_t protocol, void *session)
{
    struct conversation_value *conv=NULL;
    struct rte_hash *hash;

    if (!g_config.conversation_switch)
        return NULL;

    if((conv = find_conversation(tuple, options)) != NULL) {
        conv->createtime = g_config.g_now_time;
        return conv;
    } else {
        if (!(options & (NO_ADDR_B|NO_PORT_B))) {
            hash = conversation_hash_exact;
        } else if (!(options & NO_ADDR_B)) {
            hash = conversation_hash_no_port2;
        } else if (!(options & NO_PORT_B)) {
            hash = conversation_hash_no_addr2;
        } else
            return NULL;

        conv = (struct conversation_value *)dpi_malloc(sizeof(struct conversation_value));
        if (conv == NULL)
            return NULL;
        conv->protocol = protocol;
        conv->createtime = g_config.g_now_time;
        
        //add by liugh
        conv->conv_session = (void*)dpi_malloc(session_protocol_st_size[protocol]);
        if (conv->conv_session == NULL)
            return NULL;
        memcpy(conv->conv_session, session, session_protocol_st_size[protocol]);

        int retval = rte_hash_add_key_data(hash, tuple, conv);
        if (retval < 0) {
            DPI_LOG(DPI_LOG_WARNING, "failed to insert tuple to hash");
            dpi_free(conv->conv_session);
            dpi_free(conv);
            conv=NULL;
        }
    }

    return conv;
}

void write_ftp_control_conversation_log(const struct conversation_tuple * tuple,struct ftp_session *session);

static void _timeout_conversation_hash(struct rte_hash *hash)
{
    const void *next_key;
    void *next_data;
    uint32_t iter = 0;
    int i = 0;
    const struct conversation_tuple *tuple;
    struct conversation_value *conv;
    const struct conversation_tuple *array_timeout[CONVERSATION_ONCE_TIMEOUT_NUM];
    struct conversation_value *array_timeout_v[CONVERSATION_ONCE_TIMEOUT_NUM];

    while (rte_hash_iterate(hash, &next_key, &next_data, &iter) >= 0) {
        tuple = (const struct conversation_tuple *)next_key;
        conv = (struct conversation_value *)next_data;

        if(conv->protocol == PROTOCOL_RDP && conv->conv_session)
            continue;
        if (conv->protocol == PROTOCOL_FTP_DATA){
            if (conv->createtime + CONVERSATION_FTPCONTROL_TIMEOUT < g_config.g_now_time) {
                array_timeout[i] = tuple;
                array_timeout_v[i] = conv;
                i++;
                write_ftp_control_conversation_log(tuple,conv->conv_session);
            }
        } else {
            if (conv->createtime + CONVERSATION_TIMEOUT < g_config.g_now_time) {
                array_timeout[i] = tuple;
                array_timeout_v[i] = conv;
                i++;
            }
        }

        if (i == CONVERSATION_ONCE_TIMEOUT_NUM)
            break;
    }

    while (i > 0) {
        i--;
        if (array_timeout_v[i]){
            if(array_timeout_v[i]->conv_session!=NULL){
                dpi_free(array_timeout_v[i]->conv_session);
                array_timeout_v[i]->conv_session=NULL;
            }
            dpi_free((void *)array_timeout_v[i]);
        }
        int retval = rte_hash_del_key(hash, array_timeout[i]);
        if (retval < 0)
            DPI_LOG(DPI_LOG_WARNING, "failed to delete tuple_reverse to hash");
        array_timeout[i] = NULL;
        array_timeout_v[i] = NULL;
    }

    return;
}

void timeout_conversation_hash(void)
{
    _timeout_conversation_hash(conversation_hash_exact);
    _timeout_conversation_hash(conversation_hash_no_addr2);
    _timeout_conversation_hash(conversation_hash_no_port2);
}

void init_conversation(void)
{
    /* set up config */
    conversation_hash_params.entries = g_config.max_conversation_hash_node;
    conversation_hash_params.name = "conversation_hash_exact";
    conversation_hash_exact = rte_hash_create(&conversation_hash_params);
    if (conversation_hash_exact == NULL) {
        DPI_LOG(DPI_LOG_ERROR, "create conversation_hash_exact failed");
        exit(-1);
    }
    conversation_hash_params.name = "conversation_hash_no_addr2";
    conversation_hash_no_addr2 = rte_hash_create(&conversation_hash_params);
    if (conversation_hash_no_addr2 == NULL) {
        DPI_LOG(DPI_LOG_ERROR, "create conversation_hash_no_addr2 failed");
        exit(-1);
    }
    conversation_hash_params.name = "conversation_hash_no_port2";
    conversation_hash_no_port2 = rte_hash_create(&conversation_hash_params);
    if (conversation_hash_no_port2 == NULL) {
        DPI_LOG(DPI_LOG_ERROR, "create conversation_hash_no_port2 failed");
        exit(-1);
    }

}


/**********************文件io异步线程****************************/


GHashTable *g_conversation_hash[MAX_CONV_THREAD_NUM] = {NULL};
static inline void conversation_hash_free_key(gpointer data) {
    free(data);
    data = NULL;
}

static inline void conversation_hash_free_value(gpointer data) {
    free(data);
    data = NULL;
}


static gboolean dpi_thread_conv_timeout_func(gpointer key, gpointer value, gpointer user_data) {
    thread_conv_info *val = (thread_conv_info *)value;
    if ((g_config.g_now_time - val->last_update_time) >= g_config.dissector_thread_conv_timeout &&
        thread_conv_array[val->real_protocol_id].conv_timeout) {
        thread_conv_array[val->real_protocol_id].conv_timeout(key, val->value);
        return true;
    }
    return false;
}

static void dpi_conv_thread_stop(int ring_id){
    g_config.dissector_thread_conv_timeout  = 0;
    GHashTable *conversation_hash = g_conversation_hash[ring_id] ;

    g_hash_table_foreach_remove(g_conversation_hash[ring_id], (void*)dpi_thread_conv_timeout_func, conversation_hash);

}

static void prcess_thread_timer_cb(struct rte_timer *tim, void *arg) {
    int ring_id = *(int*)arg;
    // pthread_t         thread         = 
    GHashTable *conversation_hash = g_conversation_hash[ring_id] ;
    g_hash_table_foreach_remove(conversation_hash, (GHRFunc)dpi_thread_conv_timeout_func, conversation_hash);
    return;
}

static int dpi_thread_timer_reset(struct rte_timer *timer, rte_timer_cb_t fct, void *arg) {
    unsigned lcore_id = rte_lcore_id();
    uint64_t hz = rte_get_timer_hz() / 1000;  // 获取系统时钟频率
    uint64_t tick = hz * g_config.dissector_thread_conv_timeout;
    rte_timer_reset(timer, tick, SINGLE, lcore_id, fct, arg);

    return 0;
}

uint8_t *dpi_conv_find_key_value(struct flow_info *flow, char *key) {
    int               conv_ring_id = flow->thread_id % g_config.flow_conv_thread_num;
    // thread_conv_info *info = g_hash_table_lookup(g_conversation_hash[conv_ring_id], key);
    // if (info) {
    //     return info->value;
    // } else {
    //     return NULL;
    // }
    return NULL;
}

// 解析线程 构造好key与value
void dpi_conv_insert_key_value(struct flow_info *flow, char *key,int key_len, uint8_t *value,int value_len,enum conv_status status) {
    int      conv_ring_id = 0;
    unsigned free_space;
    int      ret = 0;
    thread_conv_info *info =NULL;
    conv_ring_id = flow->thread_id % g_config.flow_conv_thread_num;
    info = malloc(sizeof(thread_conv_info));
    info->real_protocol_id = flow->real_protocol_id;
    info->key = malloc(key_len+1);
    memset(info->key, 0, key_len+1);
    memcpy(info->key, key, key_len);
    info->value = value;
    info->status = status;
    info->last_update_time = g_config.g_now_time;
    info->conv_thread_num = conv_ring_id;
    ret = rte_ring_mp_enqueue_burst(thread_conv_ring[conv_ring_id], (void *const *)&info, 1, &free_space);
    if (ret != 1) {
        dpi_thread_conv_timeout_func(info->key, info->value,&conv_ring_id);
        free(info->key);
        free(info);
        DPI_LOG(DPI_LOG_WARNING, "关联队列满 直接释放当前关联信息");
    }
}

//关联线程
int conversation_thread_func(void *_thread_id)
{
    thread_conv_info *conv_info_burst[MAX_FLOW_BURST];
    thread_conv_info *conv_info           = NULL;
    uint32_t          burst_cnt = 0;
    unsigned int      ring_cnt       = 0;
    long              core_id        = 0;
    cpu_set_t         cpuset;
    int               ret            = 0;
    int               ring_id = 0;
    char              thread_name[16];
    time_t            time_cycle_thread = 0;

    long     thread_id = (long)_thread_id;
    unsigned lcore_id = rte_lcore_id();

    printf("conv func locore_id = %d, thread_id = %ld\n", lcore_id, thread_id);


    ring_id = lcore_id % g_config.flow_conv_thread_num;
    DPI_LOG(DPI_LOG_DEBUG,"dequeue ring id %d\n", ring_id);
    struct rte_timer timer ;
    rte_timer_init(&timer);

    GHashTable    *conversation_hash =
        g_hash_table_new_full(g_str_hash, g_str_equal, conversation_hash_free_key, conversation_hash_free_value);
    if (!conversation_hash) {
        DPI_LOG(DPI_LOG_ERROR, "conversation_hash create error.");
        exit(0);
    };
    g_conversation_hash[ring_id] = conversation_hash;//登记到全局变量中
    while (true) {
        if (unlikely(flow_conv_running == 0)) {
            if(rte_ring_empty(thread_conv_ring[ring_id]))
                break;
        }
        burst_cnt = rte_ring_sc_dequeue_burst(thread_conv_ring[ring_id], (void **)conv_info_burst, MAX_FLOW_BURST, NULL);
        if (unlikely(g_config.stop_dissect)) {
            dpi_conv_thread_stop(ring_id);
            g_config.conv_flag_per_core[ring_id] = 1;
            printf("\n[EXIT]conv thread %u exit normal\n", ring_id);
            pthread_exit(0);
        }
        if (burst_cnt == 0) {
            rte_timer_manage();
            if (g_config.g_now_time - time_cycle_thread > g_config.dissector_thread_conv_timeout) {
                //推动线程级别超时的重置定时器
                dpi_thread_timer_reset(&timer, prcess_thread_timer_cb, (void *)&ring_id);
                time_cycle_thread = g_config.g_now_time;
            }
            continue;
        }
        for (uint32_t i = 0; i < burst_cnt; ++i) {
            // printf("rte_ring_sc_dequeue_burst\n");
            conv_info = conv_info_burst[i];
            //conv_dissect操作
            thread_conv_info *info = g_hash_table_lookup(g_conversation_hash[ring_id], conv_info->key);
            if (info) {
                //插入
                if(thread_conv_array[conv_info->real_protocol_id].conv_dissect){
                    thread_conv_array[conv_info->real_protocol_id].conv_dissect(conv_info->key, conv_info->value, info->value);
                }
                info->last_update_time = g_config.g_now_time;
                free(conv_info->key);
                free(conv_info);
            } else {
                g_hash_table_insert(conversation_hash, conv_info->key, (void *)conv_info);
                if(thread_conv_array[conv_info->real_protocol_id].conv_dissect){
                    thread_conv_array[conv_info->real_protocol_id].conv_dissect(conv_info->key, conv_info->value, conv_info->value);
                }
                conv_info->last_update_time = g_config.g_now_time;
            }
        }
    }
    g_conversation_hash[ring_id] = NULL;

    return 0;
}

void init_flow_conv(void)
{

    pthread_t th;
    int       status;
    char flow_conv_ring_name[64] = {0};
    long      core_id;

    for (unsigned int i = 0; i < g_config.flow_conv_thread_num; ++i) {

      char flow_conv_ring_name[64] = {0};
      snprintf(flow_conv_ring_name, sizeof(flow_conv_ring_name), "flow_conv_ring_%d_%d", g_config.socketid, i);
      thread_conv_ring[i] =
          rte_ring_create(flow_conv_ring_name, g_config.packet_ring_size, g_config.socketid, RING_F_SC_DEQ);
      if (thread_conv_ring[i] == NULL) {
        DPI_LOG(DPI_LOG_ERROR, "error while create packet ring");
        exit(-1);
      }
      if (rte_ring_lookup(flow_conv_ring_name) != thread_conv_ring[i]) {
        DPI_LOG(DPI_LOG_ERROR, "Cannot lookup ring from its name");
        exit(-1);
      }
    }

}