#ifndef DPI_HTTP_H
#define DPI_HTTP_H

#include <netinet/in.h>
#include <glib.h>
#include <string.h>
#include <ctype.h>
#include <sys/time.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_dissector.h"



#define HTTP_CONTENT_LEN_MAX        655350
#define HTTP_UNKNOWN_LINE_NUM_MAX   50
#define HTTP_MAX_URI_LEN            1024

#define CHUNK_SIZE_MAX              32

#define HTTP_NEED_MORE  0
#define HTTP_ERROR     -1


#define CPU_LEN     64
#define OS_LEN      64
#define TYPE_LEN    20
#define LOGIN_LEN   128
#define APP_LEN     128

#define URI_KEY_MAX        (50)
#define BODY_MAX        (8192)


enum _http_index_em{

    EM_HTTP_METHOD,
    EM_HTTP_URI,
    EM_HTTP_URI_COUNT,
    EM_HTTP_URI_KEYS,
    EM_HTTP_URI_KEYS_COUNT,
    EM_HTTP_URI_PATH,
    EM_HTTP_URI_PATH_COUNT,
    EM_HTTP_VERSION,
    EM_HTTP_STATUS,
    EM_HTTP_RESPONSESTATUS,
    EM_HTTP_CACHE_CONTROL,
    EM_HTTP_CONNECTION,
    EM_HTTP_COOKIE,
    EM_HTTP_COOKIE2,
    EM_HTTP_COOKIE_KEYS,
    EM_HTTP_DATE,
    EM_HTTP_PRAGMA,
    EM_HTTP_TRAILER,
    EM_HTTP_TRANSFER_ENCODING,
    EM_HTTP_UPGRADE,
    EM_HTTP_VIA,
    EM_HTTP_VIA_COUNT,
    EM_HTTP_WARNING,
    EM_HTTP_ACCEPT,
    EM_HTTP_ACCEPT_CHARSET,
    EM_HTTP_ACCEPT_ENCODING,
    EM_HTTP_ACCEPT_LANGUAGE,
    EM_HTTP_AUTHORIZATION,
    EM_HTTP_AUTH_USERNAME,
    EM_HTTP_EXPECT,
    EM_HTTP_FROM,
    EM_HTTP_HOST,
    EM_HTTP_HOST_COUNT,
    EM_HTTP_IF_MATCH,
    EM_HTTP_IF_MODIFIED_SINCE,
    EM_HTTP_IF_NONE_MATCH,
    EM_HTTP_IF_RANGE,
    EM_HTTP_IF_UNMODIFIED_SINCE,
    EM_HTTP_MAX_FORWARDS,
    EM_HTTP_PROXY_AUTHORIZATION,
    EM_HTTP_PROXY_TYPE,
    EM_HTTP_PROXY_LOGIN,
    EM_HTTP_PROXY_AUTHORIZATION_INFO,
    EM_HTTP_RANGE,
    EM_HTTP_REFERER,
    EM_HTTP_TE,
    EM_HTTP_USER_AGENT,
    EM_HTTP_USER_AGENT_NUM,
    EM_HTTP_PROXY_PROXYAGENT,
    EM_HTTP_USER_CPU,
    EM_HTTP_USER_OS,
    EM_HTTP_ACCEPT_RANGES,
    EM_HTTP_AGE,
    EM_HTTP_ETAG,
    EM_HTTP_LOCATION,
    EM_HTTP_PROXY_AUTHENTICATE,
    EM_HTTP_INQUIRY_TYPE,
    EM_HTTP_PROXY_CONNECT_HOST,
    EM_HTTP_PROXY_CONNECT_PORT,
    EM_HTTP_RETRY_AFTER,
    EM_HTTP_SERVER,
    EM_HTTP_VARY,
    EM_HTTP_WWW_AUTHENTICATE,
    EM_HTTP_ALLOW,
    EM_HTTP_CONTENT_ENCODING_C,
    EM_HTTP_CONTENT_ENCODING_S,
    EM_HTTP_CONTENT_LANGUAGE,
    EM_HTTP_CONTENT_LENGTH,
    EM_HTTP_CONTENT_LENGTH_REQ,
    EM_HTTP_CONTENT_LENGTH_RSP,
    EM_HTTP_CONTENT_LOCATION,
    EM_HTTP_CONTENT_MD5,
    EM_HTTP_CONTENT_RANGE,
    EM_HTTP_CONTENT_TYPE,
    EM_HTTP_EXPIRES,
    EM_HTTP_REFRESH,
    EM_HTTP_LAST_MODIFIED,
    EM_HTTP_X_FORWARDED_FOR,
    EM_HTTP_SET_COOKIE,
    EM_HTTP_SET_COOKIE2,
    EM_HTTP_DNT,
    EM_HTTP_X_POWERED_BY,
    EM_HTTP_P3P,
    EM_HTTP_CLIENT_HEAD,
    EM_HTTP_CLIENT_BODY,
    EM_HTTP_GET_SVR_REQ_URL,    //客户端获取服务器请求的 URL
    EM_HTTP_ENTITY_TITLE_HEAD,    //实体标题首部
    EM_HTTP_H_A_NUMBER,
    EM_HTTP_H_X_NUMBER,
    EM_HTTP_APPTYPE,

    EM_HTTP_X_HTTP_TOKEN,
    EM_HTTP_X_COOKIE_TOKEN,
    EM_HTTP_X_SINKHOLE,
    EM_HTTP_REQ_HEAD_FIELDS,
    EM_HTTP_REQ_HEAD_FIELDS_MD5,
    EM_HTTP_REQ_HEAD_FIELDS_NUM,
    EM_HTTP_REQ_METHOD_NUM,
    EM_HTTP_REQ_VERSION_NUM,
    EM_HTTP_REQ_BODY,
    EM_HTTP_RSP_HEAD_FIELDS,
    EM_HTTP_RSP_HEAD_FIELDS_MD5,
    EM_HTTP_RSP_CACHE_CONTROL,
    EM_HTTP_RSP_CONNECTION,
    EM_HTTP_RSP_PRAGMA,
    EM_HTTP_RSP_ACCEPT_RANGES,
    EM_HTTP_RSP_ACCEPT_CHARSET,
    EM_HTTP_RSP_CONTENT_TYPE,
    EM_HTTP_RSP_VERSION,
    EM_HTTP_RSP_BODY,
    EM_HTTP_RSP_FULLTEXT_LEN,
    EM_HTTP_URL,
    EM_HTTP_CONDISPUP,
    EM_HTTP_CONDISPDOWN,
    EM_HTTP_URISEARCH,
    EM_HTTP_IMSI,
    EM_HTTP_IMEI,

#ifdef DPI_SDT_ZDY
    EM_HTTP_V51,
#endif
#if defined (DPI_SDT_ZDY) || defined (DPI_SDT_YNAO)
    EM_HTTP_LOCAL_FILENAME,
    EM_HTTP_LOCAL_FILEPATH,
#endif
#ifdef DPI_SDT_ZDY
    EM_HTTP_CONDISP,
    EM_HTTP_REMAINDERLINE,
#endif
    EM_HTTP_MAX
};


typedef enum http_parsing_state
{
    HTTP_PARSING_STATE_ERROR = 0,
    HTTP_PARSING_STATE_NEW_MSG,                       // 开始了一个新的 http 消息;
    HTTP_PARSING_STATE_PARSING_MSG_HEADER,            // 解析 http 消息头;
    HTTP_PARSING_STATE_PARSING_MSG_BODY,              // 解析 http body;
} http_parsing_state_enum;

typedef enum http_body_type
{
    HTTP_BODY_TYPE_UNKNOWN = 0,
    HTTP_BODY_TYPE_CONTENT_LENGTH,                    // 通过 Content-Length 说明 body 长度;直接写入
    HTTP_BODY_TYPE_CHUNKED,                           // 使用 chunked 方式传输 body;需要计算多个chunk
    HTTP_BODY_TYPE_RANGE,                             // 使用 range 方式传输 body; 需要偏移量写入
} http_body_type_enum;

typedef enum http_boundary_state
{
  HTTP_BOUNDARY_STATE_UNKNOWN,                       //需要搜索filename
  HTTP_BOUNDARY_STATE_WRITING,                          //重组中
}http_boundary_state_enum;

typedef enum http_chunked_state
{
  HTTP_CHUNKED_STATE_UNKNOWN,                       //需要搜索filename
  HTTP_CHUNKED_STATE_WRITING,                          //重组中
}http_chunked_state_enum;


struct http_unknown_line {
    uint32_t key_len;
    uint32_t val_len;
    const uint8_t *key_ptr;
    const uint8_t *val_ptr;
};


struct http_info {
    GHashTable *table;
    uint32_t   header_num;
    uint32_t   empty_line_position;

    PROTOCOL_HEAD_DEF(version)
    PROTOCOL_HEAD_DEF(code)
    PROTOCOL_HEAD_DEF(response_code)
    PROTOCOL_HEAD_DEF(uri)
    PROTOCOL_HEAD_DEF(method)
    PROTOCOL_HEAD_DEF(content)
    PROTOCOL_HEAD_DEF(rsp_version)  // 响应版本
    PROTOCOL_HEAD_DEF(username)  // 认证的用户名


    char       uaCPU[CPU_LEN];
    char       uaOS[OS_LEN];
    char       proxy_login[LOGIN_LEN];
    char       proxy_type[TYPE_LEN];
    char       inquiry_type[TYPE_LEN];
    char       filename[COMMON_FILE_PATH];   //实体文件还原文件名
    char       http_filename[COMMON_FILE_PATH];// http协议中传输的真实文件名 boundary中的文件名权重大于uri中文件名
    char       boundary[256];
    int        boundary_key_len;
    int        http_filelen;
    uint8_t    action;
    uint8_t    get_content_flag;
    uint8_t	   is_request; // 请求，响应标识，用来区分共有的字段，比如 Cache-Control, Connection, Pragma等。
	  uint8_t	   host_num; // host的数量 包含多个host的请求
    int		     user_agent_num;
    uint8_t    encoding;//需要进行gzip解码


    uint16_t status;



    char       app_type[APP_LEN];
    uint8_t    uri_num;  // uri的数量
    uint8_t    via_num;  // via的个数， 以逗号间隔
    char       req_heads[2048]; // 多个请求头字段的key组合，分号隔开
    char       req_heads_md5[100]; // 多个请求头字段的md5
    int           req_heads_num;  // 请求头字段的数量
    char       rsp_heads[2048]; // 同req
    char       rsp_heads_md5[100]; // 同 req
    int           rsp_heads_num;  // 响应头字段的数量

    uint8_t       req_method_num;  // 请求方法的数量
    uint8_t    req_version_num; // 请求版本的数量

    uint16_t   rsp_fulltext_len;  // 图片，视频等文本的大小
    char       url[256];

    char       cookie_keys[2048]; // cookie的key集合
    int           cookie_keys_num;

    char       req_body[2048];
    uint32_t   req_len;

    char       rsp_body[2048];
    uint32_t   rsp_len;


    uint8_t    chunk_flag;

    char       uriSearch[512];
    int        is_search;        //-1:非搜索引擎host  0:未知需要探测   1:是  2:已经有过一次搜索动作
    char       conDisp[512];
    char       imei[32];
    char       imsi[32];

};

struct http_cache
{
    char     *cache;
    int       cache_size;
    int       cache_hold;
};
struct cwmp_cache
{
    char     *addr;
    int       cache_size;
    int       cache_hold;
};

struct http_range{
    char unit[32];
    uint64_t start;
    uint64_t end;
    uint64_t size;
    bool has_range;
    bool size_known;
};


struct http_file{
    FILE              *fp;
    uint64_t           eof;  //range总长度
    uint64_t           last_update_time;
    char               suffix[8];
    char               name[1024];
    char               local[1024];
    char*               frame_body;
    int                frame_len;
    uint64_t           write_offset;
    // struct http_range  *range;
    bool has_range;      //[长度未知]为false
    bool size_known;
    uint8_t            encoding;
    char               c_type[256];
    char               head[1024];
    char    file_name[COMMON_FILE_PATH];
    //为了输出log使用
    struct flow_info    *flow;
    precord_t          *record;

};

struct http_session
{
    int                direction[2];
    char               host[256];
    char               username[256];
    char               password[256];
    char               method[16];
    char               *uri;
    char              decode_uri_filename[2048];
    struct http_cache  cache[2];
    uint8_t            flag;
    uint16_t           request_count;
    uint16_t           response_count;
    uint8_t            is_valid;
    uint8_t            is_boundary;
    uint8_t            is_formdata;   //v2中不使用
    uint8_t            is_range;   //v2中不使用
    uint8_t            form_urlencoded;  //application/x-www-form-urlencoded
    uint8_t            rsp_fulltext_len;  //c_type 为视频或图片
    char               c_type[512]; //在没有文件名的情况下 保存content-type，获取文件名
    uint8_t            get_err;

    uint64_t                 write_offset;      //此session操作文件句柄的位置
    struct http_range        range;             //此session中获取到的range(请求或响应)
    uint64_t                 deal_content_len;  //http已经处理的payload的总长度
    uint64_t                 content_len;       //在http头中获取到的content-len
    struct http_info         http_value;         //一个http头更新一次
    http_parsing_state_enum  parsing_state;     //解析消息头状态
    http_body_type_enum      body_state;        // 解析消息负载状态
    http_boundary_state_enum boundary_state;    //boundary状态
    http_chunked_state_enum  chunked_state;    //boundary状态
    uint64_t                 http_chunked_len;  //本条http携带的chunked长度，使用本长度重置payload指针偏移
    uint64_t                 http_chunked_write_len;  //本条http已经处理的chunked长度
    uint8_t                  filter;                //后缀过滤器
    struct http_file        *file;        //登记本条流的文件属性
    precord_t               *record;

    char auth[1024];
    char uri_path[256];                // uri path
    int  uri_path_num;                 // uri path 的数量
    char uri_keys[URI_KEY_MAX][1024];  // uri key 的组合， 分号隔开
    int  uri_keys_num;                 // uri key 的数量
    //cwmp 协议使用
    struct cwmp_cache cwmp_cache[2];
};





#endif

