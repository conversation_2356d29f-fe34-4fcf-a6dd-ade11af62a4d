#include "dpi_flow_timer.h"
#include "rte_lcore.h"

extern struct global_config g_config;

static uint64_t dpi_flow_timer_get_tick(struct flow_info *flow)
{
  uint8_t proto;
  uint64_t tick;
  proto = flow->tuple.inner.proto;
  uint64_t hz = rte_get_timer_hz() / 1000; // 获取系统时钟频率,精度为 1ms

  switch (proto) {
  case IPPROTO_TCP :
    tick = g_config.tcp_flow_timeout * hz;
    break;
  case IPPROTO_UDP :
    tick = g_config.udp_flow_timeout * hz;
    break;
  case IPPROTO_SCTP :
    tick = g_config.sctp_flow_timeout * hz;
    break;
  default :
    tick = g_config.tcp_flow_timeout * hz;
    break;
  }

  return tick;
}

int dpi_flow_timer_init(struct work_process_data *workflow, struct flow_info *flow)
{
  rte_timer_init(&flow->timer);
  return 0;
}


int dpi_flow_timer_reset(struct work_process_data *workflow, struct flow_info *flow, rte_timer_cb_t fct, void *arg)
{
    unsigned lcore_id = rte_lcore_id();
    uint64_t tick = dpi_flow_timer_get_tick(flow);
    rte_timer_reset(&flow->timer, tick, SINGLE, lcore_id, fct, arg);

  return 0;
}

void dpi_flow_timer_reset_sync(struct work_process_data *workflow, struct flow_info *flow, rte_timer_cb_t fct, void *arg)
{
  unsigned lcore_id = rte_lcore_id();
  uint64_t tick = dpi_flow_timer_get_tick(flow);
  rte_timer_reset(&flow->timer, tick, SINGLE, lcore_id, fct, arg);

  return;
}


int dpi_flow_timer_reset_tick(struct work_process_data *workflow, struct flow_info *flow, int tick ,rte_timer_cb_t fct, void *arg)
{
  unsigned lcore_id = rte_lcore_id();
  uint64_t hz = rte_get_timer_hz(); // 获取系统时钟频率
  uint64_t tick_hz = tick * hz;
  rte_timer_reset(&flow->timer, tick_hz, SINGLE, lcore_id, fct, arg);

  return 0;
}