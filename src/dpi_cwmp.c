/****************************************************************************************
 * 文 件 名 : dpi_cwmp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 *修改: hongll  2022/08/18
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2022 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <string.h>
#include <sys/time.h>
#include <openssl/aes.h>

#include "dpi_common.h"
#include "list.h"

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_log.h"
#include "dpi_ocsp.h"

#include "dpi_http.h"
#include "dpi_cwmp.h"
#include "dpi_dissector.h"


extern rte_atomic64_t log_256k_fail;
extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];
extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern const char   *mxmlElementGetAttr(mxml_node_t *node, const char *name);
#define ENABLE_OLD				(0)

#if ENABLE_OLD
size_t ws_base64_decode_inplace(char *s);
#endif

#define CWMP_TCP_PORT			(8090)  // unknown ? to be updated

#define CWMP_LEN				(256)
#define PARAM_MAX				(20)
#define METHOD_MAX				(20)
#define ATTRS_MAX				(20)

#define STR_SUMMARY				"InternetGatewayDevice.DeviceSummary"
#define STR_HARD_VER			"InternetGatewayDevice.DeviceInfo.HardwareVersion"
#define STR_SOFT_VER			"InternetGatewayDevice.DeviceInfo.SoftwareVersion"
#define STR_MANAGER_URL			"InternetGatewayDevice.ManagementServer.URL"
#define CONN_MANAGER_URL			"InternetGatewayDevice.ManagementServer.ConnectionRequestURL"


/*
 * An CPE/ACS MUST be able to accept a SOAP request with a total envelope size of at
 * least 32 kilobytes (32768 bytes) without resulting in a “Resources Exceeded”
 * response.
*/

#define CWMP_SOAP_REQ_LEN		(32768 + 1)

#define FREE(x) do { free(x); x = NULL; } while (0);


static struct cwmp_namespaces ns;



struct cwmp_info {

	//	struct list_head events;
	struct list_head notifications;
	struct list_head downloads;
	struct list_head uploads;
	struct list_head scheduled_informs;

	struct deviceid dev;

	struct event events[__EVENT_MAX];
	int			event_num;

	struct param_value params[PARAM_MAX];
	int			params_num;

	char		methods[METHOD_MAX][64];
	int			method_num; // 实际获取到的rpc method数量

	struct param_value _set_param_values[PARAM_MAX];
	int			set_param_num;

	struct param_value _get_param_values[PARAM_MAX];   //  InternetGatewayDevice.ManagementServer.URL --> ManagerURL
	int			get_param_num;

	struct download _download;
	struct download_response _download_rsp;

	struct upload _upload;
	struct upload_response _upload_rsp;

	struct param_attr_name get_attrs_names[ATTRS_MAX];
	int			get_attrs_name_num;

	struct object_action _add_del_obj;
	struct object_action_response _add_del_obj_rsp;

	struct schedule_inform _schedule_inform;

	char		command_key[32];
	char		summary[256];		// InternetGatewayDevice.DeviceSummary
	char		hard_ver[256];		// InternetGatewayDevice.DeviceInfo.HardwareVersion
	char		soft_ver[256];		// InternetGatewayDevice.DeviceInfo.SoftwareVersion
	char		manager_url[256];   // InternetGatewayDevice.ManagementServer.URL
	char		connect_url[256];
	char		connect_udp_url[256];
	char		filename[256];
	int			status;
	char		cwmpid[CWMP_LEN];
	char		cur_time[32];

	int			fault;
	int			max_envelope;
	int			retry_count;
	int			download_count;
	int			upload_count;
	int			end_session;
	int			method_id;
	bool		get_rpc_methods;
	bool		hold_requests;

};

static int xml_handle_inform(mxml_node_t *body_in, struct cwmp_info *info);

/* CPE GetRPCMethods */
static int xml_handle_get_rpc_methods(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_set_parameter_values(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_get_parameter_values(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_get_parameter_names(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_set_parameter_attributes(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_download(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_upload(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_factory_reset(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_reboot(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_get_parameter_attributes(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_schedule_inform(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_AddObject(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_DeleteObject(mxml_node_t *body_in, struct cwmp_info *info);

/* response */
static int xml_handle_inform_response(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_get_rpc_methods_response(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_get_parameter_values_response(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_download_response(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_upload_response(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_factory_reset_response(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_reboot_response(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_get_parameter_attributes_response(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_schedule_inform_response(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_AddObject_response(mxml_node_t *body_in, struct cwmp_info *info);

static int xml_handle_DeleteObject_response(mxml_node_t *body_in, struct cwmp_info *info);


const struct rpc_method rpc_methods[] = {
	{"Inform",							xml_handle_inform },
	{ "GetRPCMethods",					xml_handle_get_rpc_methods },
	{ "SetParameterValues",				xml_handle_set_parameter_values },
	{ "GetParameterValues",				xml_handle_get_parameter_values },
	{ "GetParameterNames",				xml_handle_get_parameter_names },
	{ "GetParameterAttributes",			xml_handle_get_parameter_attributes },
	{ "SetParameterAttributes",			xml_handle_set_parameter_attributes },
	{ "AddObject",						xml_handle_AddObject },
	{ "DeleteObject",					xml_handle_DeleteObject },
	{ "Download",						xml_handle_download },
	{ "Upload",							xml_handle_upload },
	{ "Reboot",							xml_handle_reboot },
	{ "FactoryReset",					xml_handle_factory_reset },
	{ "ScheduleInform",					xml_handle_schedule_inform },

	{ "InformResponse",					xml_handle_inform_response },
	{ "GetRPCMethodsResponse",			xml_handle_get_rpc_methods_response },
	{ "GetParameterValuesResponse",		xml_handle_get_parameter_values_response },
	{ "Download",						xml_handle_download_response },
	{ "Upload",							xml_handle_upload_response },
	{ "FactoryReset",					xml_handle_factory_reset_response },
	{ "RebootResponse",					xml_handle_reboot_response },
	{ "GetParameterAttributesResponse",	xml_handle_get_parameter_attributes_response },
	{ "ScheduleInform",					xml_handle_schedule_inform_response },
	{ "AddObject",						xml_handle_AddObject_response },
	{ "DeleteObject",					xml_handle_DeleteObject_response },
};

struct event_code event_code_array[] = {
	[EVENT_BOOTSTRAP] = {(char *)"0 BOOTSTRAP", EVENT_SINGLE, EVENT_REMOVE_AFTER_INFORM},
	[EVENT_BOOT] = {(char *)"1 BOOT", EVENT_SINGLE, EVENT_REMOVE_AFTER_INFORM},
	[EVENT_PERIODIC] = {(char *)"2 PERIODIC", EVENT_SINGLE, EVENT_REMOVE_AFTER_INFORM},
	[EVENT_SCHEDULED] = {(char *)"3 SCHEDULED", EVENT_SINGLE, EVENT_REMOVE_AFTER_INFORM},
	[EVENT_VALUE_CHANGE] = {(char *)"4 VALUE CHANGE", EVENT_SINGLE, EVENT_REMOVE_AFTER_INFORM},
	[EVENT_KICKED] = {(char *)"5 KICKED", EVENT_SINGLE, EVENT_REMOVE_AFTER_INFORM},
	[EVENT_CONNECTION_REQUEST] = {(char *)"6 CONNECTION REQUEST", EVENT_SINGLE, EVENT_REMOVE_AFTER_INFORM | EVENT_REMOVE_NO_RETRY},
	[EVENT_TRANSFER_COMPLETE] = {(char *)"7 TRANSFER COMPLETE", EVENT_SINGLE, EVENT_REMOVE_AFTER_TRANSFER_COMPLETE},
	[EVENT_DIAGNOSTICS_COMPLETE] = {(char *)"8 DIAGNOSTICS COMPLETE", EVENT_SINGLE, EVENT_REMOVE_AFTER_INFORM},
	[EVENT_REQUEST_DOWNLOAD] = {(char *)"9 REQUEST DOWNLOAD", EVENT_SINGLE, EVENT_REMOVE_AFTER_INFORM},
	[EVENT_AUTONOMOUS_TRANSFER_COMPLETE] = {(char *)"10 AUTONOMOUS TRANSFER COMPLETE", EVENT_SINGLE, EVENT_REMOVE_AFTER_TRANSFER_COMPLETE},
	[EVENT_M_REBOOT] = {(char *)"M Reboot", EVENT_MULTIPLE, EVENT_REMOVE_AFTER_INFORM},
	[EVENT_M_SCHEDULEINFORM] = {(char *)"M ScheduleInform", EVENT_MULTIPLE, EVENT_REMOVE_AFTER_INFORM},
	[EVENT_M_DOWNLOAD] = {(char *)"M Download", EVENT_MULTIPLE, EVENT_REMOVE_AFTER_TRANSFER_COMPLETE},
	[EVENT_M_UPLOAD] = {(char *)"M Upload", EVENT_MULTIPLE, EVENT_REMOVE_AFTER_TRANSFER_COMPLETE}
};


typedef enum _cwmp_enum_index {
	EM_CWMP_MANUFACTURER,
	EM_CWMP_OUI,
	EM_CWMP_PRODUCT_CLASS,
	EM_CWMP_SERIAL_NUMBER,
	EM_CWMP_EVENTCODE,
	EM_CWMP_COMMAND_KEY,
	EM_CWMP_SUMMARY,
	EM_CWMP_HARD_VER,
	EM_CWMP_SOFT_VER,
	EM_CWMP_MANAGER_URL,
	EM_CWMP_CONNECT_URL,
	EM_CWMP_CONNECT_UDP_URL,
	//	EM_CWMP_USERNAME,
	//	EM_CWMP_PASSWORD,
	EM_CWMP_DOWNLOAD_URL,
	EM_CWMP_DOWMLOAD_SUCCESS_URL,
	EM_CWMP_DOWNLOAD_FAILURE_URL,
	EM_CWMP_DOWNLOAD_DELAY_SECONDS,
	EM_CWMP_DOWNLOAD_FILE_SIZE,
	EM_CWMP_DOWNLOAD_FILE_TYPE,
	EM_CWMP_DOWNLOAD_TARGET_FILENAME,
	EM_CWMP_DOWNLOAD_USERNAME,
	EM_CWMP_DOWNLOAD_PASSWORD,
	EM_CWMP_DOWNLOAD_STATUS,
	EM_CWMP_DOWNLOAD_START_TIME,
	EM_CWMP_DOWNLOAD_COMPLETE_TIME,
	EM_CWMP_METHODS,
	EM_CWMP_SET_PARAM_NAMES,
	EM_CWMP_SET_PARAM_VALUES,
	EM_CWMP_GET_PARAM_NAMES,
	EM_CWMP_GET_PARAM_VALUES,

	EM_CWMP_MAX
} cwmp_enum_index;

static dpi_field_table cwmp_field_array[] = {
	DPI_FIELD_D(EM_CWMP_MANUFACTURER,			YA_FT_STRING,		"manufacturer"),
	DPI_FIELD_D(EM_CWMP_OUI,					YA_FT_STRING,		"oui"),
	DPI_FIELD_D(EM_CWMP_PRODUCT_CLASS,			YA_FT_STRING,		"product_class"),
	DPI_FIELD_D(EM_CWMP_SERIAL_NUMBER,			YA_FT_STRING,		"serial_number"),
	DPI_FIELD_D(EM_CWMP_EVENTCODE,				YA_FT_STRING,		"eventcode"),
	DPI_FIELD_D(EM_CWMP_COMMAND_KEY,			YA_FT_STRING,		"command_key"),
	DPI_FIELD_D(EM_CWMP_SUMMARY,				YA_FT_STRING,		"summary"),
	DPI_FIELD_D(EM_CWMP_HARD_VER,				YA_FT_STRING,		"hard_ver"),
	DPI_FIELD_D(EM_CWMP_SOFT_VER,				YA_FT_STRING,		"soft_ver"),
	DPI_FIELD_D(EM_CWMP_MANAGER_URL,			YA_FT_STRING,		"manager_url"),
	DPI_FIELD_D(EM_CWMP_CONNECT_URL,			YA_FT_STRING,		"connect_url"),
	DPI_FIELD_D(EM_CWMP_CONNECT_UDP_URL,		YA_FT_STRING,		"connect_udp_url"),
	DPI_FIELD_D(EM_CWMP_DOWNLOAD_URL,			YA_FT_STRING,		"download_url"),
	DPI_FIELD_D(EM_CWMP_DOWMLOAD_SUCCESS_URL,	YA_FT_STRING,		"download_success_url"),
	DPI_FIELD_D(EM_CWMP_DOWNLOAD_FAILURE_URL,	YA_FT_STRING,		"download_failure_url"),
	DPI_FIELD_D(EM_CWMP_DOWNLOAD_DELAY_SECONDS,	YA_FT_UINT32,		"download_delay_seconds"),
	DPI_FIELD_D(EM_CWMP_DOWNLOAD_FILE_SIZE,		YA_FT_UINT32,		"download_filesize"),
	DPI_FIELD_D(EM_CWMP_DOWNLOAD_FILE_TYPE,		YA_FT_STRING,		"download_filetype"),
	DPI_FIELD_D(EM_CWMP_DOWNLOAD_TARGET_FILENAME,YA_FT_STRING,		"download_target_filename"),
	DPI_FIELD_D(EM_CWMP_DOWNLOAD_USERNAME,		YA_FT_STRING,		"download_username"),
	DPI_FIELD_D(EM_CWMP_DOWNLOAD_PASSWORD,		YA_FT_STRING,		"download_password"),
	DPI_FIELD_D(EM_CWMP_DOWNLOAD_STATUS,		YA_FT_STRING,		"download_status"),
	DPI_FIELD_D(EM_CWMP_DOWNLOAD_START_TIME,	YA_FT_STRING,		"download_start_time"),
	DPI_FIELD_D(EM_CWMP_DOWNLOAD_COMPLETE_TIME,	YA_FT_STRING,		"download_complete_time"),
	DPI_FIELD_D(EM_CWMP_METHODS,				YA_FT_STRING,		"methods"),
	DPI_FIELD_D(EM_CWMP_SET_PARAM_NAMES,		YA_FT_STRING,		"set_param_names"),
	DPI_FIELD_D(EM_CWMP_SET_PARAM_VALUES,		YA_FT_STRING,		"set_param_values"),
	DPI_FIELD_D(EM_CWMP_GET_PARAM_NAMES,		YA_FT_STRING,		"get_param_names"),
	DPI_FIELD_D(EM_CWMP_GET_PARAM_VALUES,		YA_FT_STRING,		"get_param_values"),

};


static void xml_get_hold_request(mxml_node_t *tree, struct cwmp_info *info) {
	mxml_node_t *b;
	char *c;

	info->hold_requests = false;

	b = mxmlFindElement(tree, tree, "cwmp:NoMoreRequests", NULL, NULL, MXML_DESCEND);
	if (b) {
		b = mxmlWalkNext(b, tree, MXML_DESCEND_FIRST);

		if (b && b->value.opaque)
			info->hold_requests = (atoi(b->value.opaque)) ? true : false;
	}

	b = mxmlFindElement(tree, tree, "cwmp:HoldRequests", NULL, NULL, MXML_DESCEND);
	if (b) {
		b = mxmlWalkNext(b, tree, MXML_DESCEND_FIRST);

		if (b && b->value.opaque)
			info->hold_requests = (atoi(b->value.opaque)) ? true : false;
	}
}

static mxml_node_t* mxmlFindElementOpaque(mxml_node_t *node,	/* I - Current node */
	mxml_node_t *top,	/* I - Top node */
	const char *text,	/* I - Element text, if NULL return NULL */
	int descend) {	/* I - Descend into tree - MXML_DESCEND, MXML_NO_DESCEND, or MXML_DESCEND_FIRST */

	if (!node || !top || !text)
		return (NULL);

	node = mxmlWalkNext(node, top, descend);

	while (node != NULL)
	{
		if (node->type == MXML_OPAQUE
			&& node->value.opaque
			&& (!text || !strcmp(node->value.opaque, text))) {

			return (node);
		}

		if (descend == MXML_DESCEND)
			node = mxmlWalkNext(node, top, MXML_DESCEND);
		else
			node = node->next;
	}

	return (NULL);
}

static int xml_check_duplicated_parameter(mxml_node_t *tree) {
	mxml_node_t *b, *n = tree;
	while (n) {
		if (n && n->type == MXML_OPAQUE
			&& n->value.opaque
			&& n->parent->type == MXML_ELEMENT
			&& !strcmp(n->parent->value.element.name, "Name")) {

			b = n;
			while ((b = mxmlWalkNext(b, tree, MXML_DESCEND))) {
				if (b && b->type == MXML_OPAQUE
					&& b->value.opaque
					&& b->parent->type == MXML_ELEMENT
					&& !strcmp(b->parent->value.element.name, "Name")) {

					if (strcmp(b->value.opaque, n->value.opaque) == 0) {
						//						log_message(NAME, L_NOTICE, "Fault in the param: %s , Fault code: 9003 <parameter duplicated>\n", b->value.opaque);
						return 1;
					}
				}
			}
		}
		n = mxmlWalkNext(n, tree, MXML_DESCEND);
	}
	return 0;
}

/* CPE GetRPCMethods */
static int xml_handle_get_rpc_methods(mxml_node_t *tree_in, struct cwmp_info *info) {

	return 0;
}

/* GetRPCMethodsResponse */
static int xml_handle_get_rpc_methods_response(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *node, *b1, *b2, *tree = body_in;
	int i = 0;
	int flag;

	b1 = mxmlFindElement(tree, tree, "MethodList", NULL, NULL, MXML_DESCEND);
	if (!b1) return -1;


	flag = 0;
	b2 = mxmlGetFirstChild(b1);
	while (b2) {

		if (b2->child && b2->child->value.opaque) {
			if (!strcmp(b2->value.opaque, "string")) {
				snprintf(info->methods[i], sizeof(info->methods[i]), "%s", b2->child->value.opaque);
				flag++;
			}
		}

		b2 = mxmlGetNextSibling(b2);

		if (flag > 0)
			i++;

		if (i >= METHOD_MAX)
			break;
	}

	info->method_num = i;

	return 0;
}

/* SetParameterValues */
static int xml_handle_set_parameter_values(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *b = body_in, *n_list, *n_st, *n_param;
	int code = FAULT_9002;
	char *c, *attr;
	char *p, *p1, *p2;
	int whitespace;
	char tmp[256];
	int i;
	int i_cnt = 0;

	n_list = mxmlFindElement(body_in, body_in, "ParameterList", NULL, NULL, MXML_DESCEND);
	if (!n_list) return -1;

	c = (char *)mxmlElementGetAttr(n_list, "soap_enc:arrayType");
	if (!c || (*(c + 5) != ':')) {
		return -1;
	}

	attr = strdup((c + 6));
	p = attr;
	do {
		if (!p)
			break;

		if (*p == '[') p1 = p;
		if (*p == ']') p2 = p;

		--p;
	} while (1);

	if (!p1 && !p2 && p1 >= p2)
		return -1;

	memset(tmp, 0, sizeof(tmp));
	strncpy(tmp, p1 + 1, p2 - (p1 + 1));
	info->set_param_num = atoi(tmp);

	if (xml_check_duplicated_parameter(body_in)) {
		code = FAULT_9003;
		goto fault_out;
	}

	n_st = mxmlFindElement(n_list, n_list, "ParameterValueStruct", NULL, NULL, MXML_DESCEND);
	for (i = 0; i < info->set_param_num; i++) {

		if (!n_st || i >= PARAM_MAX)
			break;

		n_param = mxmlFindElement(n_st, n_st, "Name", NULL, NULL, MXML_DESCEND);
		if (n_param) {
			//			info->_set_param_values[i].name = (char *)mxmlGetText(n_param, &whitespace);
		}

		n_param = mxmlFindElement(n_st, n_st, "Value", NULL, NULL, MXML_DESCEND);
		if (n_param) {
			//			info->_set_param_values[i].value = (char *)mxmlGetText(n_param, &whitespace);
		}

		n_param = mxmlFindElement(n_st, n_st, "ParameterKey", NULL, NULL, MXML_DESCEND);
		if (n_param) {
			//			info->_set_param_values[i].key = (char *)mxmlGetText(n_param, &whitespace);
		}

		n_st = mxmlWalkNext(n_st, n_list, MXML_DESCEND);
	}

	return 0;

fault_out:
	return 0;
}

/* GetParameterValues */
static int xml_handle_get_parameter_values(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *b = body_in, *n_list, *n_st, *n_param;
	int code = FAULT_9002;
	char *c, *attr;
	char *p, *p1, *p2;
	int whitespace;
	char tmp[256];
	int i = 0;
	int i_cnt = 0;

	n_list = mxmlFindElement(body_in, body_in, "ParameterNames", NULL, NULL, MXML_DESCEND);
	if (!n_list) return -1;

	c = (char *)mxmlElementGetAttr(n_list, "soap_enc:arrayType");
	if (!c || (*(c + 5) != ':')) {
		return -1;
	}

	attr = strdup((c + 6));
	p = attr;
	do {
		if (!p)
			break;

		if (*p == '[') p1 = p;
		if (*p == ']') p2 = p;

		--p;
	} while (1);

	if (!p1 && !p2 && p1 >= p2)
		return -1;

	memset(tmp, 0, sizeof(tmp));
	strncpy(tmp, p1 + 1, p2 - (p1 + 1));
	info->set_param_num = atoi(tmp);

	//	if (xml_check_duplicated_parameter(body_in)) {
	//		code = FAULT_9003;
	//		goto fault_out;
	//	}

	n_st = mxmlFindElement(n_list, n_list, "ParameterValueStruct", NULL, NULL, MXML_DESCEND);
	if (n_st) {
		n_param = mxmlFindElement(n_st, n_st, "string", NULL, NULL, MXML_DESCEND);
		while (n_st && i < PARAM_MAX) {
			//			info->_get_param_values[i].name = (char *)mxmlGetText(n_param, &whitespace);

			n_param = mxmlWalkNext(n_param, n_st, MXML_DESCEND);
		}
	}

	return 0;
}

static int xml_handle_get_parameter_names(mxml_node_t *body_in, struct cwmp_info *info) {


	return 0;
}

static int xml_handle_set_parameter_attributes(mxml_node_t *body_in, struct cwmp_info *info) {


	return 0;
}

static int xml_handle_download(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *n, *t, *b = body_in, *n_tmp;

//	n_tmp = mxmlFindElement(b, b, "Download", NULL, NULL, MXML_DESCEND);
//	if (!n_tmp) return -1;

    n_tmp = body_in;

	n = mxmlFindElement(n_tmp, n_tmp, "CommandKey", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_download.key, sizeof(info->_download.key), "%s", n->child->value.opaque);
		snprintf(info->command_key, sizeof(info->command_key), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "FileType", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_download.file_type, sizeof(info->_download.file_type), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "URL", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_download.download_url, sizeof(info->_download.download_url), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "Username", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_download.username, sizeof(info->_download.username), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "Password", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_download.password, sizeof(info->_download.password), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "FileSize", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		info->_download.file_size = (uint32_t)atoi(n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "TargetFileName", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_download.target_file_name, sizeof(info->_download.target_file_name), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "DelaySeconds", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		info->_download.delay_seconds = (uint32_t)atoi(n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "SuccessURL", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_download.success_url, sizeof(info->_download.success_url), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "FailureURL", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_download.failure_url, sizeof(info->_download.failure_url), "%s", n->child->value.opaque);
	}

	return 0;
}

static int xml_handle_upload(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *n, *t, *b = body_in, *n_tmp;

//	n_tmp = mxmlFindElement(b, b, "Upload", NULL, NULL, MXML_DESCEND);
//	if (!n_tmp) return -1;

    n_tmp = body_in;
	n = mxmlFindElement(n_tmp, n_tmp, "CommandKey", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_upload.key, sizeof(info->_upload.key), "%s", n->child->value.opaque);
		snprintf(info->command_key, sizeof(info->command_key), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "FileType", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_upload.file_type, sizeof(info->_upload.file_type), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "URL", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_upload.upload_url, sizeof(info->_upload.upload_url), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "Username", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_upload.username, sizeof(info->_upload.username), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "Password", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_upload.password, sizeof(info->_upload.password), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "DelaySeconds", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		info->_upload.delay_seconds = (uint32_t)atoi(n->child->value.opaque);
	}


	return 0;
}

static int xml_handle_factory_reset(mxml_node_t *body_in, struct cwmp_info *info) {
	// 无参数 FactoryReset

	return 0;
}

static int xml_handle_reboot(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *n, *t, *b = body_in, *n_tmp;

//	n_tmp = mxmlFindElement(b, b, "Reboot", NULL, NULL, MXML_DESCEND);
//	if (!n_tmp) return -1;

    n_tmp = body_in;
	n = mxmlFindElement(n_tmp, n_tmp, "CommandKey", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->command_key, sizeof(info->command_key), "%s", n->child->value.opaque);
	}

	return 0;
}

static int xml_handle_get_parameter_attributes(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *node, *b1, *b2, *tree = body_in;
	int i = 0;
	int flag;

	b1 = mxmlFindElement(tree, tree, "ParameterNames", NULL, NULL, MXML_DESCEND);
	if (!b1) return -1;


	flag = 0;
	b2 = mxmlGetFirstChild(b1);
	while (b2) {

		if (b2->child && b2->child->value.opaque) {
			if (!strcmp(b2->value.opaque, "string")) {
				snprintf(info->get_attrs_names[i].name, sizeof(info->get_attrs_names[i].name), "%s", b2->child->value.opaque);
				flag++;
			}
		}

		b2 = mxmlGetNextSibling(b2);

		if (flag > 0)
			i++;

		if (i >= __EVENT_MAX)
			break;
	}

	info->event_num = i;

	return 0;
}

static int xml_handle_schedule_inform(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *n, *t, *b = body_in, *n_tmp;

//	n_tmp = mxmlFindElement(b, b, "ScheduleInform", NULL, NULL, MXML_DESCEND);
//	if (!n_tmp) return -1;

    n_tmp = body_in;
	n = mxmlFindElement(n_tmp, n_tmp, "DelaySeconds", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		info->_schedule_inform.delay_seconds = (uint32_t)atol(n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "CommandKey", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_schedule_inform.command_key, sizeof(info->_schedule_inform.command_key), "%s", n->child->value.opaque);
		snprintf(info->command_key, sizeof(info->command_key), "%s", n->child->value.opaque);
	}


	return 0;
}

static int xml_handle_AddObject(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *n, *t, *b = body_in, *n_tmp;

//	n_tmp = mxmlFindElement(b, b, "AddObject", NULL, NULL, MXML_DESCEND);
//	if (!n_tmp) return -1;

    n_tmp = body_in;
	n = mxmlFindElement(n_tmp, n_tmp, "ObjectName", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_add_del_obj.object_name, sizeof(info->_add_del_obj.object_name), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "ParameterKey", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_add_del_obj.parameter_key, sizeof(info->_add_del_obj.parameter_key), "%s", n->child->value.opaque);
	}

	return 0;
}

static int xml_handle_DeleteObject(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *n, *t, *b = body_in, *n_tmp;

//	n_tmp = mxmlFindElement(b, b, "DeleteObject", NULL, NULL, MXML_DESCEND);
//	if (!n_tmp) return -1;

    n_tmp = body_in;
	n = mxmlFindElement(n_tmp, n_tmp, "ObjectName", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_add_del_obj.object_name, sizeof(info->_add_del_obj.object_name), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "ParameterKey", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_add_del_obj.parameter_key, sizeof(info->_add_del_obj.parameter_key), "%s", n->child->value.opaque);
	}

	return 0;
}


/********************** response ************************/

static int xml_handle_get_parameter_values_response(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *node, *b1, *b2, *b3, *tree = body_in;
	int i = 0, j = 0;
	int flag, flag2;

	b1 = mxmlFindElement(tree, tree, "ParameterList", NULL, NULL, MXML_DESCEND);
	if (!b1) return -1;

	node = mxmlGetFirstChild(b1);
	while (node) {

		if (node->child && node->child->value.opaque && !strcmp(node->value.opaque, "ParameterValueStruct")) {

			flag = 0;
			b2 = mxmlGetFirstChild(node);
			while (b2) {

				if (b2->child && b2->child->value.opaque) {
					if (!strcmp(b2->value.opaque, STR_SUMMARY)) {
						snprintf(info->summary, sizeof(info->summary), "%s", b2->child->value.opaque);
						flag++;
					}
					else if (!strcmp(b2->value.opaque, STR_HARD_VER)) {
						snprintf(info->hard_ver, sizeof(info->hard_ver), "%s", b2->child->value.opaque);
						flag++;
					}
					else if (!strcmp(b2->value.opaque, STR_SOFT_VER)) {
						snprintf(info->soft_ver, sizeof(info->soft_ver), "%s", b2->child->value.opaque);
						flag++;
					}
					else if (!strcmp(b2->value.opaque, STR_MANAGER_URL)) {
						snprintf(info->manager_url, sizeof(info->manager_url), "%s", b2->child->value.opaque);
						flag++;
          }else if (!strcmp(b2->value.opaque, CONN_MANAGER_URL)) {
            snprintf(info->connect_url, sizeof(info->connect_url), "%s", b2->child->value.opaque);
						flag++;
          }
				}

				if (j >= 4)
					break;

				b2 = mxmlGetNextSibling(b2);
			}

			if (flag > 0)
				i++;
		}

		if (i >= ATTRS_MAX)
			break;

		node = mxmlGetNextSibling(node);
	}

	info->get_attrs_name_num = i;

	return 0;
}


static int xml_handle_download_response(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *n, *t, *b = body_in, *n_tmp;

//	n_tmp = mxmlFindElement(b, b, "DownloadResponse", NULL, NULL, MXML_DESCEND);
//	if (!n_tmp) return -1;

    n_tmp = body_in;
	n = mxmlFindElement(n_tmp, n_tmp, "Status", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		info->_download_rsp.status = (uint32_t)atol(n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "StartTime", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_download_rsp.start_time, sizeof(info->_download_rsp.start_time), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "CompleteTime", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_download_rsp.complete_time, sizeof(info->_download_rsp.complete_time), "%s", n->child->value.opaque);
	}

	return 0;
}

static int xml_handle_upload_response(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *n, *t, *b = body_in, *n_tmp;

//	n_tmp = mxmlFindElement(b, b, "DownloadResponse", NULL, NULL, MXML_DESCEND);
//	if (!n_tmp) return -1;

    n_tmp = body_in;
	n = mxmlFindElement(n_tmp, n_tmp, "Status", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		info->_upload_rsp.status = (uint32_t)atol(n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "StartTime", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_upload_rsp.start_time, sizeof(info->_upload_rsp.start_time), "%s", n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "CompleteTime", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		snprintf(info->_upload_rsp.complete_time, sizeof(info->_upload_rsp.complete_time), "%s", n->child->value.opaque);
	}

	return 0;
}

static int xml_handle_factory_reset_response(mxml_node_t *body_in, struct cwmp_info *info) {
	// no params

	return 0;
}

static int xml_handle_reboot_response(mxml_node_t *body_in, struct cwmp_info *info) {
	// no params

	return 0;
}

static int xml_handle_get_parameter_attributes_response(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *node, *b1, *b2, *b3, *tree = body_in;
	int i = 0, j = 0;
	int flag, flag2;

	b1 = mxmlFindElement(tree, tree, "ParameterList", NULL, NULL, MXML_DESCEND);
	if (!b1) return -1;

	node = mxmlGetFirstChild(b1);
	while (node) {

		if (node->child && node->child->value.opaque && !strcmp(node->value.opaque, "ParameterAttributeStruct")) {

			flag = 0;
			b2 = mxmlGetFirstChild(node);
			while (b2) {

				if (b2->child && b2->child->value.opaque) {
					if (!strcmp(b2->value.opaque, "Name")) {
						snprintf(info->get_attrs_names[i].name, sizeof(info->get_attrs_names[i].name), "%s", b2->child->value.opaque);
						flag++;
					}
					else if (!strcmp(b2->value.opaque, "Notification")) {
						info->get_attrs_names[i].notification = atoi(b2->child->value.opaque);
						flag++;
					}
					else if (!strcmp(b2->value.opaque, "AccessList")) {

						flag2 = 0;
						b3 = mxmlGetFirstChild(b2);
						while (b3) {
							if (b3->child && b3->child->value.opaque) {
								if (!strcmp(b2->value.opaque, "Name")) {
									snprintf(info->get_attrs_names[i].access_list[j], sizeof(info->get_attrs_names[i].access_list[j]), "%s", b2->child->value.opaque);
									flag2++;
								}
							}
						}

						if (flag2 > 0)
							j++;
					}
				}

				if (j >= ACCESS_MAX)
					break;

				info->get_attrs_names[i].access_num = j;

				b2 = mxmlGetNextSibling(b2);
			}

			if (flag > 0)
				i++;
		}

		if (i >= ATTRS_MAX)
			break;

		node = mxmlGetNextSibling(node);
	}

	info->get_attrs_name_num = i;

	return 0;
}

static int xml_handle_schedule_inform_response(mxml_node_t *body_in, struct cwmp_info *info) {
	//  no params


	return 0;
}

static int xml_handle_AddObject_response(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *n, *t, *b = body_in, *n_tmp;
	int whitespace;
	char *tmp;

//	n_tmp = mxmlFindElement(b, b, "AddObjectResponse", NULL, NULL, MXML_DESCEND);
//	if (!n_tmp) return -1;

    n_tmp = body_in;
	n = mxmlFindElement(n_tmp, n_tmp, "InstanceNumber", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		info->_add_del_obj_rsp.instance_number = (uint32_t)atoi(n->child->value.opaque);
	}

	n = mxmlFindElement(n_tmp, n_tmp, "Status", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		info->_add_del_obj_rsp.status = atoi(n->child->value.opaque);
	}


	return 0;
}

static int xml_handle_DeleteObject_response(mxml_node_t *body_in, struct cwmp_info *info) {
	mxml_node_t *n, *t, *b = body_in, *n_tmp;
	int whitespace;
	char *tmp;

//	n_tmp = mxmlFindElement(b, b, "DeleteObjectResponse", NULL, NULL, MXML_DESCEND);
//	if (!n_tmp) return -1;

    n_tmp = body_in;
	n = mxmlFindElement(n_tmp, n_tmp, "Status", NULL, NULL, MXML_DESCEND);
	if (n && n->child && n->child->value.opaque) {
		info->_add_del_obj_rsp.status = atoi(n->child->value.opaque);
	}

	return 0;
}

/**************************************************************************/


static mxml_node_t *xml_mxml_find_node_by_env_type(mxml_node_t *tree_in, char *bname) {
	mxml_node_t *b;
	char *c;
	int i;

	for (i = 0; i < (int)array_length(ns.soap_env) && ns.soap_env[i]; i++) {
		if (asprintf(&c, "%s:%s", ns.soap_env[i], bname) == -1)
			return NULL;

		b = mxmlFindElement(tree_in, tree_in, c, NULL, NULL, MXML_DESCEND);
		FREE(c);
		if (b) return b;
	}
	return NULL;
}

static void  mxml_error_callback(const char * user_data)
{

}

static int xml_handle_messages(const uint8_t *payload, uint32_t payload_len, struct cwmp_info *info) {

	if (!payload || payload_len == 0)
		return -1;

	mxml_node_t *tree_in = NULL, *b, *body_in;
	mxmlSetErrorCallback(mxml_error_callback);
	const struct rpc_method *method;
	int i, code = FAULT_9002;
	char *c;
	char *msg_in = (char *)payload;

	tree_in = mxmlLoadString(NULL, msg_in, MXML_OPAQUE_CALLBACK);
	if (!tree_in) goto error;

	b = mxmlFindElement(tree_in, tree_in, "cwmp:ID", NULL, NULL, MXML_DESCEND);
	/* ACS did not send ID parameter, we are continuing without it */
	if (!b) goto find_method;

	b = mxmlWalkNext(b, tree_in, MXML_DESCEND_FIRST);
	if (!b || !b->value.opaque) goto find_method;
	c = strdup(b->value.opaque);

	memset(info->cwmpid, 0, sizeof(info->cwmpid));
	snprintf(info->cwmpid, sizeof(info->cwmpid), "%s", c);
	FREE(c);

	//	b = mxmlNewOpaque(b, c);
	//	FREE(c);
	//	if (!b) goto error;

find_method:
	//	b = xml_mxml_find_node_by_env_type(tree_in, (char *)"Body");
	b = mxmlFindElement(tree_in, tree_in, "SOAP-ENV:Body", NULL, NULL, MXML_DESCEND);
	if (!b) {
		code = FAULT_9003;
    b = mxmlFindElement(tree_in, tree_in, "soap:Body", NULL, NULL, MXML_DESCEND);
    if (!b) {
        goto fault_out;
    }
  }
  while (1) {
		b = mxmlWalkNext(b, tree_in, MXML_DESCEND_FIRST);
		if (!b) {
			code = FAULT_9003;
			goto fault_out;
		}
		if (b->type == MXML_ELEMENT) break;
	}

	c = b->value.element.name;
	if (strchr(c, ':')) {
		char *tmp = strchr(c, ':');
		size_t ns_len = tmp - c;
		/*
				if (strlen(ns.cwmp) != ns_len) {
					code = FAULT_9003;
					goto fault_out;
				}

				if (strncmp(ns.cwmp, c, ns_len)) {
					code = FAULT_9003;
					goto fault_out;
				}
		*/
		c = tmp + 1;
	}
	else {
		code = FAULT_9003;
		goto fault_out;
	}
	method = NULL;
	for (i = 0; i < (int)array_length(rpc_methods); i++) {
		if (!strcmp(c, rpc_methods[i].name)) {
			method = &rpc_methods[i];
			break;
		}
	}
	if (method) {
		if (method->handler(b, info)) goto error;
	}
	else {
		code = FAULT_9000;
		goto fault_out;
	}

	mxmlDelete(tree_in);
	return 0;

fault_out:
	//	body_out = mxmlFindElement(tree_out, tree_out, "soap_env:Body", NULL, NULL, MXML_DESCEND);
	//	if (!body_out) goto error;
	mxmlDelete(tree_in);
	return 0;

error:
	mxmlDelete(tree_in);
	return -1;
}


/******************* parser inform  ************************/

/* Inform: Event*/
static int _xml_parse_events_inform(mxml_node_t *tree, struct cwmp_info *info) {
	mxml_node_t *node, *b1, *b2;
	int i = 0;
	int flag;

	b1 = mxmlFindElement(tree, tree, "Event", NULL, NULL, MXML_DESCEND);
	if (!b1) return -1;

	node = mxmlGetFirstChild(b1);
	while (node) {

		if (node->child && node->child->value.opaque && !strcmp(node->value.opaque, "EventStruct")) {

			flag = 0;
			b2 = mxmlGetFirstChild(node);
			while (b2) {

				if (b2->child && b2->child->value.opaque) {
					if (!strcmp(b2->value.opaque, "EventCode")) {
						snprintf(info->events[i].code, sizeof(info->events[i].code), "%s", b2->child->value.opaque);
						flag++;
					}
					else if (!strcmp(b2->value.opaque, "CommandKey")) {
						snprintf(info->events[i].key, sizeof(info->events[i].key), "%s", b2->child->value.opaque);
						flag++;
					}
				}

				b2 = mxmlGetNextSibling(b2);
			}

			if (flag > 0)
				i++;

			flag = 0;
		}

		if (i >= __EVENT_MAX)
			break;

		node = mxmlGetNextSibling(node);
	}

	info->event_num = i;

	return 0;
}

/* Inform: ParameterList */
static int _xml_parse_parameters_inform(mxml_node_t *tree, struct cwmp_info *info) {
	mxml_node_t *node, *b1, *b2;
	int i = 0;
	int flag;

	b1 = mxmlFindElement(tree, tree, "ParameterList", NULL, NULL, MXML_DESCEND);
	if (!b1) return -1;

	node = mxmlGetFirstChild(b1);
	while (node) {
		if (node->child && node->child->value.opaque && !strcmp(node->value.opaque, "ParameterValueStruct")) {

			flag = 0;
			b2 = mxmlGetFirstChild(node);
			while (b2) {

				if (b2->child && b2->child->value.opaque) {
					if (!strcmp(b2->value.opaque, "Name")) {
						snprintf(info->params[i].name, sizeof(info->params[i].name), "%s", b2->child->value.opaque);
						flag++;
					}
					else if (!strcmp(b2->value.opaque, "Value")) {
						snprintf(info->params[i].value, sizeof(info->params[i].value), "%s", b2->child->value.opaque);
						flag++;

						if (!strcmp(info->params[i].name, STR_SUMMARY)) {
							snprintf(info->summary, sizeof(info->summary), "%s", info->params[i].value);
						}
						else if (!strcmp(info->params[i].name, STR_HARD_VER)) {
							snprintf(info->hard_ver, sizeof(info->hard_ver), "%s", info->params[i].value);
						}
						else if (!strcmp(info->params[i].name, STR_SOFT_VER)) {
							snprintf(info->soft_ver, sizeof(info->soft_ver), "%s", info->params[i].value);
						}
						else if (!strcmp(info->params[i].name, STR_MANAGER_URL)) {
							snprintf(info->manager_url, sizeof(info->manager_url), "%s", info->params[i].value);
            } else if (!strcmp(info->params[i].name, CONN_MANAGER_URL)) {
              snprintf(info->connect_url, sizeof(info->connect_url), "%s",  info->params[i].value);
            }
					}
					else if (!strcmp(b2->value.opaque, "ParameterKey")) {
						snprintf(info->params[i].key, sizeof(info->params[i].key), "%s", b2->child->value.opaque);
						flag++;
					}
				}

				b2 = mxmlGetNextSibling(b2);
			}

			if (flag > 0)
				i++;

			flag = 0;
		}

		if (i >= PARAM_MAX)
			break;

		node = mxmlGetNextSibling(node);
	}

	info->params_num = i;

	return 0;
}

static int xml_handle_inform(mxml_node_t *body_in, struct cwmp_info *info) {

	mxml_node_t *tree, *b, *n, *parameter_list;
	mxml_node_t *n_devid;
	char *c;
	int counter = 0;
	int whitespace;

	tree = body_in;
	if (!tree) goto error;

	do {
		n_devid = mxmlFindElement(tree, tree, "DeviceId", NULL, NULL, MXML_DESCEND);
		if (n_devid) {
			n_devid = mxmlGetFirstChild(n_devid);
			while (n_devid) {

				if (n_devid->child && n_devid->child->value.opaque) {
					if (!strcmp(n_devid->value.opaque, "Manufacturer")) {
						snprintf(info->dev.manufacturer, sizeof(info->dev.manufacturer), "%s", n_devid->child->value.opaque);
					}
					else if (!strcmp(n_devid->value.opaque, "OUI")) {
						snprintf(info->dev.oui, sizeof(info->dev.oui), "%s", n_devid->child->value.opaque);
					}
					else if (!strcmp(n_devid->value.opaque, "ProductClass")) {
						snprintf(info->dev.product_class, sizeof(info->dev.product_class), "%s", n_devid->child->value.opaque);
					}
					else if (!strcmp(n_devid->value.opaque, "SerialNumber")) {
						snprintf(info->dev.serial_number, sizeof(info->dev.serial_number), "%s", n_devid->child->value.opaque);
					}
				}

				n_devid = mxmlGetNextSibling(n_devid);
			}
		}

		_xml_parse_events_inform(tree, info);

		b = mxmlFindElement(tree, tree, "RetryCount", NULL, NULL, MXML_DESCEND);
		if (b && b->child && b->child->value.opaque) {
			info->retry_count = atoi(b->child->value.opaque);
		}

		b = mxmlFindElement(tree, tree, "CurrentTime", NULL, NULL, MXML_DESCEND);
		if (b && b->child && b->child->value.opaque) {
			snprintf(info->cur_time, sizeof(info->cur_time), "%s", b->child->value.opaque);
		}

		b = mxmlFindElement(tree, tree, "MaxEnvelopes", NULL, NULL, MXML_DESCEND);
		if (b && b->child && b->child->value.opaque) {
			info->max_envelope = atoi(b->child->value.opaque);
		}

		_xml_parse_parameters_inform(tree, info);

	} while (0);


	mxmlDelete(tree);
	return 0;

error:
	mxmlDelete(tree);
	return -1;
}

static int xml_handle_inform_response(mxml_node_t *body_in, struct cwmp_info *info) {

	mxml_node_t *tree, *b;
	char *c;

	tree = body_in;
	if (!tree) goto error;

	b = xml_mxml_find_node_by_env_type(tree, (char *)"Fault");
	//	b = mxmlFindElement(tree, tree, "Fault", NULL, NULL, MXML_DESCEND);
	if (b) {
		b = mxmlFindElementOpaque(b, b, "8005", MXML_DESCEND);
		if (b) {
			info->fault = FAULT_ACS_8005;
			goto out;
		}
	}

	xml_get_hold_request(tree, info);

	b = mxmlFindElement(tree, tree, "MaxEnvelopes", NULL, NULL, MXML_DESCEND);
	if (!b) goto error;

	if (b && b->child && b->child->value.opaque) {
		info->max_envelope = atoi(b->child->value.opaque);
	}

	//	b = mxmlWalkNext(b, tree, MXML_DESCEND_FIRST);
	//	if (!b || !b->value.opaque)
	//		goto error;


out:
	mxmlDelete(tree);
	return 0;

error:
	mxmlDelete(tree);
	return -1;
}


static int write_cwmp_log(struct flow_info* flow, int direction, struct cwmp_info* info) {

	int *cwmp_reflect_array = map_fields_get_array(PROTOCOL_CWMP);
	if (NULL == cwmp_reflect_array) {
		return 0;
	}

	char tmp[100];
	char str[2048];
	int i, j;
	int idx = 0;
	struct tbl_log* log_ptr;

	if (rte_mempool_get(tbl_log_mempool, (void**)&log_ptr) < 0) {
		DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
		return PKT_OK;
	}
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
	write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "cwmp");

	for (j = 0; j < EM_CWMP_MAX; j++) {
		switch (j) {
		case EM_CWMP_MANUFACTURER:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->dev.manufacturer, strlen(info->dev.manufacturer));
			break;
		case EM_CWMP_OUI:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->dev.oui, strlen(info->dev.oui));
			break;
		case EM_CWMP_PRODUCT_CLASS:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->dev.product_class, strlen(info->dev.product_class));
			break;
		case EM_CWMP_SERIAL_NUMBER:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->dev.serial_number, strlen(info->dev.serial_number));
			break;
		case EM_CWMP_EVENTCODE:
			memset(str, 0, sizeof(str));
			for (i = 0; i < info->event_num; i++) {
				if (i != info->event_num - 1)
					strcat(str, ",");

				strcat(str, info->events[i].code);
			}
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
			break;
		case EM_CWMP_COMMAND_KEY:
			memset(str, 0, sizeof(str));
			for (i = 0; i < info->event_num; i++) {
				if (i != info->event_num - 1)
					strcat(str, ",");

				strcat(str, info->events[i].key);
			}
            if (strlen(str)) {
                if (strlen(info->command_key)) {
                    strcat(str, ",");
                    strcat(str, info->command_key);
                }
            }
            else
                strcat(str, info->command_key);

			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
			break;
		case EM_CWMP_SUMMARY:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->summary, strlen(info->summary));
			break;
		case EM_CWMP_HARD_VER:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->hard_ver, strlen(info->hard_ver));
			break;
		case EM_CWMP_SOFT_VER:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->soft_ver, strlen(info->soft_ver));
			break;
		case EM_CWMP_MANAGER_URL:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->manager_url, strlen(info->manager_url));
			break;
		case EM_CWMP_CONNECT_URL:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->connect_url, strlen(info->connect_url));
			break;
		case EM_CWMP_CONNECT_UDP_URL:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->connect_udp_url, strlen(info->connect_udp_url));
			break;
		case EM_CWMP_DOWNLOAD_URL:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->_download.download_url, strlen(info->_download.download_url));
			break;
		case EM_CWMP_DOWMLOAD_SUCCESS_URL:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->_download.success_url, strlen(info->_download.success_url));
			break;
		case EM_CWMP_DOWNLOAD_FAILURE_URL:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->_download.failure_url, strlen(info->_download.failure_url));
			break;
		case EM_CWMP_DOWNLOAD_DELAY_SECONDS:
			if (info->_download.delay_seconds > 0)
				write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->_download.delay_seconds);
			else
				write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
			break;
		case EM_CWMP_DOWNLOAD_FILE_SIZE:
			if (info->_download.file_size > 0)
				write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->_download.file_size);
			else
				write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
			break;
		case EM_CWMP_DOWNLOAD_FILE_TYPE:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->_download.file_type, strlen(info->_download.file_type));
			break;
		case EM_CWMP_DOWNLOAD_TARGET_FILENAME:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->_download.target_file_name, strlen(info->_download.target_file_name));
			break;
		case EM_CWMP_DOWNLOAD_USERNAME:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->_download.username, strlen(info->_download.username));
			break;
		case EM_CWMP_DOWNLOAD_PASSWORD:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->_download.password, strlen(info->_download.password));
			break;
		case EM_CWMP_DOWNLOAD_STATUS:
			if (info->_download_rsp.status >= 0)
				write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->_download_rsp.status);
			else
				write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
			break;
		case EM_CWMP_DOWNLOAD_START_TIME:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->_download_rsp.start_time, strlen(info->_download_rsp.start_time));
			break;
		case EM_CWMP_DOWNLOAD_COMPLETE_TIME:
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->_download_rsp.complete_time, strlen(info->_download_rsp.complete_time));
			break;
		case EM_CWMP_METHODS:
			memset(str, 0, sizeof(str));
			for (i = 0; i < info->method_num; i++) {
				if (i != info->method_num - 1)
					strcat(str, ",");

				strcat(str, info->methods[i]);
			}
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
			break;
		case EM_CWMP_SET_PARAM_NAMES:
			memset(str, 0, sizeof(str));
			for (i = 0; i < info->set_param_num; i++) {
				if (i != info->set_param_num - 1)
					strcat(str, ",");

				strcat(str, info->_set_param_values[i].name);
			}
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
			break;
		case EM_CWMP_SET_PARAM_VALUES:
			memset(str, 0, sizeof(str));
			for (i = 0; i < info->set_param_num; i++) {
				if (i != info->set_param_num - 1)
					strcat(str, ",");

				strcat(str, info->_set_param_values[i].value);
			}
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
			break;
		case EM_CWMP_GET_PARAM_NAMES:
			memset(str, 0, sizeof(str));
			for (i = 0; i < info->get_param_num; i++) {
				if (i != info->get_param_num - 1)
					strcat(str, ",");

				strcat(str, info->_get_param_values[i].name);
			}
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
			break;
		case EM_CWMP_GET_PARAM_VALUES:
			memset(str, 0, sizeof(str));
			for (i = 0; i < info->get_param_num; i++) {
				if (i != info->get_param_num - 1)
					strcat(str, ",");

				strcat(str, info->_get_param_values[i].value);
			}
			write_one_str_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, str, strlen(str));
			break;
		default:
			write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
			break;
		}
	}


	log_ptr->thread_id = flow->thread_id;
	log_ptr->log_type = TBL_LOG_CWMP;
	log_ptr->log_len = idx;
	log_ptr->content_len = 0;
	log_ptr->content_ptr = NULL;
	log_ptr->flow = flow;
	log_ptr->proto_id = PROTOCOL_CWMP;

    //printf("show dissect_cwmp\n");
    //record_show(log_ptr->record);

	if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
		rte_mempool_put(tbl_log_mempool, (void*)log_ptr);
	}

	return 1;
}

static const uint8_t* get_xml(const uint8_t *payload, const uint16_t payload_len, uint32_t *xml_legnth) {
	int type = 0;
	uint32_t a;
	int line_len = 0;
	int header_len = 0;
	uint32_t end = payload_len - 1;
	const uint8_t *line_ptr = payload;
	const uint8_t *value_start = NULL;
	char *_header = NULL;
	struct header_value *_value = NULL;
	int type_start = 0;
	uint8_t parsed_lines = 0;
    int offset = 0;

	if ((payload_len == 0) || (payload == NULL) || (end == 0))
		return NULL;

    const uint8_t *ContentType = (const uint8_t *)g_strstr_len((const gchar*)payload+offset, payload_len-offset, "Content-Type");
    if(NULL == ContentType)
    {
        return NULL;
    }
    offset = ContentType - payload;

    const uint8_t *textXml = (const uint8_t *)g_strstr_len((const gchar*)payload+offset, payload_len-offset, "/xml");
    if(NULL == textXml)
    {
        return NULL;
    }
    offset = textXml - payload;

    const uint8_t *crlf = (const uint8_t *)g_strstr_len((const gchar*)payload+offset, payload_len-offset, "\x0d\x0a\x0d\x0a");
    if(NULL == crlf)
    {
        return NULL;
    }
    offset = crlf - payload + 4;
    *xml_legnth = payload_len - offset;
    return crlf + 4;
}

int dissect_cwmp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, uint32_t payload_len, uint8_t flag) {

	if (g_config.protocol_switch[PROTOCOL_CWMP] == 0)
		return 0;

    uint8_t         *xml_cxt;  // xml正文内容
    uint32_t        xml_len;

    struct cwmp_info info;
	memset(&info, 0, sizeof(struct cwmp_info));

	flow->match_data_len = payload_len;

	info._download_rsp.status = -1;  // default
#if 1
    xml_cxt = (uint8_t *)get_xml(payload, payload_len, &xml_len);

    if(xml_len > 0 && xml_cxt)
    {
        xml_handle_messages(xml_cxt, xml_len, &info);
    }

#else
	xml_handle_messages(payload, payload_len, &info);
#endif
	write_cwmp_log(flow, direction, &info);


	return 0;
}

static struct {const char *method; int len;} HTTP_METHOD[] =
{
    {"GET ",     4},
    {"POST ",    5},
    {"OPTIONS ", 8},
    {"HEAD ",    5},
    {"PUT ",     4},
    {"DELETE ",  7},
    {"CONNECT ", 8},
    {"PROPFIND ",9},
    {"REPORT ",  7},
    {NULL,       0}
};

static inline void _free_header(gpointer data)
{
    free(data);
}

static inline void _free_key_value(gpointer data)
{
    struct header_tmp_value *_value = (struct header_tmp_value *)data;

    if (_value->need_free)
        dpi_free((void *)_value->ptr);
    dpi_free(data);
}

static void _find_hash_write_log_delete(struct http_info *line_info, const char *header_name, struct tbl_log *log_ptr, int *idx)
{
    gpointer _value;
    struct header_value *value;

    if(line_info->table==NULL){
         write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
         return ;
    }

    _value = g_hash_table_lookup(line_info->table, header_name);
    value = (struct header_value *)_value;
    if (!value)
        write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
    else
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (const char *)value->ptr, value->len);
    g_hash_table_remove(line_info->table, header_name);
}


/* 0-没命中规则，1-命中规则，是想要的数据写tbl */
static int _http_content_type_filter(char *buf)
{
    if(g_config.http.num<=0){
        return 1;
    }
    regmatch_t pmatch[1];
    const size_t nmatch = 1;
    int status=0;
    int i=0;
    for(i=0;i<g_config.http.num;i++){
        status=regexec(&g_config.http.reg[i],buf,nmatch,pmatch,0);
        if(0==status){
            return 1;
        }
    }

    return 0;
}

/*
* 解析http头部的每一行头域，寻找空行位置
*/
static void parse_http_line_info(const uint8_t *payload, const uint32_t payload_len, struct http_info *line_info)
{
    uint32_t a;
    int             line_len    = 0;
    int             header_len  = 0;
    uint8_t        parsed_lines = 0;
    uint32_t                end = payload_len - 1;
    const uint8_t     *line_ptr = payload;
    char               *_header = NULL;
    struct header_value *_value = NULL;


    if ((payload_len == 0) || (payload == NULL) || (end == 0))
        return;

    line_info->table = g_hash_table_new_full(g_str_hash, g_str_equal, _free_header, _free_key_value);
    if (line_info->table == NULL) {
        DPI_LOG(DPI_LOG_DEBUG, "ghash table create error");
        return;
    }

    for (a = 0; a < end; a++) {
        if (get_uint16_t(payload, a) == ntohs(0x0d0a)) {
            line_len = (uint32_t)(((unsigned long)&payload[a]) - ((unsigned long)line_ptr));

            if (line_len == 0) {
                line_info->empty_line_position = a;
                break;
            }

            _value = (struct header_value *)dpi_malloc(sizeof(struct header_value));
            if (!_value) {
                DPI_LOG(DPI_LOG_WARNING, "malloc failed");
                goto next_line;
            }
            _value->need_free = 0;

            if (parsed_lines == 0) {
                _header = strdup("head_line");
                if (!_header) {
                    DPI_LOG(DPI_LOG_WARNING, "malloc failed");
                    dpi_free(_value);
                    goto next_line;
                }
                _value->len = line_len;
                _value->ptr = line_ptr;

                g_hash_table_insert(line_info->table, _header, _value);
            } else {
                header_len = find_special_char(line_ptr, line_len, ':');
                if (header_len <= 0) {
                    dpi_free(_value);
                    DPI_LOG(DPI_LOG_DEBUG, "no ':' in header line or index 0 is ':'");
                    goto next_line;
                }
                if (line_ptr[header_len - 1] == ' ')
                    _header = strndup((const char *)line_ptr, header_len - 1);
                else
                    _header = strndup((const char *)line_ptr, header_len);

                strdown_inplace(_header);

                if (header_len + 1 >= line_len) {
                    DPI_LOG(DPI_LOG_DEBUG, "no value in header line");
                    free(_header);
                    dpi_free(_value);
                    goto next_line;
                }

                if (line_ptr[header_len + 1] == ' ') {
                    _value->len = line_len - header_len - 2;
                    _value->ptr = line_ptr + header_len + 2;
                } else {
                    _value->len = line_len - header_len - 1;
                    _value->ptr = line_ptr + header_len + 1;
                }
                g_hash_table_insert(line_info->table, _header, _value);
            }

next_line:
            parsed_lines++;
            line_ptr = &payload[a + 2];
            line_len = 0;

            if((a + 2) >= payload_len)
                break;

            a++; /* next char in the payload */
        }
    }
}

static void _get_http_client_server_port(struct flow_info *flow, const uint8_t *payload, uint32_t payload_len)
{
    if(payload==NULL || payload_len<7){
        return;
    }
    if (payload_len >= 7 && strncasecmp((const char *)payload, "HTTP/1.", 7) == 0) {
        //s2c
        flow->drt_port_src[FLOW_DIR_DST2SRC]=flow->port_src;
        flow->drt_port_dst[FLOW_DIR_DST2SRC]=flow->port_dst;
    }
    else if (payload_len >= 10) {
        for(int i = 0; HTTP_METHOD[i].method; i++){
            if(0 == strncasecmp((const char*)payload, HTTP_METHOD[i].method, HTTP_METHOD[i].len)){
                //c2s
                flow->drt_port_src[FLOW_DIR_SRC2DST]=flow->port_src;
                flow->drt_port_dst[FLOW_DIR_SRC2DST]=flow->port_dst;
                break;
            }
        }
    }
}

static int http_change_and_account_gbk(uint8_t *pData, int len)
{
    int loop = len;
    uint8_t *p = pData;
    while(loop > 0)
    {
        if('\r'== *p|| '\n'== *p || '\t' == *p || '\b' == *p|| '\f' == *p|| '\v' == *p)
        {
            *p++ = ' ';
            loop--;
            continue;
        }
        else if(isprint(*p) > 0)
        {
            p++;
            loop--;
            continue;
        }

        /* 参考: 中华人民共和国信息技术标准化技术委员会 汉字内码扩展规范(GBK) */
        /* GBK/3 CJK 汉字区 6080个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0X81) && ((unsigned char)p[0] <= 0XA0)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/1 GB2312符号,增补符号 717 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA1) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/5 扩充符号 166 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XA8) && ((unsigned char)p[0] <= 0XA9)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/4 CJK汉字和增补汉字 8160 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XAA) && ((unsigned char)p[0] <= 0XFE)
                   && ((unsigned char)p[1] >= 0X40) && ((unsigned char)p[1] <= 0XA0) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        /* GBK/2 GB2312汉字 6763 个 */
        else if(loop>=2 && ((unsigned char)p[0] >= 0XB0) && ((unsigned char)p[0] <= 0XF7)
                   && ((unsigned char)p[1] >= 0XA1) && ((unsigned char)p[1] <= 0XFE) )
        {

            p = p + 2;
            loop = loop - 2;
            continue;
        }

        return p - pData;/* 这不是 GBK 编码*/
    }
    return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}
//add by xuxn 统计http解析post的总次数、成功率、失败率
extern volatile uint64_t post_total;    //http 解析.POST文件 总记录条数
extern volatile uint64_t post_success;    //http 解析.POST文件 解析成功的记录条数

#if ENABLE_OLD
static int http_field_element(struct tbl_log *log_ptr,struct flow_info *flow, int direction, struct http_info *line_info, int *idx, int i)
{
    //int local_idx=*idx;
    struct http_session *session = (struct http_session *)flow->app_session;
    switch(i){
    case EM_HTTP_METHOD:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (const char *)line_info->PROTOCOL_VAL_PTR(method), line_info->PROTOCOL_VAL_LEN(method));
        break;
    case EM_HTTP_URI:
        if(session->uri!=NULL){
            write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, session->uri, strlen(session->uri));
        }else{
            write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        }
        break;
    case EM_HTTP_VERSION:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (const char *)line_info->PROTOCOL_VAL_PTR(version), line_info->PROTOCOL_VAL_LEN(version));
        break;
    case EM_HTTP_STATUS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (const char *)line_info->PROTOCOL_VAL_PTR(code), line_info->PROTOCOL_VAL_LEN(code));
        break;
    case EM_HTTP_RESPONSESTATUS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (const char *)line_info->PROTOCOL_VAL_PTR(response_code), line_info->PROTOCOL_VAL_LEN(response_code));
        break;
    case EM_CWMP_CACHE_CONTROL:
        _find_hash_write_log_delete(line_info, "cache-control", log_ptr, idx);
        break;
    case EM_HTTP_CONNECTION:
        _find_hash_write_log_delete(line_info, "connection", log_ptr, idx);
        break;
    case EM_HTTP_COOKIE:
        _find_hash_write_log_delete(line_info, "cookie", log_ptr, idx);
        break;
    case EM_HTTP_COOKIE2:
        _find_hash_write_log_delete(line_info, "cookie2", log_ptr, idx);
        break;
    case EM_HTTP_DATE:
        _find_hash_write_log_delete(line_info, "date", log_ptr, idx);
        break;
    case EM_HTTP_PRAGMA:
        _find_hash_write_log_delete(line_info, "pragma", log_ptr, idx);
        break;
    case EM_HTTP_TRAILER:
        _find_hash_write_log_delete(line_info, "trailer", log_ptr, idx);
        break;
    case EM_HTTP_TRANSFER_ENCODING:
        _find_hash_write_log_delete(line_info, "transfer-encoding", log_ptr, idx);
        break;
    case EM_HTTP_UPGRADE:
        _find_hash_write_log_delete(line_info, "upgrade", log_ptr, idx);
        break;
    case EM_HTTP_VIA:
        _find_hash_write_log_delete(line_info, "via", log_ptr, idx);
        break;
    case EM_HTTP_WARNING:
        _find_hash_write_log_delete(line_info, "warning", log_ptr, idx);
        break;
    case EM_HTTP_ACCEPT:
        _find_hash_write_log_delete(line_info, "accept", log_ptr, idx);
        break;
    case EM_HTTP_ACCEPT_CHARSET:
        _find_hash_write_log_delete(line_info, "accept-charset", log_ptr, idx);
        break;
    case EM_HTTP_ACCEPT_ENCODING:
        _find_hash_write_log_delete(line_info, "accept-encoding", log_ptr, idx);
        break;
    case EM_HTTP_ACCEPT_LANGUAGE:
        _find_hash_write_log_delete(line_info, "accept-language", log_ptr, idx);
        break;
    case EM_HTTP_AUTHORIZATION:
        _find_hash_write_log_delete(line_info, "authorization", log_ptr, idx);
        break;
    case EM_HTTP_EXPECT:
        _find_hash_write_log_delete(line_info, "expect", log_ptr, idx);
        break;
    case EM_HTTP_FROM:
        _find_hash_write_log_delete(line_info, "from", log_ptr, idx);
        break;
    case EM_HTTP_HOST:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, session->host, strlen(session->host));
        break;
    case EM_HTTP_IF_MATCH:
        _find_hash_write_log_delete(line_info, "if-match", log_ptr, idx);
        break;
    case EM_HTTP_IF_MODIFIED_SINCE:
        _find_hash_write_log_delete(line_info, "if-modified-since", log_ptr, idx);
        break;
    case EM_HTTP_IF_NONE_MATCH:
        _find_hash_write_log_delete(line_info, "if-none-match", log_ptr, idx);
        break;
    case EM_HTTP_IF_RANGE:
        _find_hash_write_log_delete(line_info, "if-range", log_ptr, idx);
        break;
    case EM_HTTP_IF_UNMODIFIED_SINCE:
        _find_hash_write_log_delete(line_info, "if-unmodified-since", log_ptr, idx);
        break;
    case EM_HTTP_MAX_FORWARDS:
        _find_hash_write_log_delete(line_info, "max-forwards", log_ptr, idx);
        break;
    case EM_HTTP_PROXY_AUTHORIZATION:
        _find_hash_write_log_delete(line_info, "proxy-authorization", log_ptr, idx);
        break;
    case EM_HTTP_PROXY_TYPE:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, line_info->proxy_type, strlen(line_info->proxy_type));
        break;
    case EM_HTTP_PROXY_LOGIN:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, line_info->proxy_login, strlen(line_info->proxy_login));
        break;
    case EM_HTTP_PROXY_AUTHORIZATION_INFO:
        _find_hash_write_log_delete(line_info, "proxy-authentication-info", log_ptr, idx); //这个字段名称写错了,应该是 proxy-authentication-info 而不是 proxy-authorization-info
        break;
    case EM_HTTP_RANGE:
        _find_hash_write_log_delete(line_info, "range", log_ptr, idx);
        break;
    case EM_HTTP_REFERER:
        _find_hash_write_log_delete(line_info, "referer", log_ptr, idx);
        break;
    case EM_HTTP_TE:
        _find_hash_write_log_delete(line_info, "te", log_ptr, idx);
        break;
    case EM_HTTP_USER_AGENT:
        {
            if(line_info->table==NULL){
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
                break;
            }
            gpointer _value;
            struct header_value *value;
            _value = g_hash_table_lookup(line_info->table, "user-agent");
            value = (struct header_value *)_value;
            if (!value)
                write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
            else
            {
                int i = 0, j = 0;
                char tmp[value->len*2 + 1];
                memset(tmp, 0, value->len*2 + 1);
                for(i=0; i < value->len && j < value->len*2 + 1; i++)
                {
                    if(!isprint(value->ptr[i]))
                    {
                        sprintf(tmp+j, "%2x", value->ptr[i]);
                        j++;
                    }
                    else
                        tmp[j] = value->ptr[i];
                    j++;
                }
                write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, (const char *)tmp, strlen(tmp));
            }
            g_hash_table_remove(line_info->table, "user-agent");
        }
        break;
    case EM_HTTP_PROXY_PROXYAGENT:
        _find_hash_write_log_delete(line_info, "proxy-agent", log_ptr, idx);
        break;
    case EM_HTTP_USER_CPU:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, line_info->uaCPU, strlen(line_info->uaCPU));
        break;
    case EM_HTTP_USER_OS:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, line_info->uaOS, strlen(line_info->uaOS));
        break;

    case EM_HTTP_ACCEPT_RANGES:
        _find_hash_write_log_delete(line_info, "accept-ranges", log_ptr, idx);
        break;
    case EM_HTTP_AGE:
        _find_hash_write_log_delete(line_info, "age", log_ptr, idx);
        break;
    case EM_HTTP_ETAG:
        _find_hash_write_log_delete(line_info, "etag", log_ptr, idx);
        break;
    case EM_HTTP_LOCATION:
        _find_hash_write_log_delete(line_info, "location", log_ptr, idx);
        break;
    case EM_HTTP_PROXY_AUTHENTICATE:
        _find_hash_write_log_delete(line_info, "proxy-authenticate", log_ptr, idx);
        break;
    case EM_HTTP_INQUIRY_TYPE:
        write_one_str_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, line_info->inquiry_type, strlen(line_info->inquiry_type));
        break;
    case EM_HTTP_PROXY_CONNECT_HOST:
        _find_hash_write_log_delete(line_info, "proxy_connect_host", log_ptr, idx);
        break;
    case EM_HTTP_PROXY_CONNECT_PORT:
        _find_hash_write_log_delete(line_info, "proxy_connect_port", log_ptr, idx);
        break;
    case EM_HTTP_RETRY_AFTER:
        _find_hash_write_log_delete(line_info, "retry-after", log_ptr, idx);
        break;
    case EM_HTTP_SERVER:
        _find_hash_write_log_delete(line_info, "server", log_ptr, idx);
        break;
    case EM_HTTP_VARY:
        _find_hash_write_log_delete(line_info, "vary", log_ptr, idx);
        break;
    case EM_HTTP_WWW_AUTHENTICATE:
        _find_hash_write_log_delete(line_info, "www-authenticate", log_ptr, idx);
        break;
    case EM_HTTP_ALLOW:
        _find_hash_write_log_delete(line_info, "allow", log_ptr, idx);
        break;
    case EM_HTTP_CONTENT_ENCODING:
        _find_hash_write_log_delete(line_info, "content-encoding", log_ptr, idx);
        break;
    case EM_HTTP_CONTENT_LANGUAGE:
        _find_hash_write_log_delete(line_info, "content-language", log_ptr, idx);
        break;
    //case EM_HTTP_CONTENT_LENGTH:
    //    _find_hash_write_log_delete(line_info, "content-length", log_ptr, idx);
    //    break;
    case EM_HTTP_CONTENT_LOCATION:
        _find_hash_write_log_delete(line_info, "content-location", log_ptr, idx);
        break;
    case EM_HTTP_CONTENT_MD5:
        _find_hash_write_log_delete(line_info, "content-md5", log_ptr, idx);
        break;
    case EM_HTTP_CONTENT_RANGE:
        _find_hash_write_log_delete(line_info, "content-range", log_ptr, idx);
        break;
    //case EM_HTTP_CONTENT_TYPE:
    //    _find_hash_write_log_delete(line_info, "content-type", log_ptr, idx);
    //    break;
    case EM_HTTP_EXPIRES:
        _find_hash_write_log_delete(line_info, "expires", log_ptr, idx);
        break;
    case EM_HTTP_LAST_MODIFIED:
        _find_hash_write_log_delete(line_info, "last-modified", log_ptr, idx);
        break;
    case EM_HTTP_X_FORWARDED_FOR:
        _find_hash_write_log_delete(line_info, "x-forwarded-for", log_ptr, idx);
        break;
    case EM_HTTP_SET_COOKIE:
        _find_hash_write_log_delete(line_info, "set-cookie", log_ptr, idx);
        break;
    case EM_HTTP_SET_COOKIE2:
        _find_hash_write_log_delete(line_info, "set-cookie2", log_ptr, idx);
        break;
    //EM_HTTP_DNT,
    //EM_HTTP_X_POWERED_BY,
    //EM_HTTP_P3P,
    //EM_HTTP_CLIENT_HEAD,
    //EM_HTTP_CLIENT_BODY,
    //EM_HTTP_SERVER_HEAD,
    //EM_HTTP_GET_SVR_REQ_URL,    //客户端获取服务器请求的 URL
    //EM_HTTP_ENTITY_TITLE_HEAD,    //实体标题首部
    //EM_HTTP_H_A_NUMBER,
    //EM_HTTP_H_X_NUMBER,
    //EM_HTTP_APPTYPE,
    case EM_HTTP_X_HTTP_TOKEN:
        _find_hash_write_log_delete(line_info, "x-http-token", log_ptr, idx);
        break;
    case EM_HTTP_X_COOKIE_TOKEN:
        _find_hash_write_log_delete(line_info, "x-cookie-token", log_ptr, idx);
        break;
    default:
        write_n_empty_reconds(log_ptr->record, idx, TBL_LOG_MAX_LEN, 1);
        break;
    }

    return 0;
}
#endif
/*************************************************************************************************/

#define CHUNK_SIZE_MAX 32

static void http_parser_chunked_content(const uint8_t *chunked_content, uint32_t chunked_content_len,
        uint8_t *content, uint32_t *content_len)
{
    uint32_t offset = 0;
    int linelen;
    uint32_t max_len = *content_len;
    char chunk_string[CHUNK_SIZE_MAX];
    uint32_t chunk_size;

    *content_len = 0;

    while (offset < chunked_content_len) {
        linelen = find_packet_line_end(chunked_content + offset, chunked_content_len - offset);
        if (linelen <= 0 || linelen >= CHUNK_SIZE_MAX) {
            /* Can't get the chunk size line */
            break;
        }

        strncpy(chunk_string, (const char *)chunked_content + offset, linelen);
        chunk_string[linelen] = 0;
        chunk_size = strtol(chunk_string, NULL, 16);

        offset += linelen + 2;
        if (chunk_size > chunked_content_len - offset) {
            chunk_size = chunked_content_len - offset;
        }

        if (*content_len + chunk_size < max_len) {
            memcpy(content + *content_len, chunked_content + offset, chunk_size);
            *content_len += chunk_size;
        }

        offset += chunk_size + 2;
        if (offset >= chunked_content_len)
            break;

        if (chunk_size == 0)
            break;
    }

    return;
}

#define GET_DEV_INFO(devArr, arrlen, devFlag)    do{                                    \
    char* tmp0 = strcasestr((char *)value->ptr, (devFlag));                            \
    if(NULL != tmp0)                                                                \
    {                                                                                \
        char* tmp1 = strstr(tmp0, ";");                                                \
        char* tmp2 = strstr(tmp0, ")");                                                \
        if(NULL != tmp2 && (tmp1 == NULL || tmp1 > tmp2))                            \
            memcpy((devArr), tmp0, (tmp2-tmp0<(arrlen) ? tmp2-tmp0:(arrlen)-1));    \
        else if(NULL != tmp2 && NULL != tmp1 && tmp1 < tmp2)                        \
            memcpy((devArr), tmp0, (tmp1-tmp0<(arrlen) ? tmp1-tmp0:(arrlen)-1));    \
        else if(NULL == tmp1 && NULL == tmp2)                                        \
        {                                                                            \
            tmp1 = strstr(tmp0,"\r\n");                                                \
            if(NULL != tmp1)                                                        \
                memcpy((devArr), tmp0, (tmp1-tmp0<(arrlen) ? tmp1-tmp0:(arrlen)-1));\
        }                                                                            \
    }                                                                                \
}while(0)

#if ENABLE_OLD
size_t ws_base64_decode_inplace(char *s)
{
    static const char b64[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\r\n";
    int bit_offset, byte_offset, idx, i;
    unsigned char *d = (unsigned char *)s;
    char *p;
    int  cr_idx;
    // we will allow CR and LF - but ignore them
    cr_idx = (int)(strchr(b64, '\r') - b64);
    i = 0;
    while (*s && (p = strchr(b64, *s))) {
        idx = (int)(p - b64);
        if (idx < cr_idx) {
            byte_offset = (i * 6) / 8;
            bit_offset = (i * 6) % 8;
            d[byte_offset] &= ~((1 << (8 - bit_offset)) - 1);
            if (bit_offset < 3) {
                d[byte_offset] |= (idx << (2 - bit_offset));
            } else {
                d[byte_offset] |= (idx >> (bit_offset - 2));
                d[byte_offset + 1] = 0;
                d[byte_offset + 1] |= (idx << (8 - (bit_offset - 2))) & 0xFF;
            }
            i++;
        }
        s++;
    }
    d[i*3/4] = 0;
    return i*3/4;
}

static void dissect_http_authorization(struct http_info *line_info)
{
    struct header_value *value = (struct header_value *)g_hash_table_lookup(line_info->table, "proxy-authorization");
    if(!value)
        value = (struct header_value *)g_hash_table_lookup(line_info->table, "authorization");
    if(value)
    {
        const uint8_t *tmp = memchr(value->ptr, ' ', value->len);
        if(tmp)
        {
            int len = tmp - value->ptr;
            memcpy(line_info->proxy_type,  value->ptr, DPI_MIN(len, TYPE_LEN-1));
            if(strncasecmp(line_info->proxy_type, "Basic", 5)  == 0){
                memcpy(line_info->proxy_login, tmp + 1,    DPI_MIN(value->len - len - 1, LOGIN_LEN-1));
                ws_base64_decode_inplace(line_info->proxy_login);
            }
        }
    }
}

static void dissect_http_authenticate(struct http_info *line_info)
{
   //质询认证方案："basic" 的截取
   struct header_value *value = (struct header_value *)g_hash_table_lookup(line_info->table, "www-authenticate");
   if(!value)
           value = g_hash_table_lookup(line_info->table, "proxy-authenticate");
   if(value)
   {
       const uint8_t *tmp = memchr(value->ptr, ' ', value->len);
       if(tmp)
       {
           int len = tmp - value->ptr;
           memcpy(line_info->inquiry_type, value->ptr, len >= TYPE_LEN ? TYPE_LEN - 1 : len);
       }
   }
}

static void dissect_http_user_agent(struct http_info *line_info)
{
    struct header_value *value = (struct header_value *)g_hash_table_lookup(line_info->table, "user-agent");
    if(!value)
        return;

    //GET_DEV_INFO(line_info->uaCPU, CPU_LEN, "cpu");
    char* tmp0 = strcasestr((const char *)value->ptr, "cpu");
    if(NULL != tmp0)
    {
        char* tmp1 = strstr(tmp0, ";");
        char* tmp2 = strstr(tmp0, ")");
        if(NULL != tmp2 && (tmp1 == NULL || tmp1 > tmp2))
            memcpy(line_info->uaCPU, tmp0, (tmp2-tmp0<CPU_LEN ? tmp2-tmp0:CPU_LEN-1));
        else if(NULL != tmp2 && NULL != tmp1 && tmp1 < tmp2)
            memcpy(line_info->uaCPU, tmp0, (tmp1-tmp0<CPU_LEN ? tmp1-tmp0:CPU_LEN-1));
        else if(NULL == tmp1 && NULL == tmp2)
        {
            tmp1 = strstr(tmp0,"\r\n");
            if(NULL != tmp1)
                memcpy(line_info->uaCPU, tmp0, (tmp1-tmp0<CPU_LEN ? tmp1-tmp0:CPU_LEN-1));
        }
    }

    //该数组元素为设备系统标识,最后一个元素必须是NULL, 如果新增系统标识,则直接在后面的NULL位置上依次添加
    //注意：此处如果有os标识，则os后面必须加一个空格
    const char *devFlags[] = {"linux", "windows ", "windows/", "iphone ", "android", "mac", NULL, NULL, NULL, NULL};
    int i = 0;
    for(i=0; devFlags[i]; i++)
    {
        //GET_DEV_INFO(line_info->uaOS, OS_LEN, devFlags[i]);
        char* tmp0 = strcasestr((const char *)value->ptr, devFlags[i]);
        if(tmp0)
        {
            char* tmp1 = strstr(tmp0, ";");
            char* tmp2 = strstr(tmp0, ")");
            if(NULL != tmp2 && (tmp1 == NULL || tmp1 > tmp2)){
                memcpy(line_info->uaOS, tmp0, (tmp2-tmp0<OS_LEN ? tmp2-tmp0:OS_LEN-1));
            }
            else if(NULL != tmp2 && NULL != tmp1 && tmp1 < tmp2){
                memcpy(line_info->uaOS, tmp0, (tmp1-tmp0<OS_LEN ? tmp1-tmp0:OS_LEN-1));
            }
            else if(NULL == tmp1 && NULL == tmp2){
                tmp1 = strstr(tmp0,"\r\n");
                if(NULL != tmp1)
                    memcpy(line_info->uaOS, tmp0, (tmp1-tmp0<OS_LEN ? tmp1-tmp0:OS_LEN-1));
            }
            if(line_info->uaOS[0])
                break;
        }
    }
}

static void get_http_host(struct http_info *line_info, struct http_session *session)
{
    //缓存host字段到session中
    struct header_value *value = (struct header_value *)g_hash_table_lookup(line_info->table, "host");
    if (value)
    {
        size_t copy_len = value->len >= sizeof(session->host) ? sizeof(session->host) - 1 : value->len;
        memcpy(session->host, value->ptr, copy_len);
        session->host[copy_len] = 0;
        //g_hash_table_remove(line_info->table, "host");

        //雅虎认证
        if(copy_len > 9 && strcmp(session->host + copy_len - 9, "yahoo.com") == 0){
            value = g_hash_table_lookup(line_info->table, "head_line");
            if(!memchr(value->ptr, '?', value->len))
                return;
            const uint8_t *login  = memstr(value->ptr, "login=", value->len);
            const uint8_t *passwd = memstr(value->ptr, "passwd=", value->len);
            if(login && passwd)
            {
                uint8_t *login_end  = memchr(login, '&', value->len);
                uint8_t *passwd_end = memchr(passwd, '&', value->len);
                if(login_end && passwd_end){ //FORMAT   login:passwd
                    strcpy(line_info->proxy_type, "Yahoo");
                    uint8_t off = DPI_MIN(login_end - login - 6, LOGIN_LEN - 1);
                    memcpy(line_info->proxy_login, login+6, off);
                    if(off < LOGIN_LEN - 1)
                        line_info->proxy_login[off++] = ':';
                    if(off + passwd_end - passwd - 7 < LOGIN_LEN - 1)
                        memcpy(line_info->proxy_login + off, passwd + 7, passwd_end - passwd - 7);
                }
            }
        }
    }
}


static void get_http_uri(struct http_info *line_info, struct http_session *session)
{
    if(session==NULL){
        return;
    }
    if(line_info->PROTOCOL_VAL_LEN(uri)==0){
        return;
    }

    size_t copy_len=line_info->PROTOCOL_VAL_LEN(uri);

    if(session->uri!=NULL){
        free(session->uri);
        session->uri=NULL;
    }

    session->uri=malloc(copy_len+1);
    if(session->uri==NULL){
        DPI_LOG(DPI_LOG_ERROR,"malloc failed!");
        return;
    }
    memcpy(session->uri, line_info->PROTOCOL_VAL_PTR(uri), copy_len);
    session->uri[copy_len] = 0;
}

#endif
static int dissect_http_sctp(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, uint32_t payload_len, uint8_t flag)
{
    if (!flow->app_session)
    {
        flow->app_session = dpi_malloc(sizeof(struct http_session));
        if (!flow->app_session)
            return PKT_DROP;
        memset(flow->app_session, 0, sizeof(struct http_session));
    }
	dissect_cwmp(flow, direction, seq, payload, payload_len, flag);
    return 0;
}

#define HTTP_NEED_MORE  0
#define HTTP_ERROR     -1
static int has_chunked(const char*p, int l)
{
    const char *find_str[] = {"\x0D\x0A\x0D\x0A", "chunked", "Transfer-Encoding", "HTTP/1.", NULL};
    const char *find       = p + l;
    int         i;

    for(i = 0; NULL != find_str[i]; i++)
    {
        find = memmem(p, find -p, find_str[i],  strlen(find_str[i]));
        if(NULL == find)
        {
            return 0;
        }
    }
    return 1;
}

static int64_t get_chunkedlen(const char *http, int http_len)
{
    int64_t      offset      = 0;
    int64_t      chunk_size  = 0;
    int64_t      chunk_len   = 0;
    const char   *str_split   = "\x0D\x0A\x0D\x0A";
    const char   *p           = NULL;

    if(0 == has_chunked(http, http_len))
    {
        return -1;  // no chunk
    }

    // skip http head
    p = memmem(http, http_len, str_split,  strlen(str_split));
    if(NULL == p)
    {
        return 0;
    }

    offset    = p - http + strlen(str_split);
    http     += offset;
    http_len -= offset;
    offset    = 0;

    while (offset < http_len)
    {
        chunk_len = find_packet_line_end((const uint8_t*)http + offset, (uint32_t)http_len - offset);
        if (chunk_len <= 0)
        {
            break;
        }

        chunk_size = strtol(http + offset, NULL, 16);
        if(chunk_size > (1024*1024*10) || chunk_size < 0) // 限定每个chunk_size
        {
            return -1;  // no chunk
        }

        offset    += (chunk_len + 2 + chunk_size + 2);  // point next chunked
        if (offset  > http_len)
        {
            break;
        }
        else
        if (0 == chunk_size && offset == http_len)
        {
            return http_len;
        }
    }
    return HTTP_NEED_MORE;
}

// 返回 Content-Length 数值类型
static int64_t get_contentlen(const char *p, int l)
{
#define CONTENT_STR_LEN 16
    const char *str_start   = "\r\nContent-Length";
    const char *str_end     = "\r\n";

    if(l < CONTENT_STR_LEN)
        return 0;

    const char *find_start = memmem(p, l, str_start,  CONTENT_STR_LEN);
    if(find_start)
        find_start += CONTENT_STR_LEN;
    else
        return 0;

    const char *find_end = memmem(find_start, l-(find_start-p), str_end,  strlen(str_end));
    if(find_end == NULL || find_start + 15 < find_end)  //0xffffffff = 4294967295 = 4G
        return 0;

    int  i = 0;
    char buff[16];
    while(find_start < find_end)
    {
        if(isdigit(*find_start))
            buff[i++] =  *find_start;
        else if(*find_start != ':' && *find_start != ' ')
            return 0;
        find_start++;
    }
    buff[i] = 0;

    return atol(buff);
}

/*
*  0: not http
*  1: is  http
*/
static int is_http(const char *p, int l)
{
    int ST     ;
    int offset ;
    int i      ;
    int find   ;

    enum
    {
        HTTP_ST_DEFAULT,
        HTTP_ST_REQUEST,
        HTTP_ST_REQUEST_METHOD,
        HTTP_ST_REQUEST_URI,
        HTTP_ST_REQUEST_VERSION,
        HTTP_ST_RESPONSE,
        HTTP_ST_RESPONSE_VERSION,
        HTTP_ST_RESPONSE_NUM,
        HTTP_ST_END,
    };

    if(l <= 0)
    {
        return 0;
    }

    ST     = HTTP_ST_DEFAULT;
    offset = 0;
    while(offset < l)
    {
        switch(ST)
        {
            case HTTP_ST_DEFAULT:
                if(0 == memcmp(p, "HTTP/1.", 7))
                {
                    ST = HTTP_ST_RESPONSE;
                }
                else
                {
                    ST = HTTP_ST_REQUEST;
                }
                break;

            case HTTP_ST_REQUEST:
                ST = HTTP_ST_REQUEST_METHOD;
                break;

            case HTTP_ST_REQUEST_METHOD:
                find = 0;
                if(l - offset > 10){
                    for(i = 0; HTTP_METHOD[i].method; i++)
                    {
                        if(0 == strncasecmp(p + offset, HTTP_METHOD[i].method, HTTP_METHOD[i].len))
                        {
                            offset += HTTP_METHOD[i].len;
                            ST = HTTP_ST_END;
                            find = 1;
                            break;
                        }
                    }
                }
                if(0 == find)
                {
                    return 0;
                }
                break;

            case HTTP_ST_REQUEST_VERSION:
                if(0 == memcmp(p + offset, "HTTP/1.", strlen("HTTP/1.")))
                {
                    offset += strlen("HTTP/1.");
                    ST = HTTP_ST_END;
                }
                else
                {
                    return 0;
                }
                break;

            case HTTP_ST_RESPONSE:
                ST = HTTP_ST_RESPONSE_VERSION;
                break;

            case HTTP_ST_RESPONSE_VERSION:
                if(0 == memcmp(p + offset, "HTTP/1.", strlen("HTTP/1.")))
                {
                    offset += strlen("HTTP/1.");
                    offset ++;
                    offset ++;
                    ST = HTTP_ST_RESPONSE_NUM;
                }
                else
                {
                    return 0;
                }
                break;

            case HTTP_ST_RESPONSE_NUM:
                for(i = 0; i < 3; i++)
                {
                    if(0 == isdigit(p[offset + i]))
                    {
                        return 0;
                    }
                }
                ST = HTTP_ST_END;
                break;

            case HTTP_ST_END:
                return 1;
                break;
        }

    }
    return 0;
}


// 返回 HTTP head 长度范围
// 负数: 没有找到
static int64_t get_header_len(const char *p, int l)
{
    if(0 == is_http(p,l))
    {
        return HTTP_ERROR;
    }

    const char *find = memmem(p, l, "\x0D\x0A\x0D\x0A", 4);
    if(NULL == find)
    {
        return HTTP_NEED_MORE;
    }

    return  find - p + 4;
}


// 返回负数, 代表不存在一个完整HTTP 长度
// 返回正数, 代表  存在一个完整HTTP 长度
static int64_t get_http_len(const char *p, int len)
{
    const uint8_t *pstart   = NULL;
    const uint8_t *pend     = NULL;
    const uint8_t *data     = NULL;
    int64_t        remain   = 0;
    int64_t        success  = 0;
    int64_t        hl       = 0;  // header  len
    int64_t        cl       = 0;  // content len

    if(len < 0)
    {
        return -1;
    }

    hl = get_header_len(p, len);
    if(HTTP_NEED_MORE == hl)
    {
        return HTTP_NEED_MORE;
    }
    else if(HTTP_ERROR == hl)
    {
        return HTTP_ERROR;
    }

    cl = get_contentlen(p, hl);
    if(cl > 0)
    {
        return hl + cl;
    }

    cl = get_chunkedlen(p, len);
    if(cl > 0)
    {
        return hl + cl;
    }
    else if(cl == HTTP_NEED_MORE)
    {
        return HTTP_NEED_MORE;
    }

    return hl;
}

// 已缓存的数据直接输出
static int http_tcp_miss(struct flow_info *f, uint8_t C2S, uint32_t miss)
{
    struct   http_session *s     = NULL;
    struct   cwmp_cache   *c     = NULL;
    int      paddinglen  = 0;
    int      safelen     = 0;
    int      miss_len    = (int)miss;

    if(f->app_session)
    {
        s = (struct http_session *)f->app_session;
        c = s->cwmp_cache + C2S;

        // 如果允许 TCP PADDING
        if(c->addr && miss_len && g_config.http.tcp_padding_len > 0 && miss_len <= g_config.http.tcp_padding_len && c->cache_size - c->cache_hold > miss_len)
        {
            paddinglen = strlen(g_config.http.tcp_padding_str);
            while(miss_len > 0)
            {
                safelen = (paddinglen < miss_len) ? paddinglen : miss_len;
                memcpy(c->addr + c->cache_hold, g_config.http.tcp_padding_str, safelen);
                c->cache_hold += safelen;
                miss_len      -= safelen;
            }
        }
        else
        if(c->addr)
        {
			dissect_cwmp(f, C2S, 0, (const uint8_t*)c->addr, (uint32_t)c->cache_hold, 0);
            free(c->addr);
            c->addr      = NULL;
            c->cache_hold = 0;
        }
    }
    return 0;
}

// HTTP 专业切割机(解决 pipeline)
// 切出一个完整的 HTTP给解析接口(HTTP_HEAD + content)
static int dissect_http_strip(struct flow_info *f, uint8_t C2S, const uint8_t *p, uint32_t  pl)
{
    char                 t     = 0;
    int64_t              hl    = 0;
    int64_t              offset= 0;
    int64_t              l     = pl;
    struct http_session *s     = NULL;
    struct cwmp_cache   *c     = NULL;

    if (NULL == f->app_session)
    {
        f->app_session = dpi_malloc(sizeof(struct http_session));
        if (NULL == f->app_session)
        {
            goto HTTP_NEED_MORE_PKT;
        }
        memset(f->app_session, 0, sizeof(struct http_session));
    }
    s = (struct http_session *)f->app_session;
    c = s->cwmp_cache + C2S;

    // 是否开启缓存
    if(c->addr)
    {

        if(1 == is_http((const char*)p,l))
        {
            // 说明这是 HEAD 请求的响应
			dissect_cwmp(f, C2S, 0, (const uint8_t*)c->addr, (uint32_t)c->cache_hold, 0);
            c->cache_hold = 0; //reset
        }

        if(l >= (c->cache_size - c->cache_hold))
        {
            // 缓存撑爆前,  解析数据, 释放
			dissect_cwmp(f, C2S, 0, (const uint8_t*)c->addr, (uint32_t)c->cache_hold, 0);
            goto HTTP_DROP;
        }

        //正常 拼装
        memcpy(c->addr + c->cache_hold, p, l);
        c->cache_hold  += l;
        c->addr[c->cache_hold] = '\0';
        p = (const uint8_t*)c->addr;
        l = c->cache_hold;
    }

    // 专业切割机
    while(offset < l)
    {

        hl = get_http_len((const char*)p + offset, l - offset);
        if(hl > 0 && l - offset >= hl)
        {
			dissect_cwmp(f, C2S, 0, (const uint8_t*)p + offset, (uint32_t)hl, 0);
            offset += hl;
        }
        else if(hl == HTTP_ERROR)
        {
            goto HTTP_DROP;
        }
        else if(hl == HTTP_NEED_MORE)
        {
            break;
        }
        else if(hl > l - offset)
        {
            break;
        }
    }

    // 有没有剩料?
    if(offset >=0 && offset < l)
    {
        if(NULL != c->addr && offset >0)  //已开启缓存, 直接将剩料挪到前面
        {
            memmove(c->addr, c->addr + offset, c->cache_hold - offset);
            c->cache_hold  -= offset;
        }
        else if(NULL == c->addr)          //未开启缓存, 创建缓存, 把剩料放在前面
        {
            c->cache_size = g_config.http.http_strip_cache_size;
            c->cache_hold = l - offset;
            c->addr      = dpi_malloc(c->cache_size);
            memcpy(c->addr, p + offset, l - offset);
        }
        goto HTTP_NEED_MORE_PKT;
    }
    else
    {
        if(NULL != c->addr)
        {
            free(c->addr);
            c->addr      = NULL;
            c->cache_hold = 0;
        }
        goto HTTP_NEED_MORE_PKT;
    }


// HTTP  太长, 只解析被缓存的部分.
HTTP_DROP:
    if(NULL != c->addr)
    {
        free(c->addr);
        c->addr = NULL;
        c->cache_hold = 0;
    }

// HTTP 解析,需要更多报文
HTTP_NEED_MORE_PKT:
    return 0;
}

static void flow_cwmp_finish(struct flow_info *flow)
{
    if(flow->app_session)
    {
        struct cwmp_cache *c = NULL;
        struct http_session *session = (struct http_session *)flow->app_session;

        if(session->uri)
        {
            free(session->uri);
            session->uri = NULL;
        }

        c =  session->cwmp_cache + 0;
        if(c->addr)
        {
            dissect_cwmp(flow, 0, 0, (const uint8_t*)c->addr, (uint32_t)c->cache_hold, 0);
            free(session->cwmp_cache[0].addr);
            session->cwmp_cache[0].addr = NULL;
        }

        c =  session->cwmp_cache + 1;
        if(c->addr)
        {
            dissect_cwmp(flow, 1, 0, (const uint8_t*)c->addr, (uint32_t)c->cache_hold, 0);
            free(session->cwmp_cache[1].addr);
            session->cwmp_cache[1].addr = NULL;
        }

        free(flow->app_session);
        flow->app_session = NULL;
    }
}


static int identify_cwmp(struct flow_info *flow, uint8_t C2S,  const uint8_t *payload, const uint32_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_CWMP] == 0)
    {
        return PROTOCOL_UNKNOWN;
    }

    if (payload_len < 9)
    {
        return PROTOCOL_UNKNOWN;
    }

	uint32_t http_header_len = 0;

    // is HTTP?
    if (g_strstr_len((const gchar *)payload, payload_len, " HTTP/1.1\x0d\x0a"))
    {
		// 在识别http的基础上, 再次识别cwmp
        if (memmem(payload, payload_len, "xmlns:cwmp", 10))
        {
            flow->real_protocol_id = PROTOCOL_CWMP;
            return PROTOCOL_CWMP;
        }
    }

    return PROTOCOL_UNKNOWN;
}

extern struct decode_t decode_cwmp;
static int initial(struct decode_t *decode)
{
    decode_on_port_tcp (80,   &decode_cwmp);
    decode_on_port_tcp (8080, &decode_cwmp);
    decode_on_port_tcp (8000, &decode_cwmp);

    decode_on_port_sctp(80,   &decode_cwmp);
    decode_on_port_sctp(8080, &decode_cwmp);
    decode_on_port_sctp(8000, &decode_cwmp);

	dpi_register_proto_schema(cwmp_field_array, EM_CWMP_MAX, "cwmp");
	map_fields_info_register(cwmp_field_array, PROTOCOL_CWMP, EM_CWMP_MAX, "cwmp");
	register_tbl_array(TBL_LOG_CWMP, 0, "cwmp", NULL);
    return 0;
}

static int destroy(struct decode_t *decode)
{
    return 0;
}

struct decode_t decode_cwmp = {
    .name           =   "cwmp",
    .decode_initial =   initial,
    .pkt_identify   =   identify_cwmp,
    .pkt_dissect    =   dissect_http_strip,
    .pkt_miss       =   http_tcp_miss,
    .flow_finish    =   flow_cwmp_finish,
    .decode_destroy =   destroy,
};

