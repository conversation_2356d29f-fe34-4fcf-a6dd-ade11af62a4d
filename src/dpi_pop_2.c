/****************************************************************************************
 * 文 件 名 : dpi_pop.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/11
编码: wangy            2018/07/11
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include "dpi_email.h"

#include <yaemail/email.h>
#include <rte_mempool.h>
#include <arpa/inet.h>
#include <string.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_detect.h"
#include "dpi_dissector.h"
#include "dpi_conversation.h"
#include "dpi_utils.h"

#define POP_PORT 110
#define POP_UNKNOWN_LINE_NUM_MAX 20


extern char *email_heads[];
extern const char *email_cmds[];

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern dpi_field_table email_field_array[];

typedef enum {
    POP_STATE_READING_CMDS,              /* reading commands */
    POP_STATE_READING_DATA,              /* reading message data */
} pop_state_t;

enum pop_index_field{
    EM_POP_PROTO_TYPE,
    EM_POP_CMD_RSP,
    EM_POP_PARAMETER,
    EM_POP_DATE,
    EM_POP_DATE_WARNING,
    EM_POP_FROM,
    EM_POP_SENDER,
    EM_POP_SENDER_HOST,
    EM_POP_SENDER_MAILER,
    EM_POP_SENDER_IP,
    EM_POP_REPLY_TO,
    EM_POP_TO,
    EM_POP_RECEIVER,
    EM_POP_RECEIVER_HOST,
    EM_POP_RECEIVER_IP,
    EM_POP_RECEIVER_TYPE,

    EM_POP_CC,
    EM_POP_BCC,
    EM_POP_MESSAGE_ID,
    EM_POP_IN_REPLY_TO,
    EM_POP_REFERENCES,
    EM_POP_SUBJECT,
    EM_POP_COMMENTS,
    EM_POP_KEYWORDS,
    EM_POP_FOLLOWUP_TO,
    EM_POP_RESENT_DATE,
    EM_POP_RESENT_AGENT,
    EM_POP_RESENT_SENDER,
    EM_POP_RESENT_TO,
    EM_POP_RESENT_CC,
    EM_POP_RESENT_BCC,
    EM_POP_RESENT_REPLY_TO,
    EM_POP_RESENT_MESSAGE_ID,
    EM_POP_RETURN_PATH,
    EM_POP_RECEIVED,
    EM_POP_ENCRYPTED,
    EM_POP_DISPOSITION_NOTIFICATION_TO,
    EM_POP_DISPOSITION_NOTIFICATION_OPTIONS,
    EM_POP_ACCEPT_LANGUAGE,
    EM_POP_ORIGINAL_MESSAGE_ID,
    EM_POP_PICS_LABEL,
    EM_POP_CONTENT_ENCODING,
    EM_POP_LIST_ARCHIVE,
    EM_POP_LIST_HELP,
    EM_POP_LIST_ID,
    EM_POP_LIST_OWNER,
    EM_POP_LIST_POST,
    EM_POP_LIST_SUBSCRIBE,
    EM_POP_LIST_UNSUBSCRIBE,
    EM_POP_MESSAGE_CONTEXT,
    EM_POP_DL_EXPANSION_HISTORY,
    EM_POP_ALTERNATE_RECIPIENT,
    EM_POP_ORIGINAL_ENCODED_INFORMATION_TYPES,
    EM_POP_CONTENT_RETURN,
    EM_POP_GENERATE_DELIVERY_REPORT,
    EM_POP_PREVENT_NONDELIVERY_REPORT,
    EM_POP_OBSOLETES,
    EM_POP_SUPERSEDES,
    EM_POP_CONTENT_IDENTIFIER,
    EM_POP_DELIVERY_DATE,
    EM_POP_EXPIRY_DATE,
    EM_POP_EXPIRES,
    EM_POP_REPLY_BY,
    EM_POP_IMPORTANCE,
    EM_POP_INCOMPLETE_COPY,
    EM_POP_PRIORITY,
    EM_POP_PRECEDENCE,
    EM_POP_SENSITIVITY,
    EM_POP_LANGUAGE,
    EM_POP_CONVERSION,
    EM_POP_CONVERSION_WITH_LOSS,
    EM_POP_MESSAGE_TYPE,
    EM_POP_AUTOSUBMITTED,
    EM_POP_AUTOFORWARDED,
    EM_POP_DISCARDED_X400_IPMS_EXTENSIONS,
    EM_POP_DISCARDED_X400_MTS_EXTENSIONS,
    EM_POP_DISCLOSE_RECIPIENTS,
    EM_POP_DEFERRED_DELIVERY,
    EM_POP_LATEST_DELIVERY_TIME,
    EM_POP_ORIGINATOR_RETURN_ADDRESS,
    EM_POP_X400_CONTENT_IDENTIFIER,
    EM_POP_X400_CONTENT_RETURN,
    EM_POP_X400_CONTENT_TYPE,
    EM_POP_X400_MTS_IDENTIFIER,
    EM_POP_X400_ORIGINATOR,
    EM_POP_X400_RECEIVED,
    EM_POP_X400_RECIPIENTS,
    EM_POP_X400_TRACE,
    EM_POP_MIME_VERSION,
    EM_POP_CONTENT_ID,
    EM_POP_CONTENT_DESCRIPTION,
    EM_POP_CONTENT_TRANSFER_ENCODING,
    EM_POP_CONTENT_TYPE,
    EM_POP_CONTENT_BASE,
    EM_POP_CONTENT_LOCATION,
    EM_POP_CONTENT_FEATURES,
    EM_POP_CONTENT_DISPOSITION,
    EM_POP_CONTENT_LANGUAGE,
    EM_POP_CONTENT_ALTERNATIVE,
    EM_POP_CONTENT_MD5,
    EM_POP_CONTENT_DURATION,

    EM_POP_USER_NAME,
    EM_POP_USER_PASSWD,
    EM_POP_MAIL_FILENAME,
    EM_POP_HAS_ATTACH,
    EM_POP_ATTACH_NUM,
    EM_POP_ATTACH_NAME,
    EM_POP_ATTACH_TYPE,
    EM_POP_ATTACH_LENGTH,
    EM_POP_X_GUID,
    EM_POP_USER_AGENT,
    EM_POP_USER_APP,
    EM_POP_SERVER,
    EM_POP_QUERY_INDEX,

    EM_POP_OP_TYPE,        //操作类型...
    EM_POP_OP_TIME,
    EM_POP_OP_RESULT,

    EM_POP_MAIL_CODE_FORMAT,
    EM_POP_MAIL_CODE_BASE,
    EM_POP_MAIL_CONTENT_DATA,

    EM_POP_EXTMAILHDRCOUNT,
    EM_POP_EXTMAILHDRNAME0,
    EM_POP_EXTMAILHDRVALUE0,
    EM_POP_EXTMAILHDRNAME1,
    EM_POP_EXTMAILHDRVALUE1,
    EM_POP_EXTMAILHDRNAME2,
    EM_POP_EXTMAILHDRVALUE2,
    EM_POP_EXTMAILHDRNAME3,
    EM_POP_EXTMAILHDRVALUE3,
    EM_POP_EXTMAILHDRNAME4,
    EM_POP_EXTMAILHDRVALUE4,
    EM_POP_EXTMAILHDRNAME5,
    EM_POP_EXTMAILHDRVALUE5,
    EM_POP_EXTMAILHDRNAME6,
    EM_POP_EXTMAILHDRVALUE6,
    EM_POP_EXTMAILHDRNAME7,
    EM_POP_EXTMAILHDRVALUE7,
    EM_POP_EXTMAILHDRNAME8,
    EM_POP_EXTMAILHDRVALUE8,
    EM_POP_EXTMAILHDRNAME9,
    EM_POP_EXTMAILHDRVALUE9,
    EM_POP_EXTMAILHDRNAME10,
    EM_POP_EXTMAILHDRVALUE10,
    EM_POP_EXTMAILHDRNAME11,
    EM_POP_EXTMAILHDRVALUE11,
    EM_POP_EXTMAILHDRNAME12,
    EM_POP_EXTMAILHDRVALUE12,
    EM_POP_EXTMAILHDRNAME13,
    EM_POP_EXTMAILHDRVALUE13,
    EM_POP_EXTMAILHDRNAME14,
    EM_POP_EXTMAILHDRVALUE14,
    EM_POP_EXTMAILHDRNAME15,
    EM_POP_EXTMAILHDRVALUE15,
    EM_POP_EXTMAILHDRNAME16,
    EM_POP_EXTMAILHDRVALUE16,
    EM_POP_EXTMAILHDRNAME17,
    EM_POP_EXTMAILHDRVALUE17,
    EM_POP_EXTMAILHDRNAME18,
    EM_POP_EXTMAILHDRVALUE18,
    EM_POP_EXTMAILHDRNAME19,
    EM_POP_EXTMAILHDRVALUE19,

    EM_POP_EML_FILESIZE,

    EM_POP_LOGIN_STATUS,

    EM_POP_MAX
};


static dpi_field_table  pop_field_array[] = {


    DPI_FIELD_D(EM_POP_PROTO_TYPE,                                 EM_F_TYPE_NULL,               "Proto-Type"),
    DPI_FIELD_D(EM_POP_CMD_RSP,                                    EM_F_TYPE_NULL,               "Cmd-Rsp"),
    DPI_FIELD_D(EM_POP_PARAMETER,                                  EM_F_TYPE_NULL,               "Parameter"),
    DPI_FIELD_D(EM_POP_DATE,                                       EM_F_TYPE_STRING,             "Date"),
    DPI_FIELD_D(EM_POP_DATE_WARNING,                               EM_F_TYPE_NULL,               "Date-warning"),
    DPI_FIELD_D(EM_POP_FROM,                                       EM_F_TYPE_STRING,             "From"),
    DPI_FIELD_D(EM_POP_SENDER,                                     EM_F_TYPE_STRING,             "Sender"),
    DPI_FIELD_D(EM_POP_SENDER_HOST,                                EM_F_TYPE_STRING,             "Sender-Host"),
    DPI_FIELD_D(EM_POP_SENDER_MAILER,                              EM_F_TYPE_STRING,             "Sender-Mailer"),
    DPI_FIELD_D(EM_POP_SENDER_IP,                                  EM_F_TYPE_STRING,             "Sender-IP"),
    DPI_FIELD_D(EM_POP_REPLY_TO,                                   EM_F_TYPE_STRING,             "Reply-To"),
    DPI_FIELD_D(EM_POP_TO,                                         EM_F_TYPE_STRING,             "To"),
    DPI_FIELD_D(EM_POP_RECEIVER,                                   EM_F_TYPE_STRING,             "Receiver"),
    DPI_FIELD_D(EM_POP_RECEIVER_HOST,                              EM_F_TYPE_STRING,             "Receiver-Host"),
    DPI_FIELD_D(EM_POP_RECEIVER_IP,                                EM_F_TYPE_STRING,             "Receiver-IP"),
    DPI_FIELD_D(EM_POP_RECEIVER_TYPE,                              EM_F_TYPE_STRING,             "Receiver-TYPE"),

    DPI_FIELD_D(EM_POP_CC,                                         EM_F_TYPE_STRING,             "Cc"),
    DPI_FIELD_D(EM_POP_BCC,                                        EM_F_TYPE_STRING,             "Bcc"),
    DPI_FIELD_D(EM_POP_MESSAGE_ID,                                 EM_F_TYPE_STRING,             "Message-ID"),
    DPI_FIELD_D(EM_POP_IN_REPLY_TO,                                EM_F_TYPE_STRING,             "In-Reply-To"),
    DPI_FIELD_D(EM_POP_REFERENCES,                                 EM_F_TYPE_STRING,             "References"),
    DPI_FIELD_D(EM_POP_SUBJECT,                                    EM_F_TYPE_STRING,             "Subject"),
    DPI_FIELD_D(EM_POP_COMMENTS,                                   EM_F_TYPE_NULL,               "Comments"),
    DPI_FIELD_D(EM_POP_KEYWORDS,                                   EM_F_TYPE_NULL,               "Keywords"),
    DPI_FIELD_D(EM_POP_FOLLOWUP_TO,                                EM_F_TYPE_NULL,               "Followup-to"),
    DPI_FIELD_D(EM_POP_RESENT_DATE,                                EM_F_TYPE_STRING,             "Resent-Date"),    //转发信息...
    DPI_FIELD_D(EM_POP_RESENT_AGENT,                               EM_F_TYPE_STRING,             "Resent-Agent"),
    DPI_FIELD_D(EM_POP_RESENT_SENDER,                              EM_F_TYPE_NULL,               "Resent-Sender"),
    DPI_FIELD_D(EM_POP_RESENT_TO,                                  EM_F_TYPE_NULL,               "Resent-To"),
    DPI_FIELD_D(EM_POP_RESENT_CC,                                  EM_F_TYPE_NULL,               "Resent-Cc"),
    DPI_FIELD_D(EM_POP_RESENT_BCC,                                 EM_F_TYPE_NULL,               "Resent-Bcc"),
    DPI_FIELD_D(EM_POP_RESENT_REPLY_TO,                            EM_F_TYPE_NULL,               "Resent-Reply_To"),
    DPI_FIELD_D(EM_POP_RESENT_MESSAGE_ID,                          EM_F_TYPE_NULL,               "Resent-Message_ID"),
    DPI_FIELD_D(EM_POP_RETURN_PATH,                                EM_F_TYPE_STRING,             "Return-Path"),
    DPI_FIELD_D(EM_POP_RECEIVED,                                   EM_F_TYPE_NULL,               "Received"),
    DPI_FIELD_D(EM_POP_ENCRYPTED,                                  EM_F_TYPE_NULL,               "Encrypted"),
    DPI_FIELD_D(EM_POP_DISPOSITION_NOTIFICATION_TO,                EM_F_TYPE_STRING,             "Disposition-Notification-To"),
    DPI_FIELD_D(EM_POP_DISPOSITION_NOTIFICATION_OPTIONS,           EM_F_TYPE_NULL,               "Disposition-Notification-Options"),
    DPI_FIELD_D(EM_POP_ACCEPT_LANGUAGE,                            EM_F_TYPE_STRING,             "Accept-Language"),
    DPI_FIELD_D(EM_POP_ORIGINAL_MESSAGE_ID,                        EM_F_TYPE_NULL,               "Original-Message_ID"),
    DPI_FIELD_D(EM_POP_PICS_LABEL,                                 EM_F_TYPE_NULL,               "PICS-Label"),
    DPI_FIELD_D(EM_POP_CONTENT_ENCODING,                           EM_F_TYPE_STRING,             "Content-Encoding"),
    DPI_FIELD_D(EM_POP_LIST_ARCHIVE,                               EM_F_TYPE_NULL,               "List-Archive"),
    DPI_FIELD_D(EM_POP_LIST_HELP,                                  EM_F_TYPE_NULL,               "List-Help"),
    DPI_FIELD_D(EM_POP_LIST_ID,                                    EM_F_TYPE_NULL,               "List-ID"),
    DPI_FIELD_D(EM_POP_LIST_OWNER,                                 EM_F_TYPE_NULL,               "List-Owner"),
    DPI_FIELD_D(EM_POP_LIST_POST,                                  EM_F_TYPE_NULL,               "List-Post"),
    DPI_FIELD_D(EM_POP_LIST_SUBSCRIBE,                             EM_F_TYPE_NULL,               "List-Subscribe"),
    DPI_FIELD_D(EM_POP_LIST_UNSUBSCRIBE,                           EM_F_TYPE_NULL,               "List-Unsubscribe"),
    DPI_FIELD_D(EM_POP_MESSAGE_CONTEXT,                            EM_F_TYPE_NULL,               "Message-Context"),
    DPI_FIELD_D(EM_POP_DL_EXPANSION_HISTORY,                       EM_F_TYPE_NULL,               "DL-Expansion-History"),
    DPI_FIELD_D(EM_POP_ALTERNATE_RECIPIENT,                        EM_F_TYPE_NULL,               "Alternate-Recipient"),
    DPI_FIELD_D(EM_POP_ORIGINAL_ENCODED_INFORMATION_TYPES,         EM_F_TYPE_NULL,               "Original-Encoded-Information-Types"),
    DPI_FIELD_D(EM_POP_CONTENT_RETURN,                             EM_F_TYPE_NULL,               "Content-Return"),
    DPI_FIELD_D(EM_POP_GENERATE_DELIVERY_REPORT,                   EM_F_TYPE_NULL,               "Generate-Delivery-Report"),
    DPI_FIELD_D(EM_POP_PREVENT_NONDELIVERY_REPORT,                 EM_F_TYPE_NULL,               "Prevent-NonDelivery-Report"),
    DPI_FIELD_D(EM_POP_OBSOLETES,                                  EM_F_TYPE_NULL,               "Obsoletes,DB_FT_STRING"),
    DPI_FIELD_D(EM_POP_SUPERSEDES,                                 EM_F_TYPE_NULL,               "Supersedes"),
    DPI_FIELD_D(EM_POP_CONTENT_IDENTIFIER,                         EM_F_TYPE_NULL,               "Content-Identifier"),
    DPI_FIELD_D(EM_POP_DELIVERY_DATE,                              EM_F_TYPE_STRING,             "Delivery-Date"),
    DPI_FIELD_D(EM_POP_EXPIRY_DATE,                                EM_F_TYPE_STRING,             "Expiry-Date"),
    DPI_FIELD_D(EM_POP_EXPIRES,                                    EM_F_TYPE_NULL,               "Expires"),
    DPI_FIELD_D(EM_POP_REPLY_BY,                                   EM_F_TYPE_NULL,               "Reply-By"),
    DPI_FIELD_D(EM_POP_IMPORTANCE,                                 EM_F_TYPE_NULL,               "Importance"),
    DPI_FIELD_D(EM_POP_INCOMPLETE_COPY,                            EM_F_TYPE_NULL,              "Incomplete_Copy"),
    DPI_FIELD_D(EM_POP_PRIORITY,                                   EM_F_TYPE_STRING,             "Priority"),
    DPI_FIELD_D(EM_POP_PRECEDENCE,                                 EM_F_TYPE_STRING,             "Precedence"),
    DPI_FIELD_D(EM_POP_SENSITIVITY,                                EM_F_TYPE_NULL,               "Sensitivity"),
    DPI_FIELD_D(EM_POP_LANGUAGE,                                   EM_F_TYPE_NULL,               "Language"),
    DPI_FIELD_D(EM_POP_CONVERSION,                                 EM_F_TYPE_NULL,               "Conversion"),
    DPI_FIELD_D(EM_POP_CONVERSION_WITH_LOSS,                       EM_F_TYPE_NULL,               "Conversion-With-Loss"),
    DPI_FIELD_D(EM_POP_MESSAGE_TYPE,                               EM_F_TYPE_NULL,               "Message-Type"),
    DPI_FIELD_D(EM_POP_AUTOSUBMITTED,                              EM_F_TYPE_NULL,               "Autosubmitted"),
    DPI_FIELD_D(EM_POP_AUTOFORWARDED,                              EM_F_TYPE_NULL,               "Autoforwarded"),
    DPI_FIELD_D(EM_POP_DISCARDED_X400_IPMS_EXTENSIONS,             EM_F_TYPE_NULL,               "Discarded-X400-IPMS-Extensions"),
    DPI_FIELD_D(EM_POP_DISCARDED_X400_MTS_EXTENSIONS,              EM_F_TYPE_NULL,               "Discarded-X400-MTS-Extensions"),
    DPI_FIELD_D(EM_POP_DISCLOSE_RECIPIENTS,                        EM_F_TYPE_NULL,               "Disclose-Recipients"),
    DPI_FIELD_D(EM_POP_DEFERRED_DELIVERY,                          EM_F_TYPE_NULL,               "Deferred-Delivery"),
    DPI_FIELD_D(EM_POP_LATEST_DELIVERY_TIME,                       EM_F_TYPE_NULL,               "Latest-Delivery-Time"),
    DPI_FIELD_D(EM_POP_ORIGINATOR_RETURN_ADDRESS,                  EM_F_TYPE_NULL,               "Originator-Return-Address"),
    DPI_FIELD_D(EM_POP_X400_CONTENT_IDENTIFIER,                    EM_F_TYPE_NULL,               "X400-Content-Identifier"),
    DPI_FIELD_D(EM_POP_X400_CONTENT_RETURN,                        EM_F_TYPE_NULL,               "X400-Content-Return"),
    DPI_FIELD_D(EM_POP_X400_CONTENT_TYPE,                          EM_F_TYPE_NULL,               "X400-Content-Type"),
    DPI_FIELD_D(EM_POP_X400_MTS_IDENTIFIER,                        EM_F_TYPE_NULL,               "X400-MTS-Identifier"),
    DPI_FIELD_D(EM_POP_X400_ORIGINATOR,                            EM_F_TYPE_NULL,               "X400-Originator"),
    DPI_FIELD_D(EM_POP_X400_RECEIVED,                              EM_F_TYPE_NULL,               "X400-Received"),
    DPI_FIELD_D(EM_POP_X400_RECIPIENTS,                            EM_F_TYPE_NULL,               "X400-Recipients"),
    DPI_FIELD_D(EM_POP_X400_TRACE,                                 EM_F_TYPE_NULL,               "X400-Trace"),
    DPI_FIELD_D(EM_POP_MIME_VERSION,                               EM_F_TYPE_STRING,             "MIME-Version"),
    DPI_FIELD_D(EM_POP_CONTENT_ID,                                 EM_F_TYPE_NULL,               "Content-ID"),
    DPI_FIELD_D(EM_POP_CONTENT_DESCRIPTION,                        EM_F_TYPE_NULL,               "Content-Description"),
    DPI_FIELD_D(EM_POP_CONTENT_TRANSFER_ENCODING,                  EM_F_TYPE_STRING,             "Content-Transfer-Encoding"),
    DPI_FIELD_D(EM_POP_CONTENT_TYPE,                               EM_F_TYPE_STRING,             "Content-Type"),
    DPI_FIELD_D(EM_POP_CONTENT_BASE,                               EM_F_TYPE_NULL,               "Content-Base"),
    DPI_FIELD_D(EM_POP_CONTENT_LOCATION,                           EM_F_TYPE_NULL,               "Content-Location"),
    DPI_FIELD_D(EM_POP_CONTENT_FEATURES,                           EM_F_TYPE_NULL,               "Content-features"),
    DPI_FIELD_D(EM_POP_CONTENT_DISPOSITION,                        EM_F_TYPE_NULL,               "Content-Disposition"),
    DPI_FIELD_D(EM_POP_CONTENT_LANGUAGE,                           EM_F_TYPE_STRING,             "Content-Language"),
    DPI_FIELD_D(EM_POP_CONTENT_ALTERNATIVE,                        EM_F_TYPE_NULL,               "Content-Alternative"),
    DPI_FIELD_D(EM_POP_CONTENT_MD5,                                EM_F_TYPE_NULL,               "Content-MD5"),
    DPI_FIELD_D(EM_POP_CONTENT_DURATION,                           EM_F_TYPE_UINT32,             "Content-Duration"),

    DPI_FIELD_D(EM_POP_USER_NAME,                                  EM_F_TYPE_STRING,             "User-Name"),
    DPI_FIELD_D(EM_POP_USER_PASSWD,                                EM_F_TYPE_STRING,             "User-Passwd"),
    DPI_FIELD_D(EM_POP_MAIL_FILENAME,                              EM_F_TYPE_STRING,             "Mail-Filename"),
    DPI_FIELD_D(EM_POP_HAS_ATTACH,                                 EM_F_TYPE_STRING,             "Has-Attach"),  //附件信息...
    DPI_FIELD_D(EM_POP_ATTACH_NUM,                                 EM_F_TYPE_STRING,             "Attach-Num"),
    DPI_FIELD_D(EM_POP_ATTACH_NAME,                                EM_F_TYPE_STRING,             "Attach-Name"),
    DPI_FIELD_D(EM_POP_ATTACH_TYPE,                                EM_F_TYPE_STRING,             "Attach-Type"),
    DPI_FIELD_D(EM_POP_ATTACH_LENGTH,                              EM_F_TYPE_UINT32,             "Attach-Length"),
    DPI_FIELD_D(EM_POP_X_GUID,                                     EM_F_TYPE_STRING,             "X-GUID"),
    DPI_FIELD_D(EM_POP_USER_AGENT,                                 EM_F_TYPE_STRING,             "User-Agent"),
    DPI_FIELD_D(EM_POP_USER_APP,                                   EM_F_TYPE_STRING,             "User-App"),
    DPI_FIELD_D(EM_POP_SERVER,                                     EM_F_TYPE_STRING,             "Server"),
    DPI_FIELD_D(EM_POP_QUERY_INDEX,                                EM_F_TYPE_STRING,             "Query-Index"),

    DPI_FIELD_D(EM_POP_OP_TYPE,                                    EM_F_TYPE_STRING,             "Op-Type"),     //操作类型
    DPI_FIELD_D(EM_POP_OP_TIME,                                    EM_F_TYPE_STRING,             "Op-Time"),
    DPI_FIELD_D(EM_POP_OP_RESULT,                                  EM_F_TYPE_STRING,             "Op-Result"),


    /* new fields add by liugh*/
    DPI_FIELD_D(EM_POP_MAIL_CODE_FORMAT,                           EM_F_TYPE_STRING,             "MailContentCodeFormat"),
    DPI_FIELD_D(EM_POP_MAIL_CODE_BASE,                             EM_F_TYPE_STRING,             "MailContentCodeBase"),
    DPI_FIELD_D(EM_POP_MAIL_CONTENT_DATA,                          EM_F_TYPE_STRING,             "MailContentData"),


    DPI_FIELD_D(EM_POP_EXTMAILHDRCOUNT,                            EM_F_TYPE_UINT8,                "ExtMailHdrCount"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME0,                            EM_F_TYPE_NULL,                 "ExtMailHdrName0"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE0,                           EM_F_TYPE_NULL,                 "ExtMailHdrValue0"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME1,                            EM_F_TYPE_NULL,                 "ExtMailHdrName1"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE1,                           EM_F_TYPE_NULL,                 "ExtMailHdrValue1"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME2,                            EM_F_TYPE_NULL,                 "ExtMailHdrName2"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE2,                           EM_F_TYPE_NULL,                 "ExtMailHdrValue2"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME3,                            EM_F_TYPE_NULL,                 "ExtMailHdrName3"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE3,                           EM_F_TYPE_NULL,                 "ExtMailHdrValue3"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME4,                            EM_F_TYPE_NULL,                 "ExtMailHdrName4"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE4,                           EM_F_TYPE_NULL,                 "ExtMailHdrValue4"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME5,                            EM_F_TYPE_NULL,                 "ExtMailHdrName5"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE5,                           EM_F_TYPE_NULL,                 "ExtMailHdrValue5"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME6,                            EM_F_TYPE_NULL,                 "ExtMailHdrName6"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE6,                           EM_F_TYPE_NULL,                 "ExtMailHdrValue6"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME7,                            EM_F_TYPE_NULL,                 "ExtMailHdrName7"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE7,                           EM_F_TYPE_NULL,                 "ExtMailHdrValue7"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME8,                            EM_F_TYPE_NULL,                 "ExtMailHdrName8"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE8,                           EM_F_TYPE_NULL,                 "ExtMailHdrValue8"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME9,                            EM_F_TYPE_NULL,                 "ExtMailHdrName9"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE9,                           EM_F_TYPE_NULL,                 "ExtMailHdrValue9"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME10,                           EM_F_TYPE_NULL,                 "ExtMailHdrName10"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE10,                          EM_F_TYPE_NULL,                 "ExtMailHdrValue10"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME11,                           EM_F_TYPE_NULL,                 "ExtMailHdrName11"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE11,                          EM_F_TYPE_NULL,                 "ExtMailHdrValue11"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME12,                           EM_F_TYPE_NULL,                 "ExtMailHdrName12"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE12,                          EM_F_TYPE_NULL,                 "ExtMailHdrValue12"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME13,                           EM_F_TYPE_NULL,                 "ExtMailHdrName13"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE13,                          EM_F_TYPE_NULL,                 "ExtMailHdrValue13"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME14,                           EM_F_TYPE_NULL,                 "ExtMailHdrName14"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE14,                          EM_F_TYPE_NULL,                 "ExtMailHdrValue14"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME15,                           EM_F_TYPE_NULL,                 "ExtMailHdrName15"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE15,                          EM_F_TYPE_NULL,                 "ExtMailHdrValue15"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME16,                           EM_F_TYPE_NULL,                 "ExtMailHdrName16"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE16,                          EM_F_TYPE_NULL,                 "ExtMailHdrValue16"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME17,                           EM_F_TYPE_NULL,                 "ExtMailHdrName17"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE17,                          EM_F_TYPE_NULL,                 "ExtMailHdrValue17"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME18,                           EM_F_TYPE_NULL,                 "ExtMailHdrName18"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE18,                          EM_F_TYPE_NULL,                 "ExtMailHdrValue18"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME19,                           EM_F_TYPE_NULL,                 "ExtMailHdrName19"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE19,                          EM_F_TYPE_NULL,                 "ExtMailHdrValue19"),

    DPI_FIELD_D(EM_POP_EML_FILESIZE,                               EM_F_TYPE_UINT32,             "eml_filesize"),
    DPI_FIELD_D(EM_POP_LOGIN_STATUS,                               EM_F_TYPE_STRING,             "LoginStatus")

};

static dpi_field_table  pop_field_array_sdt[] = {
    DPI_FIELD_D(EM_POP_PROTO_TYPE,                                 YA_FT_STRING,                "Proto-Type"),
    DPI_FIELD_D(EM_POP_CMD_RSP,                                    YA_FT_NONE,                  "Cmd-Rsp"),
    DPI_FIELD_D(EM_POP_PARAMETER,                                  YA_FT_NONE,                  "Parameter"),
    DPI_FIELD_D(EM_POP_DATE,                                       YA_FT_STRING,                "Date"),
    DPI_FIELD_D(EM_POP_DATE_WARNING,                               YA_FT_NONE,                  "Date-warning"),
    DPI_FIELD_D(EM_POP_FROM,                                       YA_FT_STRING,                "From"),
    DPI_FIELD_D(EM_POP_SENDER,                                     YA_FT_STRING,                "Sender"),
    DPI_FIELD_D(EM_POP_SENDER_HOST,                                YA_FT_STRING,                "Sender-Host"),
    DPI_FIELD_D(EM_POP_SENDER_MAILER,                              YA_FT_STRING,                "Sender-Mailer"),
    DPI_FIELD_D(EM_POP_SENDER_IP,                                  YA_FT_STRING,                "Sender-IP"),
    DPI_FIELD_D(EM_POP_REPLY_TO,                                   YA_FT_STRING,                "Reply-To"),
    DPI_FIELD_D(EM_POP_TO,                                         YA_FT_STRING,                "To"),
    DPI_FIELD_D(EM_POP_RECEIVER,                                   YA_FT_STRING,                "Receiver"),
    DPI_FIELD_D(EM_POP_RECEIVER_HOST,                              YA_FT_STRING,                "Receiver-Host"),
    DPI_FIELD_D(EM_POP_RECEIVER_IP,                                YA_FT_STRING,                "Receiver-IP"),
    DPI_FIELD_D(EM_POP_RECEIVER_TYPE,                              YA_FT_STRING,                "Receiver-TYPE"),

    DPI_FIELD_D(EM_POP_CC,                                         YA_FT_STRING,                "Cc"),
    DPI_FIELD_D(EM_POP_BCC,                                        YA_FT_STRING,                "Bcc"),
    DPI_FIELD_D(EM_POP_MESSAGE_ID,                                 YA_FT_STRING,                "Message-ID"),
    DPI_FIELD_D(EM_POP_IN_REPLY_TO,                                YA_FT_STRING,                "In-Reply-To"),
    DPI_FIELD_D(EM_POP_REFERENCES,                                 YA_FT_STRING,                "References"),
    DPI_FIELD_D(EM_POP_SUBJECT,                                    YA_FT_STRING,                "Subject"),
    DPI_FIELD_D(EM_POP_COMMENTS,                                   YA_FT_NONE,                  "Comments"),
    DPI_FIELD_D(EM_POP_KEYWORDS,                                   YA_FT_NONE,                  "Keywords"),
    DPI_FIELD_D(EM_POP_FOLLOWUP_TO,                                YA_FT_NONE,                  "Followup-to"),
    DPI_FIELD_D(EM_POP_RESENT_DATE,                                YA_FT_STRING,                "Resent-Date"),    //转发信息
    DPI_FIELD_D(EM_POP_RESENT_AGENT,                               YA_FT_STRING,                "Resent-Agent"),
    DPI_FIELD_D(EM_POP_RESENT_SENDER,                              YA_FT_NONE,                  "Resent-Sender"),
    DPI_FIELD_D(EM_POP_RESENT_TO,                                  YA_FT_NONE,                  "Resent-To"),
    DPI_FIELD_D(EM_POP_RESENT_CC,                                  YA_FT_NONE,                  "Resent-Cc"),
    DPI_FIELD_D(EM_POP_RESENT_BCC,                                 YA_FT_NONE,                  "Resent-Bcc"),
    DPI_FIELD_D(EM_POP_RESENT_REPLY_TO,                            YA_FT_NONE,                  "Resent-Reply_To"),
    DPI_FIELD_D(EM_POP_RESENT_MESSAGE_ID,                          YA_FT_NONE,                  "Resent-Message_ID"),
    DPI_FIELD_D(EM_POP_RETURN_PATH,                                YA_FT_STRING,                "Return-Path"),
    DPI_FIELD_D(EM_POP_RECEIVED,                                   YA_FT_NONE,                  "Received"),
    DPI_FIELD_D(EM_POP_ENCRYPTED,                                  YA_FT_NONE,                  "Encrypted"),
    DPI_FIELD_D(EM_POP_DISPOSITION_NOTIFICATION_TO,                YA_FT_STRING,                "Disposition-Notification-To"),
    DPI_FIELD_D(EM_POP_DISPOSITION_NOTIFICATION_OPTIONS,           YA_FT_NONE,                  "Disposition-Notification-Options"),
    DPI_FIELD_D(EM_POP_ACCEPT_LANGUAGE,                            YA_FT_STRING,                "Accept-Language"),
    DPI_FIELD_D(EM_POP_ORIGINAL_MESSAGE_ID,                        YA_FT_NONE,                  "Original-Message_ID"),
    DPI_FIELD_D(EM_POP_PICS_LABEL,                                 YA_FT_NONE,                  "PICS-Label"),
    DPI_FIELD_D(EM_POP_CONTENT_ENCODING,                           YA_FT_STRING,                "Content-Encoding"),
    DPI_FIELD_D(EM_POP_LIST_ARCHIVE,                               YA_FT_NONE,                  "List-Archive"),
    DPI_FIELD_D(EM_POP_LIST_HELP,                                  YA_FT_NONE,                  "List-Help"),
    DPI_FIELD_D(EM_POP_LIST_ID,                                    YA_FT_NONE,                  "List-ID"),
    DPI_FIELD_D(EM_POP_LIST_OWNER,                                 YA_FT_NONE,                  "List-Owner"),
    DPI_FIELD_D(EM_POP_LIST_POST,                                  YA_FT_NONE,                  "List-Post"),
    DPI_FIELD_D(EM_POP_LIST_SUBSCRIBE,                             YA_FT_NONE,                  "List-Subscribe"),
    DPI_FIELD_D(EM_POP_LIST_UNSUBSCRIBE,                           YA_FT_NONE,                  "List-Unsubscribe"),
    DPI_FIELD_D(EM_POP_MESSAGE_CONTEXT,                            YA_FT_NONE,                  "Message-Context"),
    DPI_FIELD_D(EM_POP_DL_EXPANSION_HISTORY,                       YA_FT_NONE,                  "DL-Expansion-History"),
    DPI_FIELD_D(EM_POP_ALTERNATE_RECIPIENT,                        YA_FT_NONE,                  "Alternate-Recipient"),
    DPI_FIELD_D(EM_POP_ORIGINAL_ENCODED_INFORMATION_TYPES,         YA_FT_NONE,                  "Original-Encoded-Information-Types"),
    DPI_FIELD_D(EM_POP_CONTENT_RETURN,                             YA_FT_NONE,                  "Content-Return"),
    DPI_FIELD_D(EM_POP_GENERATE_DELIVERY_REPORT,                   YA_FT_NONE,                  "Generate-Delivery-Report"),
    DPI_FIELD_D(EM_POP_PREVENT_NONDELIVERY_REPORT,                 YA_FT_NONE,                  "Prevent-NonDelivery-Report"),
    DPI_FIELD_D(EM_POP_OBSOLETES,                                  YA_FT_NONE,                  "Obsoletes,DB_FT_STRING"),
    DPI_FIELD_D(EM_POP_SUPERSEDES,                                 YA_FT_NONE,                  "Supersedes"),
    DPI_FIELD_D(EM_POP_CONTENT_IDENTIFIER,                         YA_FT_NONE,                  "Content-Identifier"),
    DPI_FIELD_D(EM_POP_DELIVERY_DATE,                              YA_FT_STRING,                "Delivery-Date"),
    DPI_FIELD_D(EM_POP_EXPIRY_DATE,                                YA_FT_STRING,                "Expiry-Date"),
    DPI_FIELD_D(EM_POP_EXPIRES,                                    YA_FT_NONE,                  "Expires"),
    DPI_FIELD_D(EM_POP_REPLY_BY,                                   YA_FT_NONE,                  "Reply-By"),
    DPI_FIELD_D(EM_POP_IMPORTANCE,                                 YA_FT_NONE,                  "Importance"),
    DPI_FIELD_D(EM_POP_INCOMPLETE_COPY,                            YA_FT_NONE,                  "Incomplete_Copy"),
    DPI_FIELD_D(EM_POP_PRIORITY,                                   YA_FT_STRING,                "Priority"),
    DPI_FIELD_D(EM_POP_PRECEDENCE,                                 YA_FT_STRING,                "Precedence"),
    DPI_FIELD_D(EM_POP_SENSITIVITY,                                YA_FT_NONE,                  "Sensitivity"),
    DPI_FIELD_D(EM_POP_LANGUAGE,                                   YA_FT_NONE,                  "Language"),
    DPI_FIELD_D(EM_POP_CONVERSION,                                 YA_FT_NONE,                  "Conversion"),
    DPI_FIELD_D(EM_POP_CONVERSION_WITH_LOSS,                       YA_FT_NONE,                  "Conversion-With-Loss"),
    DPI_FIELD_D(EM_POP_MESSAGE_TYPE,                               YA_FT_NONE,                  "Message-Type"),
    DPI_FIELD_D(EM_POP_AUTOSUBMITTED,                              YA_FT_NONE,                  "Autosubmitted"),
    DPI_FIELD_D(EM_POP_AUTOFORWARDED,                              YA_FT_NONE,                  "Autoforwarded"),
    DPI_FIELD_D(EM_POP_DISCARDED_X400_IPMS_EXTENSIONS,             YA_FT_NONE,                  "Discarded-X400-IPMS-Extensions"),
    DPI_FIELD_D(EM_POP_DISCARDED_X400_MTS_EXTENSIONS,              YA_FT_NONE,                  "Discarded-X400-MTS-Extensions"),
    DPI_FIELD_D(EM_POP_DISCLOSE_RECIPIENTS,                        YA_FT_NONE,                  "Disclose-Recipients"),
    DPI_FIELD_D(EM_POP_DEFERRED_DELIVERY,                          YA_FT_NONE,                  "Deferred-Delivery"),
    DPI_FIELD_D(EM_POP_LATEST_DELIVERY_TIME,                       YA_FT_NONE,                  "Latest-Delivery-Time"),
    DPI_FIELD_D(EM_POP_ORIGINATOR_RETURN_ADDRESS,                  YA_FT_NONE,                  "Originator-Return-Address"),
    DPI_FIELD_D(EM_POP_X400_CONTENT_IDENTIFIER,                    YA_FT_NONE,                  "X400-Content-Identifier"),
    DPI_FIELD_D(EM_POP_X400_CONTENT_RETURN,                        YA_FT_NONE,                  "X400-Content-Return"),
    DPI_FIELD_D(EM_POP_X400_CONTENT_TYPE,                          YA_FT_NONE,                  "X400-Content-Type"),
    DPI_FIELD_D(EM_POP_X400_MTS_IDENTIFIER,                        YA_FT_NONE,                  "X400-MTS-Identifier"),
    DPI_FIELD_D(EM_POP_X400_ORIGINATOR,                            YA_FT_NONE,                  "X400-Originator"),
    DPI_FIELD_D(EM_POP_X400_RECEIVED,                              YA_FT_NONE,                  "X400-Received"),
    DPI_FIELD_D(EM_POP_X400_RECIPIENTS,                            YA_FT_NONE,                  "X400-Recipients"),
    DPI_FIELD_D(EM_POP_X400_TRACE,                                 YA_FT_NONE,                  "X400-Trace"),
    DPI_FIELD_D(EM_POP_MIME_VERSION,                               YA_FT_STRING,                "MIME-Version"),
    DPI_FIELD_D(EM_POP_CONTENT_ID,                                 YA_FT_NONE,                  "Content-ID"),
    DPI_FIELD_D(EM_POP_CONTENT_DESCRIPTION,                        YA_FT_NONE,                  "Content-Description"),
    DPI_FIELD_D(EM_POP_CONTENT_TRANSFER_ENCODING,                  YA_FT_STRING,                "Content-Transfer-Encoding"),
    DPI_FIELD_D(EM_POP_CONTENT_TYPE,                               YA_FT_STRING,                "Content-Type"),
    DPI_FIELD_D(EM_POP_CONTENT_BASE,                               YA_FT_NONE,                  "Content-Base"),
    DPI_FIELD_D(EM_POP_CONTENT_LOCATION,                           YA_FT_NONE,                  "Content-Location"),
    DPI_FIELD_D(EM_POP_CONTENT_FEATURES,                           YA_FT_NONE,                  "Content-features"),
    DPI_FIELD_D(EM_POP_CONTENT_DISPOSITION,                        YA_FT_NONE,                  "Content-Disposition"),
    DPI_FIELD_D(EM_POP_CONTENT_LANGUAGE,                           YA_FT_STRING,                "Content-Language"),
    DPI_FIELD_D(EM_POP_CONTENT_ALTERNATIVE,                        YA_FT_NONE,                  "Content-Alternative"),
    DPI_FIELD_D(EM_POP_CONTENT_MD5,                                YA_FT_NONE,                  "Content-MD5"),
    DPI_FIELD_D(EM_POP_CONTENT_DURATION,                           YA_FT_UINT32,                "Content-Duration"),

    DPI_FIELD_D(EM_POP_USER_NAME,                                  YA_FT_STRING,                "User-Name"),
    DPI_FIELD_D(EM_POP_USER_PASSWD,                                YA_FT_STRING,                "User-Passwd"),
    DPI_FIELD_D(EM_POP_MAIL_FILENAME,                              YA_FT_STRING,                "Mail-Filename"),
    DPI_FIELD_D(EM_POP_HAS_ATTACH,                                 YA_FT_STRING,                "Has-Attach"),  //附件信息...
    DPI_FIELD_D(EM_POP_ATTACH_NUM,                                 YA_FT_STRING,                "Attach-Num"),
    DPI_FIELD_D(EM_POP_ATTACH_NAME,                                YA_FT_STRING,                "Attach-Name"),
    DPI_FIELD_D(EM_POP_ATTACH_TYPE,                                YA_FT_STRING,                "Attach-Type"),
    DPI_FIELD_D(EM_POP_ATTACH_LENGTH,                              YA_FT_UINT32,                "Attach-Length"),
    DPI_FIELD_D(EM_POP_X_GUID,                                     YA_FT_STRING,                "X-GUID"),
    DPI_FIELD_D(EM_POP_USER_AGENT,                                 YA_FT_STRING,                "User-Agent"),
    DPI_FIELD_D(EM_POP_USER_APP,                                   YA_FT_STRING,                "User-App"),
    DPI_FIELD_D(EM_POP_SERVER,                                     YA_FT_STRING,                "Server"),
    DPI_FIELD_D(EM_POP_QUERY_INDEX,                                YA_FT_STRING,                "Query-Index"),

    DPI_FIELD_D(EM_POP_OP_TYPE,                                    YA_FT_STRING,                "Op-Type"),     //操作类型
    DPI_FIELD_D(EM_POP_OP_TIME,                                    YA_FT_STRING,                "Op-Time"),
    DPI_FIELD_D(EM_POP_OP_RESULT,                                  YA_FT_STRING,                "Op-Result"),


    /* new fields add by liugh*/
    DPI_FIELD_D(EM_POP_MAIL_CODE_FORMAT,                           YA_FT_STRING,                "MailContentCodeFormat"),
    DPI_FIELD_D(EM_POP_MAIL_CODE_BASE,                             YA_FT_STRING,                "MailContentCodeBase"),
    DPI_FIELD_D(EM_POP_MAIL_CONTENT_DATA,                          YA_FT_STRING,                "MailContentData"),


    DPI_FIELD_D(EM_POP_EXTMAILHDRCOUNT,                            YA_FT_UINT8,                 "ExtMailHdrCount"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME0,                            YA_FT_STRING,                "ExtMailHdrName0"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE0,                           YA_FT_STRING,                "ExtMailHdrValue0"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME1,                            YA_FT_STRING,                "ExtMailHdrName1"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE1,                           YA_FT_STRING,                "ExtMailHdrValue1"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME2,                            YA_FT_STRING,                "ExtMailHdrName2"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE2,                           YA_FT_STRING,                "ExtMailHdrValue2"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME3,                            YA_FT_STRING,                "ExtMailHdrName3"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE3,                           YA_FT_STRING,                "ExtMailHdrValue3"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME4,                            YA_FT_STRING,                "ExtMailHdrName4"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE4,                           YA_FT_STRING,                "ExtMailHdrValue4"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME5,                            YA_FT_STRING,                "ExtMailHdrName5"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE5,                           YA_FT_STRING,                "ExtMailHdrValue5"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME6,                            YA_FT_STRING,                "ExtMailHdrName6"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE6,                           YA_FT_STRING,                "ExtMailHdrValue6"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME7,                            YA_FT_STRING,                "ExtMailHdrName7"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE7,                           YA_FT_STRING,                "ExtMailHdrValue7"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME8,                            YA_FT_STRING,                "ExtMailHdrName8"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE8,                           YA_FT_STRING,                "ExtMailHdrValue8"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME9,                            YA_FT_STRING,                "ExtMailHdrName9"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE9,                           YA_FT_STRING,                "ExtMailHdrValue9"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME10,                           YA_FT_STRING,                "ExtMailHdrName10"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE10,                          YA_FT_STRING,                "ExtMailHdrValue10"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME11,                           YA_FT_STRING,                "ExtMailHdrName11"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE11,                          YA_FT_STRING,                "ExtMailHdrValue11"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME12,                           YA_FT_STRING,                "ExtMailHdrName12"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE12,                          YA_FT_STRING,                "ExtMailHdrValue12"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME13,                           YA_FT_STRING,                "ExtMailHdrName13"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE13,                          YA_FT_STRING,                "ExtMailHdrValue13"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME14,                           YA_FT_STRING,                "ExtMailHdrName14"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE14,                          YA_FT_STRING,                "ExtMailHdrValue14"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME15,                           YA_FT_STRING,                "ExtMailHdrName15"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE15,                          YA_FT_STRING,                "ExtMailHdrValue15"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME16,                           YA_FT_STRING,                "ExtMailHdrName16"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE16,                          YA_FT_STRING,                "ExtMailHdrValue16"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME17,                           YA_FT_STRING,                "ExtMailHdrName17"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE17,                          YA_FT_STRING,                "ExtMailHdrValue17"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME18,                           YA_FT_STRING,                "ExtMailHdrName18"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE18,                          YA_FT_STRING,                "ExtMailHdrValue18"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRNAME19,                           YA_FT_STRING,                "ExtMailHdrName19"),
    DPI_FIELD_D(EM_POP_EXTMAILHDRVALUE19,                          YA_FT_STRING,                "ExtMailHdrValue19"),

    DPI_FIELD_D(EM_POP_EML_FILESIZE,                               YA_FT_UINT32,                "eml_filesize"),
    DPI_FIELD_D(EM_POP_LOGIN_STATUS,                               YA_FT_STRING,                "LoginStatus")

};



struct pop_unknown_line {
    uint16_t key_len;
    uint16_t val_len;
    const uint8_t *key_ptr;
    const uint8_t *val_ptr;
};


#define POP_ATTACHMENT_NUM_MAX 10

#define POP_ENCODING_LEN        32
#define POP_FILE_NAME_LEN       64
#define POP_CONTENT_TYPE_LEN    128
#define POP_DISPOSITION_LEN     128
#define POP_CONTENT_MD5_LEN     (64)

typedef struct __attach_pop__
{
    //附件文件名
    uint8_t attachment_filename[POP_FILE_NAME_LEN];
    //附件内容类型
    uint8_t attachment_content_type[POP_CONTENT_TYPE_LEN];
    //附件内容编码格式
    uint8_t attachment_content_transfer_encoding[POP_ENCODING_LEN];
    //附件类型，附件文件名
    uint8_t attachment_content_disposition[POP_DISPOSITION_LEN];
    //附件的内容MD5
    uint8_t attachment_content_md5[POP_CONTENT_MD5_LEN];
    //附件内容长度
    long long attachment_len;
} AttachPop;

struct pop_session
{
    dpi_email_t *email;
    pop_state_t state;

    char user_name[64];
    char user_passwd[64];
    char mail_filename[128];
    char content_encoding[10];
    uint32_t begin_time;
    uint32_t end_time;

    const uint8_t *mail_date_ptr;
    uint16_t mail_date_len;

    const uint8_t *mail_from_ptr;
    uint16_t mail_from_len;

    const uint8_t *server;
    uint16_t server_len;

    const uint8_t *sender;
    uint16_t sender_len;

    const uint8_t *sender_host;
    uint16_t sender_host_len;

    const uint8_t *sender_ip;
    uint16_t sender_ip_len;

    const uint8_t *reply_to;
    uint16_t reply_to_len;

    const uint8_t *in_reply_to;
    uint16_t in_reply_to_len;

    const uint8_t *receiver;
    uint16_t receiver_len;

    const uint8_t *references;
    uint16_t references_len;

    const uint8_t *receiver_host;
    uint16_t receiver_host_len;

    const uint8_t *receiver_ip;
    uint16_t receiver_ip_len;

    const uint8_t *receiver_type;
    uint16_t receiver_type_len;

    const uint8_t *mail_to_ptr;
    uint16_t mail_to_len;

    const uint8_t *return_path;
    uint16_t return_path_len;

    const uint8_t *mail_cc_ptr;
    uint16_t mail_cc_len;

    const uint8_t *mail_bcc_ptr;
    uint16_t mail_bcc_len;

    const uint8_t *mail_subject_ptr;
    uint16_t mail_subject_len;

    const uint8_t *resent_agent;
    uint16_t resent_agent_len;

    const uint8_t *resent_date;
    uint16_t resent_date_len;

    const uint8_t *delivery_date;
    uint16_t delivery_date_len;

    const uint8_t *expiry_date;
    uint16_t expiry_date_len;

    const uint8_t *user_agent;
    uint16_t user_agent_len;

    const uint8_t *user_app;
    uint16_t user_app_len;

    const uint8_t *message_id;
    uint16_t message_id_len;

    const uint8_t *content_type;
    uint16_t content_type_len;

    const uint8_t *accept_language;
    uint16_t accept_language_len;

    const uint8_t *content_language;
    uint16_t content_language_len;

    const uint8_t *x_mailer;
    uint16_t x_mailer_len;

    const uint8_t *x_guid;
    uint16_t x_guid_len;

    const uint8_t *content_transfer_encoding;
    uint16_t content_transfer_encoding_len;

    const uint8_t *mime_version;
    uint16_t mime_version_len;

    const uint8_t *attach_num;
    uint16_t attach_num_len;

    const uint8_t *attach_name;
    uint16_t attach_name_len;

    const uint8_t *attach_type;
    uint16_t attach_type_len;

    const uint8_t *has_attach;
    uint16_t has_attach_len;

    uint32_t attach_len;

    const uint8_t *disposition_notification_to;
    uint16_t disposition_notification_to_len;

    const uint8_t *received;
    uint16_t received_len;

    const uint8_t *priority;
    uint16_t priority_len;

    const uint8_t *precedence;
    uint16_t precedence_len;

    uint8_t unknown_line_num;
    struct pop_unknown_line unknown_line[POP_UNKNOWN_LINE_NUM_MAX];

    uint8_t   main_content_print[32];
    uint8_t   main_content_code[50];
    const uint8_t   *main_content_data;
    uint16_t  main_content_len;

    uint32_t eml_filesize;
    char     login_status[COMMON_STATUS_LEN];

    // ==== add

    char auth_name[128];
    char auth_passwd[128];

    //暂定为最多支持10个附件
    AttachPop   attachment_list[POP_ATTACHMENT_NUM_MAX + 1];
    uint8_t     attachment_filename_num;
    uint8_t     attachment_conttype_num;
    uint8_t     attachent_md5_num;

    const uint8_t *mail_resent_date_ptr;
    uint16_t mail_resent_date_len;

    const uint8_t *mail_resent_to_ptr;
    uint16_t mail_resent_to_len;

    const uint8_t *mail_resent_from_ptr;
    uint16_t mail_resent_from_len;

    const uint8_t *mail_rcpt_to_ptr;
    uint16_t mail_rcpt_to_len;

    const uint8_t *mail_delivery_date_ptr;
    uint16_t mail_delivery_date_len;

    const uint8_t *mail_mail_from_ptr;
    uint16_t mail_mail_from_len;

    const uint8_t *mail_delivered_to_ptr;
    uint16_t mail_delivered_to_len;

    const uint8_t *mail_received_ptr;
    uint16_t mail_received_len;

    const uint8_t *mail_content_transfer_encoding_ptr;
    uint16_t mail_content_transfer_encoding_len;

    char mail_server_name_ptr[32];
    char mail_server_soft_ptr[32];

    // from ip -- Received字段中的IP，如果有多个，逗号隔开， 可能还有多个Received字段，都解析
    char    received_from_ips[1024];
    uint8_t received_from_ips_num;  // from ip数量

    char    receiveds[2048];
    uint8_t receiveds_num;

    // from domain  -- Received字段中的from 域名， 用逗号分开
    char    received_from_doms[1024];
    uint8_t received_from_doms_num;

    // by ip  -- Received 字段中的by IP， 逗号分开
    char    received_by_ips[1024];
    uint8_t received_by_ips_num;

    // by dom -- Received字段中的by 域名， 用逗号分开
    char    received_by_doms[1024];
    uint8_t received_by_doms_num;

    // from asn  IP解析出来的自治域
    char    from_asns[2048];
    uint8_t from_asns_num;

    // from country IP解析出来的国家
    char    from_countries[1024];
    uint8_t from_countries_num;

    char    cc_addrs[2048];  // Cc 抄送的地址
    char    cc_alias[2048];  // Cc 抄送的名字
    uint8_t cc_addrs_num;
    uint8_t cc_alias_num;

    // content-type 逗号分开
    char    cont_types[2048];
    uint8_t cont_types_num;

    // text charset  content-type=text是的charset
    char    text_charsets[1024];
    uint8_t text_charsets_num;

    // body type 正文的content-type
    char    body_type[1024];
    uint8_t body_type_num;

    // 邮件的头部设置字段，逗号分开  如 Content-type、X- clm-senderinfo 等
    char    head_sets[2048];
    uint8_t head_sets_num;

    // Message-ID 头
    char    msg_ids[1024];
    uint8_t msg_ids_num;

    // mime-version 头
    char    mime_vers[256];
    uint8_t mime_vers_num;

    // 包含html标签的正文
    const uint8_t *content_has_html_ptr;
    uint16_t content_has_html_len;

    // 正文的charset
    const uint8_t *content_charset_ptr;
    uint16_t content_charset_len;

    // 命令集合
    char    commands[2048];
    uint8_t commands_num;

    const uint8_t *x_ori_ip_ptr;
    uint16_t x_ori_ip_len;

    uint8_t mail_num;  // 邮件份数

    // 发件人邮箱域
    char    mail_from_doms[2048];
    uint8_t mail_from_doms_num;

    // 收件人邮箱域
    char    mail_rcpt_to_doms[2048];
    uint8_t mail_rcpt_to_doms_num;

    // 内容传输编码
    char    body_tra_encs[1024];
    uint8_t body_tra_encs_num;

    // 收件人邮箱， 逗号分号
    char    rcv_mails[2048];
    uint8_t rcv_mails_num;

    // 收件人别名,逗号分开
    char    rcv_aliases[2048];
    uint8_t rcv_aliases_num;

    // 收件人域，逗号分开
    char    rcv_doms[2048];
    uint16_t rcv_doms_num;

    // Subject主题， 逗号分开
    char    subjects[2048];
    uint8_t subjects_num;

    // X-Mailer， 逗号分开
    char    x_mailers[2048];
    uint8_t x_mailers_num;

    // starttls  tls加密标识
    uint8_t starttls_f;

    // 以下是针对imap协议的客户端的信息
    char mail_id_name[128];
    char mail_id_version[128];
    char mail_id_os[128];
    char mail_id_os_version[128];
    char mail_id_vendor[128];

    // login 登录的邮件服务器
    const uint8_t   *login_svr_ptr;
    uint16_t        login_svr_len;

    // host 发件人身份标识
    const uint8_t   *host_ptr;
    uint16_t        host_len;

    // index 请求索引
    const uint8_t   *req_index_ptr;
    uint16_t        req_index_len;


};

#if 0
static uint16_t get_pop_main_content(const char *data_p, uint32_t len, struct pop_session *session)
{
    const char *pp0=NULL;
    const char *pp1=NULL;
    uint32_t tmp_len=0;
    uint32_t index=0;

    memset(session->main_content_print,0,sizeof(session->main_content_print));
    memset(session->main_content_code,0,sizeof(session->main_content_code));
    session->main_content_len=0;
    session->main_content_data=NULL;

    pp0=mail_memstr(data_p+index, "charset=", len);
    if(pp0!=NULL){
        tmp_len=pp0-data_p;
        index+=tmp_len;
        pp1=mail_memstr(pp0, "\r\n",len-index);
        if(pp1!=NULL){
            tmp_len=pp1-pp0;
            snprintf((char *)session->main_content_print,tmp_len+1,"%s",pp0);
            //printf("[####POP]printlen:%d data:%s\n",tmp_len,session->main_content_print);
            index+=tmp_len;
            index+=2;
            if(index>=len){return PKT_DROP;}

        }
    }

    const char *cp0=NULL;
    const char *cp1=NULL;
    if(index>=len){return PKT_DROP;}
    if(pp1!=NULL){
        cp0=mail_memstr(pp1,"Content-Transfer-Encoding: ",len-index);
        tmp_len=cp0-pp1;
        index+=tmp_len;
        if(index>=len){return PKT_DROP;}
        if(cp0!=NULL){
            cp1=mail_memstr(cp0, "\r\n",len-index);
            if(cp1!=NULL){
                //strncpy((char*)session->main_content_code,cp0+tmp_len,cp1-cp0);
                tmp_len=cp1-cp0;
                index+=tmp_len;
                if(index>=len){return PKT_DROP;}

                if(tmp_len>50){tmp_len=50;}
                snprintf((char*)session->main_content_code,tmp_len+1,"%s", cp0);
                //printf("[####POP]code:%s\n",session->main_content_code);
                if(index+2<len ){
                if(strncmp(cp1+2,"\r\n",2)==0){
                    goto content_data;
                }else{
                    index+=2;
                }
                }
            }
        }
    }else{
        cp0=mail_memstr(data_p,"Content-Transfer-Encoding: ",len-index);
        tmp_len=cp0-data_p;
        index+=tmp_len;
        if(index>=len){return PKT_DROP;}
        if(cp0!=NULL){
            cp1=mail_memstr(cp0, "\r\n",len-index);
            if(cp1!=NULL){
                //strncpy((char*)session->main_content_code,cp0+tmp_len,cp1-cp0);
                tmp_len=cp1-cp0;
                index+=tmp_len;
                if(index>=len){return PKT_DROP;}

                if(tmp_len>50){tmp_len=50;}
                snprintf((char*)session->main_content_code,tmp_len,"%s", cp0);
                //printf("[####POP]code:%s\n",session->main_content_code);
                if(index+2<len ){
                if(strncmp(cp1+2,"\r\n",2)==0){
                    goto content_data;
                }else{
                    index+=2;
                }
                }
            }
        }
    }


    const char *p_tmp=NULL;
    if(index>len){return PKT_DROP;}
    p_tmp = mail_memstr(data_p+index,"\r\n\r\n",len-index);
    if(p_tmp!=NULL){
        index=p_tmp-data_p;
        if(index>=len){return PKT_DROP;}
    }

content_data:
    index+=4;
    if(index>=len){return PKT_DROP;}
    session->main_content_data=(const uint8_t *)data_p+index;

    const char *dp0=NULL;
    dp0=mail_memstr(data_p+index,".\r\n",len-index)    ;
    if(cp1!=NULL && dp0!=NULL){
        tmp_len=dp0-data_p;
        if(tmp_len<index){return PKT_DROP;}
        session->main_content_len=tmp_len-index;
        if(session->main_content_len>MAX_CONTENT_SIZE){
            session->main_content_len=MAX_CONTENT_SIZE;
        }
    }else {
        session->main_content_len=0;
        session->main_content_data=NULL;
    }

    #if 0
    if(session->main_content_len>0){
        char tts[1024]={0};
        if(session->main_content_len>1024){
            session->main_content_len=1024;
        }
        snprintf(tts,session->main_content_len,"%s",session->main_content_data);
        printf("\n===============================POP==================================\n");
        printf("%s\n",tts);
        printf("====================================================================\n\n");
    }
    #endif


    return PKT_OK;
}
#else
static uint16_t get_pop_main_content(const char *data_p, uint32_t len, struct pop_session *session)
{
    const char *cp0 = NULL;
    const char *cp1 = NULL;
    const char *pp0 = NULL;
    const char *pp1 = NULL;
    uint32_t tmp_len = 0;
    uint32_t index = 0;

    session->main_content_len = 0;
    session->main_content_data = NULL;

    pp0 = mail_memstr(data_p + index, "charset=", len);
    if (pp0 != NULL) {
        tmp_len = pp0 - data_p;
        index += tmp_len;
        pp1 = mail_memstr(pp0, "\r\n", len - index);
        if (pp1 != NULL) {
            tmp_len = pp1 - pp0;
            strncat(session->text_charsets, (const char *)(pp0 + strlen("charset=")), tmp_len + 1);
            session->text_charsets_num++;
            index += tmp_len;
            index += 2;

            if (!strncasecmp((const char *)data_p, "text/html", 9)) {
                session->content_charset_ptr = (const uint8_t *)(pp0 + strlen("charset="));
                session->content_charset_len = tmp_len + 1;
            }

            if (index >= len) { return PKT_DROP; }

        }
    }

    if (index >= len) { return PKT_DROP; }
    if (pp1 != NULL) {
        cp0 = mail_memstr(pp1, "Content-Transfer-Encoding: ", len - index);
        tmp_len = cp0 - pp1;
        index += tmp_len;
        if (index >= len) { return PKT_DROP; }
        if (cp0 != NULL) {
            cp1 = mail_memstr(cp0, "\r\n", len - index);
            if (cp1 != NULL) {
                tmp_len = cp1 - cp0;
                index += tmp_len;
                if (index >= len) { return PKT_DROP; }

                if (tmp_len > 50) { tmp_len = 50; }
                snprintf((char*)session->main_content_code, tmp_len + 1, "%s", cp0);
                strncat(session->body_tra_encs, cp0, tmp_len + 1);
                strcat(session->body_tra_encs, ",");
                session->body_tra_encs_num++;

                if (index + 2 < len) {
                    if (strncmp(cp1 + 2, "\r\n", 2) == 0) {
                        goto content_data;
                    }
                    else {
                        index += 2;
                    }
                }
            }
        }
    }
    else {
        cp0 = mail_memstr(data_p, "Content-Transfer-Encoding: ", len - index);
        tmp_len = cp0 - data_p;
        index += tmp_len;
        if (index >= len) { return PKT_DROP; }
        if (cp0 != NULL) {
            cp1 = mail_memstr(cp0, "\r\n", len - index);
            if (cp1 != NULL) {
                tmp_len = cp1 - cp0;
                index += tmp_len;
                if (index >= len) { return PKT_DROP; }

                if (tmp_len > 50) { tmp_len = 50; }
                snprintf((char*)session->main_content_code, tmp_len, "%s", cp0);
                strncat(session->body_tra_encs, cp0, tmp_len);
                strcat(session->body_tra_encs, ",");
                session->body_tra_encs_num++;

                if (index + 2 < len) {
                    if (strncmp(cp1 + 2, "\r\n", 2) == 0) {
                        goto content_data;
                    }
                    else {
                        index += 2;
                    }
                }
            }
        }
    }

    const char *p_tmp = NULL;
    p_tmp = mail_memstr(data_p + index, "\r\n\r\n", len - index);
    if (p_tmp != NULL) {
        index = p_tmp - data_p;
        if (index >= len) { return PKT_DROP; }
    }

content_data:
    index += 4;
    if (index >= len) { return PKT_DROP; }
    session->main_content_data = (const uint8_t *)data_p + index;

    const char *dp0 = NULL;
    dp0 = mail_memstr(data_p + index, ".\r\n", len - index);
    if (cp1 != NULL && dp0 != NULL) {
        tmp_len = dp0 - data_p;
        if (tmp_len < index) { return PKT_DROP; }
        session->main_content_len = tmp_len - index;
        if (session->main_content_len > MAX_CONTENT_SIZE) {
            session->main_content_len = MAX_CONTENT_SIZE;
        }
    }
    else {
        session->main_content_len = 0;
        session->main_content_data = NULL;
    }

    /* html标签的正文 */
    if (!strncasecmp((const char *)data_p, "text/html", 9)) {
        session->content_has_html_ptr = session->main_content_data;
        session->content_has_html_len = session->main_content_len;
    }


    return PKT_OK;
}
#endif



/*
*pop的邮件的内容用一个单独的eml文件保存，通过tbl日志中的一个字段关联
*/
static void get_pop_filename(uint8_t thread_id, char *name, int len)
{
    get_special_filename(NULL, "pop", "eml", name, len, 1);
}

/*
* 解析Cc的地址与名字, 逗号分开
* ex: Cc: "'Antonio Pelaez'" <<EMAIL>>,
    "'Than Thi Chung'" <<EMAIL>>,
    "'Vu Thi Bich Hop'" <<EMAIL>>,
    "'Le Van Son'" <<EMAIL>>, <<EMAIL>>,
    <<EMAIL>>, "'Pham Thi Lan'" <<EMAIL>>,
    <<EMAIL>>,
    "'Pham Thuy Anh'" <<EMAIL>>,
    "'Nguyen Thi Thuy'" <<EMAIL>>
*/
static int extract_cc_infos(const uint8_t *ptr, uint16_t ptr_len, struct pop_session *session) {

    if (!ptr || !session || !ptr_len)
        return -1;

    uint16_t i = 0, start = 0;
    uint16_t j, k;
    if (!strncasecmp((const char *)ptr, "cc: ", 4))
        i += 4;

    start = i;
    do {
        if (ptr[i] == '>') {
            while (start < i && ptr[start] != '\'' && ptr[start] != '<') start++;

            for (j = start; j <= i; j++) {
                if (ptr[j] == '<') {
                    // Cc address
                    strncat(session->cc_addrs, (char *)(ptr + j + 1), i - (j + 1));
                    strcat(session->cc_addrs, ",");
                    session->cc_addrs_num++;

                    // j = i时 无 alias
                    if (j > start && j < i) {
                        // Cc alias
                        strncat(session->cc_alias, (char *)(ptr + start), j - start);

                        while (1) {

                            char ch = session->cc_alias[strlen(session->cc_alias) - 1];
                            if (ch != '\'')
                                session->cc_alias[strlen(session->cc_alias) - 1] = '\0';
                            else
                                break;
                        }

                        strcat(session->cc_alias, ",");
                        session->cc_alias_num++;
                    }

                    start = i + 1;
                    break;
                }
            }
        }

    } while (i++ < ptr_len);


    return 0;
}

static void get_address_and_alias(const uint8_t *ptr, uint16_t ptr_len, struct pop_session *session) {
    if (!ptr || !session)
        return;

    char alias[300];
    char tmp[2048 + 1];
    const char *token = "<";
    char *p_next = NULL;
    uint16_t i, k;

    memset(alias, 0, sizeof(alias));
    strncpy(alias, (const char *)ptr, ptr_len);
    p_next = strtok(alias, token);

    for (i = 0; i < strlen(alias); i++) {
        if (alias[i] == '<') {
            p_next = &alias[i];
            break;
        }
    }

    if (p_next) {
        if (strlen(p_next) < ptr_len) {
            // 别名
            strncat(session->rcv_aliases, (const char *)ptr, strlen(p_next) - 1);
            strcat(session->rcv_aliases, ",");
            session->rcv_aliases_num++;
        }

        // 地址
        strncat(session->rcv_mails, (const char *)(ptr + (int)strlen(p_next) + 1), ptr_len - ((int)strlen(p_next) + 1));
        strcat(session->rcv_mails, ",");
        session->rcv_mails_num++;

        for (k = (int)strlen(p_next) + 1; k < ptr_len; k++) {
            if (p_next[k] == '@') {
                strncat(session->rcv_doms, (const char *)&p_next[k + 1], ptr_len - k - 1);
                if (session->rcv_doms[strlen(session->rcv_doms) - 1] == '>')
                    session->rcv_doms[strlen(session->rcv_doms) - 1] = '\0';
                strcat(session->rcv_doms, ",");

                session->rcv_doms_num++;
                break;
            }
        }
    }
    else {
        strncat(session->rcv_mails, (const char *)ptr, ptr_len);
        strcat(session->rcv_mails, ",");
        session->rcv_mails_num++;

        for (k = 0; k < ptr_len; k++) {
            if (ptr[k] == '@') {
                strncat(session->rcv_doms, (const char *)&ptr[k + 1], ptr_len - k - 1);
                if (session->rcv_doms[strlen(session->rcv_doms) - 1] == '>')
                    session->rcv_doms[strlen(session->rcv_doms) - 1] = '\0';
                strcat(session->rcv_doms, ",");

                session->rcv_doms_num++;
                break;
            }
        }
    }
}

/*
* 提取To信息
* ex:
To: "'Diep Nguyen'" <<EMAIL>>,
    "'Nguyen thi kieu, Vien'" <<EMAIL>>,
    "'Pernille Friis'" <<EMAIL>>,
    "'Trine Glue Doan'" <<EMAIL>>, <<EMAIL>>
*/
static int extract_to_infos(const uint8_t *ptr, uint16_t ptr_len, struct pop_session *session) {
    if (!ptr || !session || !ptr_len)
        return -1;

    int k = 0;
    int i = 0;
    char alias[300];
    char tmp[2048 + 1];
    char *p_next = NULL;
    char *p_start = NULL;

    strncpy(tmp, (const char *)ptr, ptr_len);
    p_start = tmp;
    p_next = tmp;
    while (i < ptr_len) {

        if (p_next[i] == '>') {
            get_address_and_alias((const uint8_t *)(p_start + 1), p_next + i - (p_start + 1), session);

            p_start = &tmp[i];
            if (p_start[0] != '\"') {

                // update next start
                k = 0;
                while (i + k < ptr_len && p_start[k++] != '\"')
                    ;

                p_start += (k - 1);

                i += k;
                continue;
            }
        }

        i++;
    }

    // 只有一个收件人的邮箱
    if (i == ptr_len) {
        get_address_and_alias(ptr, ptr_len, session);
    }

    return 0;
}

/*
*  根据IP查询ASN与国家
*@ip4_str ex:**************
*/
static int get_region_from_ip(ip2region_t st, const char *ip4_str, IPINFO *info, char *asn, int asn_len) {
    memset(asn, 0, asn_len);
    if (g_config.mmdb_switch == 0)
        return -1;

    if (!ip4_str || !info)
        return -1;

    int i = 0;
    uint8_t ip[4];
    char tmp[64];
    const char *token = ".";
    char *p_next = NULL;

    memset(ip, 0, sizeof(ip));
    snprintf(tmp, sizeof(tmp), "%s", ip4_str);
    p_next = strtok(tmp, token);
    while (p_next) {

        if (i >= 4)
            break;

        ip[i++] = (uint8_t)atoi(p_next);

        if (p_next[0] == '\0')
            break;

        p_next = strtok(NULL, token);
    }

    datablock_entry entry;
    char* s[6]; //国家 地区 省 市县 运营商
    if (ip2region_memory_search(st, get_uint32_ntohl(ip, 0), &entry)) {
        //printf("IP positin: %s\n", entry.region);
        if (entry.region[0] == '0') {
            strcpy(info->country, "中国");

            goto asn_extract;
        }
        int i, j = 0;
        s[j++] = entry.region;
        for (i = 0; entry.region[i]; ++i) {
            if (entry.region[i] == '|') {
                entry.region[i] = '\0';
                s[j++] = entry.region + i + 1;
            }
        }
        if (j < 4) return 0;
        strcpy(info->country, s[0]);
        strcpy(info->area,  s[1]);
        strcpy(info->state, s[2]);
        strcpy(info->city,  s[3]);
    }

asn_extract:
    get_asn_from_ip(ip4_str, asn, asn_len);

    return 0;
}

/*
* 解析Received字段中的 from domain&ip, 并统计数量
* ex: from helg05.formin.fi ([************]) by hels11.mfa.uhnet.fi with Microsoft SMTPSVC(6.0.3790.1830);
*/
static int extract_received_infos(const uint8_t *ptr, uint16_t ptr_len, struct pop_session *session) {

    if (!ptr || !session || !ptr_len)
        return -1;

    struct keys_info {
        uint8_t idx;
        const char *name;
    } targets[] = {
        {1,  "from"},
        {2,  "by"},
        {0,  NULL},
    };

    uint8_t offset = 0;
    uint8_t type = 0;
    uint16_t i;
    uint16_t dom_start, ip_start;
    for (i = 0; i < ptr_len; i++) {
        for (type = 0; targets[type].idx && targets[type].name; type++) {
            if (!strncasecmp((const char *)(ptr + i), targets[type].name, strlen(targets[type].name))) {
                goto recv_start;
            }
        }
    }

recv_start:
    if (i == ptr_len)
        return -1;

    i += strlen(targets[type].name) + 1;
    dom_start = i;

    // from domain
    for (; i < ptr_len; i++) {
        if (ptr[i] == ' ') {
            uint16_t len = i - dom_start;
            const uint8_t *p0 = memstr(ptr + dom_start, ")", i - dom_start);
            if (p0) {
                len = p0 - (ptr + dom_start);
            }

            if (targets[type].idx == 1) {
                strncat(session->received_from_doms, (char *)(ptr + dom_start), len);
                strcat(session->received_from_doms, ",");
                session->received_from_doms_num++;
                ip_start = i;  // update here
                break;
            }
            else if (targets[type].idx == 2) {
                strncat(session->received_by_doms, (char *)(ptr + dom_start), len);
                strcat(session->received_by_doms, ",");
                session->received_by_doms_num++;
                ip_start = i;  // update here
                break;
            }
        }
    }

    // from ip
    for (; i < ptr_len; i++) {
        if (ptr[i] == '[') {
            ip_start = i;  // found here
        }
        else if (ptr[i] == ']') {
            if (ptr[ip_start] == '[') {
                // from ip
                if (targets[type].idx == 1) {
                    strncat(session->received_from_ips, (char *)(ptr + ip_start + 1), i - ip_start - 1);
                    strcat(session->received_from_ips, ",");
                    session->received_from_ips_num++;

                    IPINFO info;
                    char ip[64];
                    memset(ip, 0, sizeof(ip));
                    strncpy(ip, (char *)(ptr + ip_start + 1), i - ip_start - 1);

                    char asn[100];
                    get_region_from_ip(&g_config.entry, ip, &info, asn, sizeof(asn));
                    strcat(session->from_countries, info.country);
                    strcat(session->from_countries, ",");
                    session->from_countries_num++;

                    if (strlen(asn)) {
                        strcat(session->from_asns, asn);
                        strcat(session->from_asns, ",");
                        session->from_asns_num++;
                    }
                }
                // by ip
                else if (targets[type].idx == 2) {
                    strncat(session->received_by_ips, (char *)(ptr + ip_start + 1), i - ip_start - 1);
                    strcat(session->received_by_ips, ",");
                    session->received_by_ips_num++;
                }
            }
            break;
        }
    }


    return 0;
}


//解决某个字段值有多行的情况(每行都具有明确的 "\r\n"): 确定该字段值的长度
//适用条件: 该字段完整值之后(包括"\r\n")一定是含有 ": " 或 "\r\n\r\n" 的一整行
//          该字段到这一行首个元素的地址偏移量上所有字符(可再减2去掉最后的一个"\r\n"),
//            均作为该字段的值，本函数的返回值即是这个偏移量,代表该字段值的长度
static uint16_t get_pop_field(const uint8_t *payload, uint32_t payload_len, const uint8_t** field)
{
    uint16_t field_len = 0;
    const uint8_t* tmp = *field;
    uint16_t check_len = MAX_CHECK_LEN;
    if (tmp && tmp - payload < payload_len)
    {
        const uint8_t* p0 = (uint8_t*)strstr((const char*)tmp, "\r\n");
        const uint8_t* p1 = (uint8_t*)strstr((const char*)tmp, ": ");
        const uint8_t* p2 = (uint8_t*)strstr((const char*)tmp, "\r\n\r\n");
        if (p1 && p2 && p1 > p2)        //最后一个标准字段
        {
            field_len = p2 - tmp;
            return field_len < payload_len ? field_len : 0;
        }
        if (p0 && p1 && p2 && p0 < p1 && p1 < p2)
        {
            const uint8_t* p2 = (uint8_t*)strstr((const char*)p1, "\r\n");
            if (p2)
            {
                field_len = p0 - tmp + 2;
                int len = tmp - payload;
                while (p0 && (unsigned)(len + field_len) < payload_len && p0 < p1)
                {
                    if (check_len > (len - field_len)) {
                        check_len = len - field_len;
                    }
                    p0 = (const uint8_t*)mail_memstr((const char*)tmp + field_len, "\r\n", check_len);
                    if (!p0) break;
                    if (p0 >= p2)
                    {
                        field_len -= 2;
                        break;
                    }
                    field_len = p0 - tmp + 2;
                    if (field_len <= 0 || field_len >= payload_len) {
                        break;
                    }
                    p0 = tmp + field_len;
                    check_len = MAX_CHECK_LEN;
                }
                return field_len < payload_len ? field_len : 0;
            }
        }
    }
    *field = NULL;
    return 0;
}

/*
*解析邮件内容，只解析头部，邮件具体内容保存在文件中
*/
static void dissect_imf(const uint8_t *payload, uint32_t payload_len, struct pop_session *session)
{
    uint32_t offset = 0;
    const uint8_t *line;
    const uint8_t *coma_ptr;
    const uint8_t *split_head , *split_tail;
    session->unknown_line_num = 0;
    int line_len;
    int line_num;
    int flag = 1;
    int empty_line_index;
    uint16_t *last_val_len_ptr = NULL;

    session->unknown_line_num = 0;

    empty_line_index = _find_empty_line(payload, payload_len);
    if (empty_line_index < 0)
        empty_line_index = payload_len;


    if (empty_line_index < (int)payload_len)
        session->mail_num = 2;
    else
        session->mail_num = 1;

    // 正文前的一些命令解析
    int cmd_end;
    int cmd_line_len;
    const uint8_t *cmd_line;
    uint32_t cmd_off = 0;
    const uint8_t *pto = NULL;

    cmd_end = empty_line_index;
    cmd_line = payload;
    while (offset < (uint32_t)cmd_end)
    {
        cmd_line_len = find_packet_line_end(cmd_line, cmd_end - offset);
        if (cmd_line_len <= 0)
            break;
        else
        {
            // login server
            if (session->login_svr_ptr) {
                pto = memstr(cmd_line, "Pop3 Server ", cmd_line_len);
                if (!pto) {
                    pto = memstr(cmd_line, "Imap Server ", cmd_line_len);
                }
                if (pto && pto + 12) {
                    pto = memstr(pto + 12, "(", cmd_line_len - 12);
                }
                if (pto)
                {
                    session->login_svr_ptr = pto;
                    session->login_svr_len = payload + cmd_line_len - pto;
                }
            }

            int i_set = 0;
            const char *p_set;
            while ((p_set = email_heads[i_set++])) {
                if (cmd_line_len >= (int)strlen(p_set) && strncasecmp((const char *)cmd_line, p_set, strlen(p_set)) == 0) {
                    strcat(session->head_sets, p_set);
                    strcat(session->head_sets, ",");
                    session->head_sets_num++;
                }
            }

            uint16_t space_index = 0;
            while (space_index < cmd_line_len) {
                if (cmd_line[space_index++] == ' ') {
                    int i_cmd = 0;
                    int i_line;
                    const char *p_cmd;
                    while ((p_cmd = email_cmds[i_cmd++])) {
                        if (cmd_line_len >= (int)strlen(p_cmd)) {
                            if (strncasecmp((const char *)(cmd_line + space_index), p_cmd, strlen(p_cmd)) == 0) {
                                strcat(session->commands, p_cmd);
                                strcat(session->commands, ",");
                                session->commands_num++;

                                if (session->starttls_f == 0 && !strncasecmp("starttls", p_cmd, strlen(p_cmd)) == 0)
                                    session->starttls_f = 1;

                                // HELO EHLO 后面紧跟服务器
                                if (!session->host_ptr) {
                                    if (!strcmp((const char *)(cmd_line + space_index), "HELO ")
                                        || !strcmp((const char *)(cmd_line + space_index), "EHLO ")) {
                                        session->host_ptr = cmd_line + space_index + 5;
                                        session->host_len = payload + cmd_line_len - (cmd_line + space_index + 5);
                                    }
                                }

                                // USER
                                if (!strcmp((const char *)(cmd_line + space_index), "USER ")) {
                                    strncpy(session->auth_name, (const char *)(cmd_line + space_index + 5), cmd_line_len - space_index);
                                }
                                else if (!strcmp((const char *)(cmd_line + space_index), "PASS ")) {
                                    strncpy(session->auth_passwd, (const char *)(cmd_line + space_index + 5), cmd_line_len - space_index);
                                }
                            }
                        }
                    }

                    break;
                }
            }

            if (cmd_line_len > 13 && strncasecmp((const char *)cmd_line, "Content-Type:", 13) == 0) {
                session->content_type = cmd_line + 14;

                if ((pto = memstr(cmd_line, ";", cmd_line_len))) {
                    session->content_type_len = pto - session->content_type;

                    strncat(session->cont_types, (const char *)(cmd_line + 14), session->content_type_len);
                    strcat(session->cont_types, ",");
                    session->cont_types_num++;

                }
                session->content_type_len = cmd_line_len - 14;
                if (session->content_type != NULL &&
                    ((strncasecmp((const char *)session->content_type, "text/plain", 10) == 0) ||
                    (strncasecmp((const char *)session->content_type, "text/html", 9) == 0))
                    ) {

                    uint16_t main_len = session->content_type - payload;
                    uint16_t mm_len = 0;
                    if (main_len < payload_len)
                        mm_len = payload_len - main_len;

                    if (session->main_content_len == 0 && mm_len > 0 && mm_len < payload_len)
                        get_pop_main_content((const char *)session->content_type, mm_len, session);
                }

            }
        }

        cmd_off += cmd_line_len + 2;
        cmd_line = payload + cmd_off;
        if(cmd_off >= payload_len) //越界跳出
        {
            break;
        }
    }


    line = payload;
    while (offset < (uint32_t)empty_line_index) {
        line_len = find_packet_line_end(line, empty_line_index - offset);

        if (line_len < 0 || line_len>(int)payload_len)
            break;
        line_num = 1;
        while( (offset + line_len + 2 < (unsigned)empty_line_index) && ( line[line_len+2] == '\t' || line[line_len+2] == ' ' ) ){
            line_num += 1;
            if (find_packet_line_end(line + line_len + 2,empty_line_index - offset - line_len - 2) > 0){
                line_len = find_packet_line_end(line + line_len + 2,empty_line_index - offset - line_len -2) + line_len + 2;
                if(line_len>(int)payload_len){
                    break;
                }
            }
            else
                break;
        }
        if ((coma_ptr = (uint8_t*)memchr((const char*)line, ':', line_len))) {
            if (line_len > 10 && strncasecmp((const char *)line, "MAIL FROM:", 10) == 0) {
                session->mail_mail_from_ptr = line + 11;
                session->mail_mail_from_len = line_len - 11;
                last_val_len_ptr = &session->mail_mail_from_len;
            }
            else if (line_len > 5 && strncasecmp((const char *)line, "Date:", 5) == 0) {
                session->mail_date_ptr = line + 6;
                session->mail_date_len = line_len - 6;
                session->delivery_date = session->delivery_date,
                session->delivery_date_len = session->delivery_date_len;
                last_val_len_ptr = &session->mail_date_len;
                strcpy(session->content_encoding, "GB2312");
            }
            else if (line_len > 5 && strncasecmp((const char *)line, "From:", 5) == 0) {
                if ((split_head = (uint8_t*)memchr(line, '<', line_len)) && (split_tail = (uint8_t*)memchr(line, '>', line_len))) {
                    session->mail_from_ptr = split_head + 1;
                    session->mail_from_len = split_tail - split_head -1;
                    session->sender = line + 5;
                    session->sender_len = split_head - line - 5;
                }
                else {
                    session->mail_from_ptr = line + 5;
                    session->mail_from_len = line_len - 5;
                    if( (split_head = (uint8_t*)memchr((const char*)line,'@',line_len)) ){
                        session->sender = line + 5;
                        session->sender_len = split_head - line -5;
                    }
                }
                last_val_len_ptr = &session->mail_from_len;
            }
            else if (line_len > 11 && strncasecmp((const char *)line, "Message-ID:", 11) == 0) {
                session->message_id = line + 12;
                session->message_id_len = line_len - 12;
                last_val_len_ptr = &session->message_id_len;

                strncat(session->msg_ids, (const char *)(line + 12), line_len - 12);
                strcat(session->msg_ids, ",");
                session->msg_ids_num++;
            }
            else if (line_len > 17 && strncasecmp((const char *)line, "X-ATTACHMENT-NUM:", 17) == 0) {
                if (line[17] == ' ') {
                    session->attach_num = line + 18;
                    session->attach_num_len = line_len - 18;
                } else {
                    session->attach_num = line + 17;
                    session->attach_num_len = line_len - 17;
                }
                last_val_len_ptr = &session->has_attach_len;
            } else if (line_len > 13 && strncasecmp((const char *)line,"X-Has-Attach:",13) == 0) {
                if (line[13] == ' ') {
                    session->has_attach = line + 14;
                    session->has_attach_len = line_len - 14;
                } else {
                    session->has_attach = line + 13;
                    session->has_attach_len = line_len - 13;
                }
                last_val_len_ptr = &session->has_attach_len;
            } else if (line_len > 7 && strncasecmp((const char *)line, "X-GUID:", 7) == 0) {
                if (line[7] == ' ') {
                    session->x_guid = line + 8;
                    session->x_guid_len = line_len - 8;
                } else {
                    session->x_guid = line + 7;
                    session->x_guid_len = line_len - 7;
                }
                last_val_len_ptr = &session->x_guid_len;
            } else if (line_len > 12 && strncasecmp((const char *)line, "X-SENDER-IP:", 12) == 0) {
                if (line[12] == ' ') {
                    session->sender_ip = line + 13;
                    session->sender_ip_len = line_len - 13;
                } else {
                    session->sender_ip = line + 12;
                    session->sender_ip_len = line_len - 12;
                }
                last_val_len_ptr = &session->sender_ip_len;
            } else if (line_len > 12 && strncasecmp((const char *)line, "In-Reply-To:", 12) == 0) {
                session->in_reply_to = line + 13;
                session->in_reply_to_len = line_len - 13;
                last_val_len_ptr = &session->in_reply_to_len;
            } else if (line_len > 11 && strncasecmp((const char *)line, "References:", 11) == 0) {
                session->references = line + 12;
                session->references_len = line_len - 12;
                last_val_len_ptr = &session->references_len;
            } else if (line_len > 9 && strncasecmp((const char *)line, "Received:", 9) == 0) {
                /*if(flag == 1){ //只对第一个Received起作用
                    if( memchr(line + 9,';',line_len -9) ){
                        session->expiry_date = (uint8_t*)memchr(line + 9,';',line_len -9) + 1;
                        session->expiry_date_len = (line + line_len) - session->expiry_date;
                    }
                }
                if ((split_head = (const uint8_t*)mail_memstr((const char*)line,"from",line_len))
                           && (split_tail = (const uint8_t*)mail_memstr((const char*)line,"\r\n",line_len + 2))
                           && (split_tail > split_head)  ){
                    session->sender_host = split_head + 4;
                    session->sender_host_len = split_tail - split_head - 4;
                    if ((split_head = (const uint8_t*)memchr(split_head,'[',split_tail - split_head))
                               && (split_tail > split_head)
                               && (split_tail = (const uint8_t*)memchr(split_head,']',split_tail - split_head))){
                        session->sender_ip = split_head + 1;
                        session->sender_ip_len = split_tail - split_head - 1;
                    }
                }
                if ((split_head = (const uint8_t*)mail_memstr((const char*)line,"by",line_len)) && (split_tail = (const uint8_t*)mail_memstr((const char*)line,"with",line_len))){
                    session->resent_agent = split_head + 2;
                    session->resent_agent_len = split_tail - split_head - 2;
                } else {
                    session->resent_agent = (const uint8_t*)"SMTP";
                    session->resent_agent_len = 4;
                }
                flag += 1;
                */
                session->mail_received_ptr = line + 9;
                session->mail_received_len = get_pop_field(payload, payload_len, &session->mail_received_ptr);
                //此处不能用下面的偏移量,只能在这里完成偏移
                offset += session->mail_received_len + 2;
                line = &payload[offset];

                strncat(session->receiveds, (const char *)session->mail_received_ptr, session->mail_received_len);
                strcat(session->receiveds, ",");
                session->receiveds_num++;

                extract_received_infos(session->mail_received_ptr, session->mail_received_len, session);

                continue;

            }
            else if (line_len > 9 && strncasecmp((const char *)line, "Reply-To:", 9) == 0) {
                session->reply_to = line + 10;
                session->reply_to_len = line_len - 10;
                last_val_len_ptr = &session->reply_to_len;
            } else if (line_len > 11 && strncasecmp((const char *)line, "Resent-Date:", 11) == 0) {
                if (line[11] == ' ') {
                    session->resent_date = line + 12;
                    session->resent_date_len = line_len - 12;
                } else {
                    session->resent_date = line + 1;
                    session->resent_date_len = line_len - 11;
                }
                last_val_len_ptr = &session->resent_date_len;
            } else if (line_len > 9 && strncasecmp((const char *)line, "X-Mailer:", 9) == 0) {
                if (line[9] == ' ') {
                    session->x_mailer = line + 10;
                    session->x_mailer_len = line_len - 10;
                } else {
                    session->x_mailer = line + 9;
                    session->x_mailer_len = line_len - 9;
                }
                last_val_len_ptr = &session->x_mailer_len;

                strncat(session->x_mailers, (const char *)(line + 10), line_len - 10);
                strcat(session->x_mailers, ",");
                session->x_mailers_num++;

            }
            else if (line_len > 11 && strncasecmp((const char *)line, "User-Agent:", 11) == 0) {
                if (line_num == 1) {
                    session->user_agent = line + 12;
                    session->user_agent_len = line_len - 12;
                } else {
                    if( (split_head = (const uint8_t*)mail_memstr((const char*)line, "\r\n", line_len)) ){
                        session->user_agent = line + 12;
                        session->user_agent_len = split_head - line - 11;
                        session->user_app =  split_head + 3;
                        session->user_app_len = line_len - session->user_agent_len - 14;
                    }
                }
                last_val_len_ptr = &session->user_agent_len;
            } else if (line_len > 12 && strncasecmp((const char *)line, "Return-Path:", 12) == 0) {
                session->return_path = line + 13;
                session->return_path_len = line_len - 13;
                last_val_len_ptr = &session->return_path_len;
            } else if (line_len > 13 && strncasecmp((const char *)line, "Mime-Version:", 13) == 0) {
                session->mime_version = line + 14;
                session->mime_version_len = line_len - 14;
                last_val_len_ptr = &session->mime_version_len;

                strncat(session->mime_vers, (const char *)(line + 14), line_len - 14);
                strcat(session->mime_vers, ",");
                session->mime_vers_num++;
            }
            else if (line_len > 16 && strncasecmp((const char *)line, "Accept-Language:", 16) == 0) {
                session->accept_language = line + 17;
                session->accept_language_len = line_len - 17;
                last_val_len_ptr = &session->accept_language_len;
            } else if (line_len > 17 && strncasecmp((const char *)line, "Content-Language:", 17) == 0) {
                session->content_language = line + 18;
                session->content_language_len = line_len - 18;
                last_val_len_ptr = &session->content_language_len;
            }
            /*else if (line_len > 13 && strncasecmp((const char *)line, "Content-Type:", 13) == 0) {
                session->content_type = line + 14;
                session->content_type_len = line_len - 14;
                uint16_t main_len=session->content_type-payload;
                uint16_t mm_len=0;
                if(main_len<payload_len)
                    mm_len=payload_len-main_len;
                if(session->content_type!=NULL &&
                    ((strncasecmp((const char *)session->content_type,"text/plain",10) == 0) ||
                    (strncasecmp((const char *)session->content_type,"text/html",9) == 0))
                    ){
                    if(session->main_content_len==0 && mm_len>0 && mm_len<payload_len)
                        get_pop_main_content((const char *)session->content_type, mm_len, session);
                }

            }*/
            else if (line_len > 3 && strncasecmp((const char *)line, "To:", 3) == 0) {
                if (line_num == 1){
                        session->receiver_type = (const uint8_t*)"Single";
                        session->receiver_type_len = 6;
                } else {
                        session->receiver_type = (const uint8_t*)"Group";
                        session->receiver_type_len = 5;
                }

                session->mail_to_ptr = line + 4;
                int left_len = payload_len - (line - payload) - 4;

                if (session->mail_to_ptr) {
                    // 获取收件人别名与地址
                    extract_to_infos(session->mail_to_ptr, line_len - 4, session);
                }

                const uint8_t* p1 = (const uint8_t*)mail_memstr((const char*)session->mail_to_ptr, ": ", left_len);
                if (p1)
                {
                    int pop_len = p1 - session->mail_to_ptr - 1;
                    pop_len = (unsigned)pop_len < payload_len ? pop_len : 0;
                    for(;pop_len> 0 && session->mail_to_ptr[pop_len] != '\r'; pop_len--);
                    session->mail_to_len = pop_len;

                    session->receiver = session->mail_to_ptr;
                    session->receiver_len = session->mail_to_len;
                    last_val_len_ptr = &session->mail_to_len;
                }else{
                    if ((split_head = memchr(line,'<',line_len)) && (split_tail = memchr(line,'>',line_len))) {
                        session->mail_to_ptr = split_head + 1;
                        session->mail_to_len = split_tail - split_head -1;
                        session->receiver = session->mail_to_ptr;
                        session->receiver_len = session->mail_to_len;
                    } else {
                        session->mail_to_ptr = line + 3;
                        session->mail_to_len = line_len - 3;
                        if ( (split_head = memchr(line,'@',line_len)) ){
                            session->receiver = line + 3;
                            session->receiver_len = split_head - line - 3;
                        }
                    }
                }

                last_val_len_ptr = &session->mail_to_len;

            } else if (line_len > 28 && strncasecmp((const char *)line, "Disposition-Notification-To:",28) == 0) {
                session->disposition_notification_to = line + 29;
                session->disposition_notification_to_len = line_len - 29;
                last_val_len_ptr = &session->disposition_notification_to_len;
            } else if (line_len > 3 && strncasecmp((const char *)line, "Cc:", 3) == 0) {
                int move_len=0;
                if (line[3] == ' ') {
                    session->mail_cc_ptr = line + 4;
                    session->mail_cc_len = line_len - 4;
                    move_len=4;
                } else {
                    session->mail_cc_ptr = line + 3;
                    session->mail_cc_len = line_len - 3;
                    move_len=3;
                }
                /*
                const uint8_t* p1 = (uint8_t*)strstr((const char*)session->mail_cc_ptr,": ");
                if(p1)
                {
                    int len = p1 - session->mail_cc_ptr - 1;
                    len = (unsigned)len < payload_len ? len : 0;
                    for(;len > 0 && session->mail_cc_ptr[len] != '\r'; len--);
                    session->mail_cc_len = len;
                }else{
                    session->mail_cc_len = line_len - move_len;
                }*/

                last_val_len_ptr = &session->mail_cc_len;

                extract_cc_infos(line + 4, line_len - 4, session);

            }
            else if (line_len > 11 && strncasecmp((const char *)line, "Precedence:", 11) == 0) {
                session->precedence = line + 12;
                session->precedence_len = line_len - 12;
                last_val_len_ptr = &session->precedence_len;
            } else if (line_len > 11 && strncasecmp((const char *)line, "X-Priority:", 11) == 0) {
                if (line[11] == ' ') {
                    session->priority = line + 12;
                    session->priority_len = line_len - 12;
                } else {
                    session->priority = line + 11;
                    session->priority_len = line_len - 11;
                }
                last_val_len_ptr = &session->mail_subject_len;
            } else if (line_len > 8 && strncasecmp((const char *)line, "Subject:", 8) == 0) {
                session->mail_subject_ptr = line + 9;
                session->mail_subject_len = line_len - 9;
                last_val_len_ptr = &session->mail_subject_len;

                strncat(session->subjects, (const char *)(line + 9), line_len - 9);
                strcat(session->subjects, ",");
                session->subjects_num++;
            }
            else if (line_len > 15 && strncasecmp((const char *)line, "delivery-date: ", 15) == 0) {
                session->mail_delivery_date_ptr = line + 15;
                session->mail_delivery_date_len = line_len - 15;
            }
            else if (line_len > 5 && strncasecmp((const char *)line, "Bcc: ", 5) == 0) {
                session->mail_bcc_ptr = line + 5;
                session->mail_bcc_len = line_len - 5;
            }
            else if (line_len > 27 && strncasecmp((const char *)line, "content-transfer-encoding: ", 27) == 0) {
                session->mail_content_transfer_encoding_ptr = line + 27;
                session->mail_content_transfer_encoding_len = line_len - 27;
            }
            else if (line_len > 14 && strncasecmp((const char *)line, "delivered-to: ", 14) == 0) {
                session->mail_delivered_to_ptr = line + 14;
                session->mail_delivered_to_len = line_len - 14;
            }
            else if (line_len > 18 && strncasecmp((const char *)line, "x-originating-ip: ", 18) == 0) {
                session->x_ori_ip_ptr = line + 18;
                session->x_ori_ip_len = line_len - 18;
            }
            else {
                if (session->unknown_line_num < POP_UNKNOWN_LINE_NUM_MAX) {
                    int key_len = coma_ptr - line;
                    session->unknown_line[session->unknown_line_num].key_ptr = line;
                    session->unknown_line[session->unknown_line_num].key_len = key_len;

                    if (key_len + 1  < line_len) {
                            session->unknown_line[session->unknown_line_num].val_ptr = coma_ptr + 1;
                            session->unknown_line[session->unknown_line_num].val_len = (line_len - key_len - 1) < 256 ? (line_len - key_len - 1) : 256;
                    } else {
                        session->unknown_line[session->unknown_line_num].val_ptr = coma_ptr + 2;
                        session->unknown_line[session->unknown_line_num].val_len = 0;
                    }
                    last_val_len_ptr = &session->unknown_line[session->unknown_line_num].val_len;

                    session->unknown_line_num++;
                }
            }
        }
        offset += line_len + 2;
        line = &payload[offset];
    }

    //取附件...
    while((unsigned)(empty_line_index + 5) < payload_len){
        empty_line_index += 4;
        line = payload + empty_line_index;
        if ((line_len = _find_empty_line(line, payload_len - (line - payload))) <= 0)
            break;
        else if (line_len > 31){
            if (mail_memstr((const char*)line, "Content-Disposition: attachment", line_len)) {
                if ((coma_ptr = (const uint8_t*)mail_memstr((const char*)line, "Content-MD5:", line_len))) {
                    uint16_t m_len = (const uint8_t*)memchr(coma_ptr, '\r', line_len) - coma_ptr - 12;
                    if (m_len) {
                        session->attachent_md5_num++;
                        memcpy((void*)session->attachment_list[session->attachment_filename_num].attachment_content_md5
                            , (const void*)(coma_ptr + 12), m_len < POP_CONTENT_MD5_LEN ? m_len : POP_CONTENT_MD5_LEN - 1);
                    }
                }

                if ((coma_ptr = (const uint8_t*)mail_memstr((const char*)line, "Content-Transfer-Encoding:", line_len))) {
                    uint16_t m_len = (const uint8_t*)memchr(coma_ptr, '\r', line_len) - coma_ptr - 26;
                    if (m_len) {
                        memcpy((void*)session->attachment_list[session->attachment_filename_num].attachment_content_transfer_encoding
                            , (const void*)(coma_ptr + 26), m_len < POP_CONTENT_MD5_LEN ? m_len : POP_CONTENT_MD5_LEN - 1);
                    }
                }

                if( (coma_ptr = (const uint8_t*)mail_memstr((const char*)line, "Content-Type:",line_len)) ){
                    session->attach_type = coma_ptr + 13;
                    session->attach_type_len = (const uint8_t*)memchr(coma_ptr,'\r',line_len) - coma_ptr - 13;
                    strncat(session->body_type, (const char *)session->attach_type, session->attach_type_len);
                    strcat(session->body_type, ",");
                    session->body_type_num++;
                    memcpy((void*)session->attachment_list[session->attachment_filename_num].attachment_content_type
                            , (const void*)session->attach_type, session->attach_type_len < POP_CONTENT_TYPE_LEN ? session->attach_type_len : POP_CONTENT_TYPE_LEN - 1);

                    #if 1   //提取主体内容 add by liugh
                    uint32_t now_len=line-payload+14;
                    uint32_t left_len=payload_len-now_len;
                    if(left_len >0 &&
                        ((strncasecmp((const char *)line+14,"text/plain",10) == 0) ||
                         (strncasecmp((const char *)line+14,"text/html",9) == 0))
                    ){
                        if(session->main_content_len==0 && left_len>0 && left_len<payload_len)
                            get_pop_main_content((const char *)(const char *)line+14, left_len, session);
                    }
                    #endif

                }
                if( (coma_ptr = (const uint8_t*)mail_memstr((const char*)line, "filename", line_len)) ){
                    session->attach_name = coma_ptr + 9;
                    session->attach_name_len = (const uint8_t*)memchr(coma_ptr,'\r',line_len - empty_line_index) - coma_ptr - 9;
                    memcpy((void*)session->attachment_list[session->attachment_filename_num].attachment_filename
                                , (const void*)session->attach_name, session->attach_name_len < POP_FILE_NAME_LEN ? session->attach_name_len : POP_FILE_NAME_LEN - 1);

                }
                session->attach_len = payload_len - empty_line_index;
                session->attachment_list[session->attachment_filename_num++].attachment_len = session->attach_len;

                break;
            }
        }
        empty_line_index += line_len;
    }
    return;
}

/*多个邮件分割*/
static uint32_t pop_seperate_imf(const uint8_t *payload, uint32_t max_len)
{
    uint8_t line_num = 0;
    uint32_t offset = 0;
    const uint8_t *line=NULL;
    int line_len;
    int copy_len;
    char line_str[1500];
    uint32_t tmp_len=0;

    line = payload;
    while (offset < max_len) {
        //line_len = find_packet_line_end(line, max_len - offset);
        line_len=_find_email_end_line(line, max_len - offset);
        if (line_len < 0){
            break;
        }

        offset+=line_len+5;
        tmp_len=line_len+5;
        if(offset+3>=max_len){
            break;
        }
        if (0 == memcmp((const char*)(&line[tmp_len]), "+OK", 3)){
            return (offset);
        }

        line = &payload[offset];
        tmp_len=0;
    }

    return (max_len);
}

static int write_pop_eml(struct flow_info *flow, int direction) {
    EmailSession *session = NULL;
    session = (EmailSession *)flow->app_session;
    if (session == NULL)
        return 0;

    struct Emailcache *c = session->cache + 2;
    uint32_t           index = 0;
    uint32_t           target_id = 0;
    uint8_t           *imf_start = (uint8_t *)c->cache;
    uint32_t           imf_max_len = c->cache_hold;
    uint32_t           imf_len = 0;
    session->mail_num += 1;

    while (imf_max_len && imf_max_len >= 5) {
        imf_len = pop_seperate_imf(imf_start, imf_max_len);

        if (imf_len < 256 || !dpi_hybrid_strnstr((const char *)imf_start, imf_len, "Received") ||
            !dpi_hybrid_strnstr((const char *)imf_start, imf_len, "To")) {
            imf_start += imf_len;
            imf_max_len -= imf_len;
            continue;
        }
        dpi_email_t *email = dpi_email_create();
        dpi_email_imf(email, NULL, (const char *)imf_start, imf_len);
        if (g_config.pop_content) {
            get_pop_filename(flow->thread_id, session->mail_filename, sizeof(session->mail_filename));
            FILE *fp = fopen(session->mail_filename, "w");
            if (fp) {
                fwrite(imf_start, imf_len, 1, fp);
                fclose(fp);
                session->eml_filesize = imf_len;
            } else
                log_trace("can't open pop mail file");
        }

        strcpy(session->email_type, "POP");
        write_email_log(flow, direction, email);
        dpi_email_destory(email);
        imf_start += imf_len;
        imf_max_len -= imf_len;
    }
    return 0;
}

static int identify_pop(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len)
{
    int line_len;
    if (g_config.protocol_switch[PROTOCOL_MAIL_POP] == 0)
        return PROTOCOL_UNKNOWN;

    if (payload_len > 2 && get_uint16_ntohs(payload, payload_len - 2) == 0x0d0a) {
        if (flow->pkt_first_line.has_search == 0) {
            line_len = find_packet_line_end(payload, payload_len);
            flow->pkt_first_line.has_search = 1;
            flow->pkt_first_line.linelen = line_len;
        } else {
            line_len = flow->pkt_first_line.linelen;
        }

        if (line_len >= 3 && strncasecmp((const char *)payload, "+OK", 3) == 0) {
            flow->real_protocol_id = PROTOCOL_MAIL_POP;
            return PROTOCOL_MAIL_POP;
        } else if (line_len >= 4 && strncasecmp((const char *)payload, "-ERR", 4) == 0) {
            flow->real_protocol_id = PROTOCOL_MAIL_POP;
            return PROTOCOL_MAIL_POP;
        }

        if (line_len == 4 && strncasecmp((const char *)payload, "CAPA", 4) == 0) {
            flow->real_protocol_id = PROTOCOL_MAIL_POP;
            return PROTOCOL_MAIL_POP;
        } else if (line_len >= 5 && strncasecmp((const char *)payload, "USER ", 5) == 0) {
            flow->real_protocol_id = PROTOCOL_MAIL_POP;
            return PROTOCOL_MAIL_POP;
        } else if (line_len >= 5 && strncasecmp((const char *)payload, "PASS ", 5) == 0) {
            flow->real_protocol_id = PROTOCOL_MAIL_POP;
            return PROTOCOL_MAIL_POP;
        } else if (line_len == 4 && strncasecmp((const char *)payload, "STAT", 4) == 0) {
            flow->real_protocol_id = PROTOCOL_MAIL_POP;
            return PROTOCOL_MAIL_POP;
        } else if (line_len == 4 && strncasecmp((const char *)payload, "LIST", 4) == 0) {
            flow->real_protocol_id = PROTOCOL_MAIL_POP;
            return PROTOCOL_MAIL_POP;
        } else if (line_len == 4 && strncasecmp((const char *)payload, "UIDL", 4) == 0) {
            flow->real_protocol_id = PROTOCOL_MAIL_POP;
            return PROTOCOL_MAIL_POP;
        } else if (line_len > 5 && strncasecmp((const char *)payload, "RETR ", 5) == 0) {
            flow->real_protocol_id = PROTOCOL_MAIL_POP;
            return PROTOCOL_MAIL_POP;
        } else if (line_len > 5 && strncasecmp((const char *)payload, "DELE ", 5) == 0) {
            flow->real_protocol_id = PROTOCOL_MAIL_POP;
            return PROTOCOL_MAIL_POP;
        }
    }

    return PROTOCOL_UNKNOWN;
}

/*
*pop协议的解析函数
        主要是根据每个数据包的命令得到pop的当前状态，并缓存一些信息在会话的app_session中
*/
static int dissect_pop_rsm(struct flow_info *flow, uint8_t direction, const uint8_t *payload, uint32_t payload_len) {
    char           t = 0;
    int64_t        hl = 0;
    int64_t        offset = 0;
    const uint8_t *line;
    int            line_len;
    uint16_t       copy_len;
    int64_t        l = payload_len;
    uint32_t token_len = 0;
    int            space_index = 0;
    int            cmd_index = 0;

    const uint8_t *next_token = NULL;
    int command_len = 0;
    const uint8_t *command_token = NULL;
    char command[64] = { 0 };

    EmailSession *session = NULL;
    uint8_t       is_request = 0;

    struct Emailcache *c = NULL;
    char               _str[1024] = {0};
    int                cache_offset = direction;
    if (NULL == flow->app_session) {
        flow->app_session = dpi_malloc(sizeof(EmailSession));
        if (NULL == flow->app_session) {
            goto EMAIL_NEED_MORE_PKT;
        }
        memset(flow->app_session, 0, sizeof(EmailSession));
        session = (EmailSession *)flow->app_session;
        session->complemail = 1;
    }
    session = (EmailSession *)flow->app_session;
    c = session->cache + direction;

    if (direction == FLOW_DIR_SRC2DST) {
        is_request = 1;
    } else {
        is_request = 0;
    }

    if (is_request) {
        switch (session->state) {
            case EMAIL_STATE_READING_CMDS:
        {
          line = payload;
          line_len = find_packet_line_end(line, payload_len);
          token_len = dpi_get_token_len(payload, payload + line_len, &next_token);

          if (token_len != 0) {
                        command_len = token_len;
                        command_token = payload;

                        if (command_len > 0 && command_len < 64) {
                            strncpy(command, (const char *)command_token, command_len);
                            dpi_str_join(session->commands, command, ",");
                        }
          }
          if (line_len <= 0) {
                        DPI_LOG(DPI_LOG_WARNING, "no end line when reading pop cmd");
                        return PKT_OK;
          }
          if (line_len > 5 && strncasecmp((const char *)line, "USER ", 5) == 0) { /*USER username*/
                        copy_len = (uint32_t)line_len - 5 >= sizeof(session->auth_name) ? sizeof(session->auth_name) - 1
                                                                                        : (uint32_t)line_len - 5;
                        strncpy(session->auth_name, (const char *)line + 5, copy_len);
                        // printf("autoname = %s\n", session->auth_name);
                        // session->user_name[copy_len] = 0;
                        // session->begin_time = time(0);
                        // session->state = EMAIL_STATE_READING_STATUS;
          } else if (line_len > 5 && strncasecmp((const char *)line, "PASS ", 5) == 0) { /*PASS password*/
                        copy_len = (uint32_t)line_len - 5 >= sizeof(session->auth_passwd) ? sizeof(session->auth_passwd) - 1
                                                                                            : (uint32_t)line_len - 5;
                        strncpy(session->auth_passwd, (const char *)line + 5, copy_len);
                        // printf("autopassword = %s\n", session->auth_passwd);
                        // session->user_passwd[copy_len] = 0;
                        session->state = EMAIL_STATE_READING_STATUS;
                        //write_pop_log(flow, direction);
          } else if (line_len >= 4 && strncasecmp((const char *)line, "RETR", 4) == 0) { /*download mail*/
                        session->state = EMAIL_STATE_READING_DATA;
                        flow->reassemble_flag = 1; /* for mail out-of-order */
                        goto EMAIL_NEED_MORE_PKT;
          } else if (line_len >= 4 && strncasecmp((const char *)line, "QUIT", 4) == 0) {
                        session->state = EMAIL_STATE_READING_CMDS;
          } else if (line_len >= 4 && strncasecmp((const char *)line, "STAT", 4) == 0) {
                        session->state = EMAIL_STATE_READING_CMDS;
          }
        }
        break;
            case EMAIL_STATE_READING_DATA:
                break;
            default:
                session->state = EMAIL_STATE_READING_CMDS;
                break;
        }
    } else if (payload_len > 5) {
        line = payload;

        if (session->state == EMAIL_STATE_READING_CMDS) {
            if (dpi_hybrid_strnstr((char *)line, payload_len,"Received:") != NULL) {
                session->state = EMAIL_STATE_READING_DATA;
                flow->reassemble_flag = 1;
            }else if(strncasecmp((const char *)payload,"+OK ",4)==0){
            line = payload;
            line_len = find_packet_line_end(line, payload_len);
            if(line_len > 5 && strlen(session->auth_name)>0 && line_len>(int)strlen(session->auth_name)){
                if(memmem(line, line_len, session->auth_name,strlen(session->auth_name))!=NULL){
                    strncpy(session->login_status,"YES",COMMON_STATUS_LEN);
                    // write_email_log(flow, direction, NULL);
                }
            }
        }else if(strncasecmp((const char *)payload,"-ERR ",5)==0){
            line = payload;
            line_len = find_packet_line_end(line, payload_len);
            if(line_len > 5  && strlen(session->auth_name)>0 &&
               memmem(line, line_len, "Unable to log on",strlen("Unable to log on"))!=NULL){
                strncpy(session->login_status,"NO",COMMON_STATUS_LEN);
                // write_email_log(flow, direction, NULL);
            }
        }
        }
    }

    if (session->state == EMAIL_STATE_READING_DATA) {
        cache_offset = 2;
        session->state = EMAIL_STATE_READING_DATA;
    }

    c = session->cache + cache_offset;
    if (NULL == c->cache) {
        c->cache_size = 1024 * 1000;  // 200K
        c->cache_hold = 0;
        c->cache = dpi_malloc(c->cache_size);
    }
    if (c->cache && session->state != EMAIL_STATE_READING_CMDS) {
        if ((int)payload_len >= (c->cache_size - c->cache_hold)) {
            // 缓存撑爆前重新申请内存
            // 创建临时缓冲区
            char *new_cache = (char *)realloc(c->cache, c->cache_size + l + 1000);
            if (NULL == new_cache) {
                goto EMAIL_NEED_MORE_PKT;
            }
            c->cache = new_cache;
            c->cache_size += l + 1000;
        }
        // 正常 拼装
        memcpy(c->cache + c->cache_hold, payload, payload_len);
        c->cache_hold += payload_len;
    }
EMAIL_NEED_MORE_PKT:
    return 0;
}

static void timeout_pop(struct flow_info *flow) {
    EmailSession *session = flow->app_session;
    if (!session) {
        return;
    }
    struct Emailcache *c = session->cache + 2;
    if (c->cache) {
        write_pop_eml(flow, 0);
    }
#if 0
// 无实体的不输出
    else if(g_config.write_nofile_eml) {
      if (!session->record) {
            static char *proto_name = "";
            if (g_config.write_mail_log) {
                proto_name = "email";
            } else {
                proto_name = "pop";
            }
            session->record = ya_create_record(proto_name);
            session->NXTYPE = MAIL_POP3;
            dpi_head_field_put(flow, flow->direction, proto_name, session->record);
      }
        write_email_log(flow, 0, EM_MAIL_IMAP, NULL);
    }
#endif

    if (session->record)
        precord_destroy(session->record);
    session->record = NULL;

    for (int i = 0; i < EMAIL_CACHE_MAX; i++) {
        struct Emailcache *c = session->cache + i;
        if (NULL != c->cache) {
            free(c->cache);
            c->cache = NULL;
            c->cache_hold = 0;
        }
    }
    free(session);
    flow->app_session = NULL;

    return;
}

extern struct decode_t decode_pop;

static int init_pop_dissector(struct decode_t *decode)
{
    dpi_register_proto_schema(pop_field_array,EM_POP_MAX,"pop");

    decode_on_port_tcp(POP_PORT, &decode_pop);

    register_tbl_array(TBL_LOG_MAIL_POP, 1, "pop", NULL);

    map_fields_info_register(pop_field_array,PROTOCOL_MAIL_POP, EM_POP_MAX,"pop");
	return 0;
}

static int pop_destroy(struct decode_t *decode) { return 0; }

struct decode_t decode_pop = {
    .name           =   "pop",
#ifdef DPI_SDT_ZDY
    .identify_type  =   DPI_IDENTIFY_PORT_CONTENT,
#endif
    .decode_initial =   init_pop_dissector,
    .pkt_identify   =   identify_pop,
    .pkt_dissect    =   dissect_pop_rsm,
    .pkt_miss       =   dissect_email_miss,
    .flow_finish    =   timeout_pop,
    .decode_destroy =   pop_destroy,
};
