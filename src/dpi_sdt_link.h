#ifndef _DPI_SDT_LINK_H_
#define _DPI_SDT_LINK_H_

#include <yaProtoRecord/precord.h>

#include "dpi_memory.h"


enum _link_enum{
    EM_LINK_PORTINFO,
    EM_LINK_PORTINFOATT,
    EM_LINK_UPPAYLEN,
    EM_LINK_DOWNPAYLEN,
    EM_LINK_TCPFLAG,
    EM_LINK_UPLINKPKTNUM,
    EM_LINK_UPLINKSIZE,
    EM_LINK_UPLINKBIGPKTLEN,
    EM_LINK_UPLINKSMAPKTLEN,
    EM_LINK_UPLINKBIGPKTINT,
    EM_LINK_UPLINKSMAPKTINT,
    EM_LINK_DOWNLINKPKTNUM,
    EM_LINK_DOWNLINKSIZE,
    EM_LINK_DOWNLINKBIGPKTLEN,
    EM_LINK_DOWNLINKSMAPKTLEN,
    EM_LINK_DOWNLINKBIGPKTINT,
    EM_LINK_DOWNLINKSMAPKTINT,
    EM_LINK_FIRTTLBYCLI,
    EM_LINK_FIRTTLBYSRV,
    EM_LINK_APPDIREC,
    EM_LINK_TCPFLAGSFINCNT,
    EM_LINK_TCPFLAGSSYNCNT,
    EM_LINK_TCPFLAGSRSTCNT,
    EM_LINK_TCPFLAGSPSHCNT,
    EM_LINK_TCPFLAGSACKCNT,
    EM_LINK_TCPFLAGSURGCNT,
    EM_LINK_TCPFLAGSECECNT,
    EM_LINK_TCPFLAGSCWRCNT,
    EM_LINK_TCPFLAGSNSCNT,
    EM_LINK_TCPFLAGSSYNACKCNT,
    EM_LINK_ETAGS,
    EM_LINK_TTAGS,
    EM_LINK_UPLINKCHECKSUM,
    EM_LINK_DOWNLINKCHECKSUM,
    EM_LINK_UPLINKDESBYTES,
    EM_LINK_DOWNLINKDESBYTES,
    EM_LINK_STREAM,
    EM_LINK_UPLINKSTREAM,
    EM_LINK_DOWNLINKSTREAM,
    EM_LINK_TRANS_PAYLOAD_HEX,
    EM_LINK_UPLINKTRANSPAYHEX,
    EM_LINK_DOWNLINKTRANSPAYHEX,
    EM_LINK_UPLINKPAYLENSET,
    EM_LINK_DOWNLINKPAYLENSET,
    EM_LINK_ESTABLISH,
    EM_LINK_UPLINKSYNSEQNUM,
    EM_LINK_DOWNLINKSYNSEQNUM,
    EM_LINK_UPLINKSYNTCPWINS,
    EM_LINK_DOWNLINKSYNTCPWINS,
    EM_LINK_UPLINKTCPOPTS,
    EM_LINK_DOWNLINKTCPOPTS,
    EM_LINK_UPSESBYTES,
    EM_LINK_DOWNSESBYTES,
    EM_LINK_SESBYTES,
    EM_LINK_SESBYTESRATIO,
    EM_LINK_PAYLENRATIO,


    EM_LINK_MAX
};


//int write_link_log(struct flow_info *flow, struct tbl_log *log_ptr, int *idx);

int write_link_log(struct flow_info *flow, int direction, struct tbl_log *log_ptr, int *idx, SdtMatchResult *match_result);
int write_link(precord_t *record, struct flow_info *flow, int direction);
void dpi_sdt_init_link_map_fields(void);
#endif
