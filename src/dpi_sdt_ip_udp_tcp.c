#include <pcap.h>
#include <string.h>
#include <stdlib.h>
#include <netinet/in.h>
#include <rte_mempool.h>
#include <glib.h>

#include "dpi_typedefs.h"
#include "dpi_proto_ids.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_conversation.h"
#include "dpi_tcp_reassemble.h"
#include <libsdt/libsdt_interface.h>
#include "sdt_ip_protocols.h"
#include "sdt_action_out.h"
#include "dpi_sdt_match.h"
#include "dpi_sdt_link.h"
#include "dpi_pschema.h"
#include "dpi_utils.h"
#include "dpi_typedefs.h"
#include "dpi_sdt_eval_field_callback.h"

extern struct rte_mempool *tbl_log_mempool;

#define SDX_NEXT_MAX_HEADER   256
#define SDX_NEXT_MAX_CONTENT  3000

typedef enum _sdt_tran_type
{
  EM_TRAN_IP,
  EM_TRAN_TCP,
  EM_TRAN_UDP,
  EM_TRAN_SCTP,
}sdt_tran_type;

enum _sdt_ip_enum{
    EM_SDT_IP_FLAG,
    EM_SDT_IP_LEN,
    EM_SDT_IP_HEADER,
    EM_SDT_IP_TTL,
    EM_SDT_IP_CONTENT,
    EM_SDT_IP_CONTENT_LEN,
    EM_SDT_IP_MAX
};

enum _sdt_udp_enum{
    EM_SDT_UDP_HEADER,
    EM_SDT_UDP_PAYLOAD,
    EM_SDT_UDP_PAYLOAD_LEN,
    EM_SDT_UDP_MAX
};

enum _sdt_tcp_enum{
    EM_SDT_TCP_HEADER,
    EM_SDT_TCP_HEADER_LEN,
    EM_SDT_TCP_FLAG,
    EM_SDT_TCP_FLAG_FIN,
    EM_SDT_TCP_FLAG_SYN,
    EM_SDT_TCP_FLAG_RST,
    EM_SDT_TCP_FLAG_PSH,
    EM_SDT_TCP_FLAG_ACK,
    EM_SDT_TCP_FLAG_URG,
    EM_SDT_TCP_FLAG_ECE,
    EM_SDT_TCP_FLAG_CWR,
    EM_SDT_TCP_FLAG_NS,
    EM_SDT_TCP_WINDOWSIZE,
    EM_SDT_TCP_PAYLOAD,
    EM_SDT_TCP_PAYLOAD_LEN,
    EM_SDT_TCP_MAX
};

dpi_field_table ip_field_array[] = {
     DPI_FIELD_D(EM_SDT_IP_FLAG,                 YA_FT_UINT16,       "ip_flag"),
     DPI_FIELD_D(EM_SDT_IP_LEN,                  YA_FT_UINT16,       "ip_len"),
     DPI_FIELD_D(EM_SDT_IP_HEADER,               YA_FT_BYTES,        "ip_header"),
     DPI_FIELD_D(EM_SDT_IP_TTL,                  YA_FT_UINT8,        "ip_ttl"),
     DPI_FIELD_D(EM_SDT_IP_CONTENT,              YA_FT_BYTES,        "ip_content"),
     DPI_FIELD_D(EM_SDT_IP_CONTENT_LEN,          YA_FT_UINT16,       "ip_contentlen"),
};

dpi_field_table udp_field_array[] = {
    DPI_FIELD_D(EM_SDT_UDP_HEADER,               YA_FT_BYTES,       "udp_header"),
    DPI_FIELD_D(EM_SDT_UDP_PAYLOAD,              YA_FT_BYTES,       "udp_payload"),
    DPI_FIELD_D(EM_SDT_UDP_PAYLOAD_LEN,          YA_FT_UINT16,      "udp_payloadlen"),
};

dpi_field_table tcp_field_array[] = {
    DPI_FIELD_D(EM_SDT_TCP_HEADER,               YA_FT_BYTES,        "tcp_header"),
    DPI_FIELD_D(EM_SDT_TCP_HEADER_LEN,           YA_FT_UINT8,        "tcp_headerlen"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG,                 YA_FT_UINT16,       "tcp_flag"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_FIN,             YA_FT_UINT8,        "tcp_fin"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_SYN,             YA_FT_UINT8,        "tcp_syn"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_RST,             YA_FT_UINT8,        "tcp_rst"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_PSH,             YA_FT_UINT8,        "tcp_psh"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_ACK,             YA_FT_UINT8,        "tcp_ack"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_URG,             YA_FT_UINT8,        "tcp_urg"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_ECE,             YA_FT_UINT8,        "tcp_ece"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_CWR,             YA_FT_UINT8,        "tcp_cwr"),
    DPI_FIELD_D(EM_SDT_TCP_FLAG_NS,              YA_FT_UINT8,        "tcp_ns"),
    DPI_FIELD_D(EM_SDT_TCP_WINDOWSIZE,           YA_FT_UINT16,       "tcp_window"),
    DPI_FIELD_D(EM_SDT_TCP_PAYLOAD,              YA_FT_BYTES,        "tcp_payload"),
    DPI_FIELD_D(EM_SDT_TCP_PAYLOAD_LEN,          YA_FT_UINT16,       "tcp_payloadlen"),
};
void write_common(precord_t *record, int log_len_max, struct flow_info *flow, int direction);

int write_ip_record(struct flow_info *flow, int direction, const void *field_info, precord_t * record)
{
  int              i       = 0;
  int              idx     = 0;
  struct tbl_log  *log_ptr = NULL;
  struct pkt_info *pkt     = (struct pkt_info *)field_info;

  const uint8_t *content     = NULL;
  int            content_len = 0;

  const uint8_t *ip_header     = NULL;
  int            ip_header_len = 0;
  if (pkt->ipversion == 4) {
    ip_header     = (const uint8_t *)pkt->iph4;
    ip_header_len = pkt->iph4->ihl * 4;

    content     = (const uint8_t *)pkt->iph4 + ip_header_len;
    content_len = ntohs(pkt->iph4->tot_len) - ip_header_len;
  } else if (pkt->ipversion == 6) {
    ip_header     = (const uint8_t *)pkt->iph6;
    ip_header_len = sizeof(struct dpi_ipv6hdr);

    content     = (const uint8_t *)pkt->iph6 + ip_header_len;
    content_len = ntohs(pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen);
  } else {
    return PKT_DROP;
  }

    player_t *layer = precord_layer_put_new_layer(record, "ip");

    for(i=0;i<EM_SDT_IP_MAX;i++){
        switch(i){
        case EM_SDT_IP_FLAG:
            if(pkt->iph4)
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, (uint64_t)ntohs(pkt->iph4->frag_off));
            else
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case EM_SDT_IP_LEN:
            if(pkt->iph4)
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL,  (uint64_t)ntohs(pkt->iph4->tot_len));
            else if(pkt->iph6)
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL,  (uint64_t)ntohs(pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen));
            else
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case EM_SDT_IP_HEADER:
            write_multi_num_reconds(record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)ip_header, ip_header_len);
            break;
        case EM_SDT_IP_TTL:
            if(pkt->ipversion==4)
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, (uint64_t)pkt->iph4->ttl);
            else
                write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, (uint64_t)pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_hlim);
            break;
        case EM_SDT_IP_CONTENT:
            write_multi_num_reconds(record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)content, content_len);
            break;
        case EM_SDT_IP_CONTENT_LEN:
            write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, ip_field_array[i].type, NULL, (uint64_t)content_len);
            break;
        default:
            write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }
    return 0;
}

int write_udp_record(struct flow_info *flow, int direction, const void *_pkt, precord_t * record)
{
    int i = 0;
    int idx = 0;
    struct pkt_info *pkt = (struct pkt_info *)_pkt;
    int      payload_len=0;
    const uint8_t *payload=NULL;
    int           ip_header_len=0;
    if(4==pkt->ipversion){
        ip_header_len=pkt->iph4->ihl*4;
        payload_len = ntohs(pkt->iph4->tot_len) - ip_header_len - sizeof( struct dpi_udphdr);
        payload     = (const uint8_t *)pkt->udph+sizeof( struct dpi_udphdr);
    }else if(6==pkt->ipversion){
        payload_len = ntohs(pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen) - sizeof(struct dpi_udphdr);
        payload     = (const uint8_t *)pkt->udph+sizeof(struct dpi_udphdr);
    }else{
        return PKT_DROP;
    }

    if(payload_len<0 || payload_len>pkt->pkt_len){
        return PKT_DROP;
    }

#ifdef DPI_SDT_ZDY
    //更新共性字段
    precord_layer_remove(record, "common");
    precord_layer_remove(record, "327_common");
    write_common(record, TBL_LOG_MAX_LEN, flow, direction);
#endif
    player_t *layer = precord_layer_put_new_layer(record, "udp");
    for (i = 0; i < EM_SDT_UDP_MAX; i++) {
      switch (i) {
        case EM_SDT_UDP_HEADER:
          write_multi_num_reconds(record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)pkt->udph, (uint32_t)sizeof(struct dpi_udphdr));
          break;
        case EM_SDT_UDP_PAYLOAD:
          write_multi_num_reconds(record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)payload, (uint32_t)payload_len);
          break;
        case EM_SDT_UDP_PAYLOAD_LEN:
          write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, udp_field_array[i].type, NULL, (uint64_t)payload_len);
          break;
        default:
          write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
          break;
      }
    }
    return PKT_DROP;
}

int write_tcp_record(struct flow_info *flow, int direction, const void *field_info, precord_t * record)
{
    int i = 0;
    int idx = 0;

    const  uint8_t *payload=NULL;
    int payload_len    = 0;
    int ipv6_option_len = 0;
    int ipv4_headerlen = 0;

    struct pkt_info  *pkt=(struct pkt_info  *)field_info;

    int tcp_header_len=pkt->tcph->doff*4;
    payload=(const uint8_t *)pkt->tcph + tcp_header_len;
    if(4==pkt->ipversion){
        ipv4_headerlen=pkt->iph4->ihl*4;
        payload_len=ntohs(pkt->iph4->tot_len)-ipv4_headerlen-tcp_header_len;
    }else if(6==pkt->ipversion){
        ipv6_option_len=(const uint8_t *)pkt->tcph-(const uint8_t *)pkt->iph6-sizeof(struct dpi_ipv6hdr);
        payload_len=ntohs(pkt->iph6->ip6_ctlun.ip6_un1.ip6_un1_plen) - ipv6_option_len -tcp_header_len;
    }else{
        return PKT_DROP;
    }

    if(payload_len<0 ||payload_len>pkt->pkt_len || ipv6_option_len<0){
        return PKT_DROP;
    }

#ifdef DPI_SDT_ZDY
    //更新共性字段
    precord_layer_remove(record, "common");
    precord_layer_remove(record, "327_common");
    write_common(record, TBL_LOG_MAX_LEN, flow, direction);
#endif
    player_t *layer = precord_layer_put_new_layer(record, "tcp");

    // #ifdef DPI_SDT_ZDY
    //     write_327ZDY_task_info(match_result, record, (const char *)flow->path);
    // #endif

    for(i=0;i<EM_SDT_TCP_MAX;i++){
        switch(i){
        case EM_SDT_TCP_HEADER:
             write_multi_num_reconds(record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)pkt->tcph, pkt->tcph->doff*4);
             break;
         case EM_SDT_TCP_HEADER_LEN:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->doff*4);
             break;
         case EM_SDT_TCP_FLAG:
         {
             uint16_t tcp_flags=pkt->tcph->doff<<12|
                        pkt->tcph->res1<<8 |
                        pkt->tcph->cwr<<7  |
                        pkt->tcph->ece<<6  |
                        pkt->tcph->urg<<5  |
                        pkt->tcph->ack<<4  |
                        pkt->tcph->psh<<3  |
                        pkt->tcph->rst<<2  |
                        pkt->tcph->syn<<1  |
                        pkt->tcph->fin;
             tcp_flags&=0x0FFF;
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, tcp_flags);
         }
             break;
         case EM_SDT_TCP_FLAG_FIN:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->fin);
             break;
         case EM_SDT_TCP_FLAG_SYN:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->syn);
             break;
         case EM_SDT_TCP_FLAG_RST:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->rst);
             break;
         case EM_SDT_TCP_FLAG_PSH:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->psh);
             break;
         case EM_SDT_TCP_FLAG_ACK:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->ack);
             break;
         case EM_SDT_TCP_FLAG_URG:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->urg);
             break;
         case EM_SDT_TCP_FLAG_ECE:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->ece);
             break;
         case EM_SDT_TCP_FLAG_CWR:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->cwr);
             break;
         case EM_SDT_TCP_FLAG_NS:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, pkt->tcph->res1&0x1);
             break;
         case EM_SDT_TCP_WINDOWSIZE:
             write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, ntohs(pkt->tcph->window));
             break;
        case EM_SDT_TCP_PAYLOAD:
            write_multi_num_reconds(record, &idx, TBL_LOG_MAX_LEN, (const uint8_t *)payload, (uint32_t)payload_len);
            break;
        case EM_SDT_TCP_PAYLOAD_LEN:
            write_coupler_log(record, &idx, TBL_LOG_MAX_LEN,  tcp_field_array[i].type, NULL, payload_len);
            break;
        default:
            write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }

    return PKT_DROP;
}

int write_ipff_log(struct flow_info *flow, struct pkt_info *pkt, SdtMatchResult *ruleAction)
{
    sdt_out_status *s = sdt_rule_hash_db_lookup(ruleAction);
    precord_t *record = dpi_precord_create("");

    struct tbl_log  *new_tbl = NULL;
    if (rte_mempool_get(tbl_log_mempool, (void **)&new_tbl) < 0)
    {
        DPI_LOG(DPI_LOG_WARNING, "in %s rte_mempool_get not enough memory", __func__);
        return 0; //安全
    }

    memset(new_tbl, 0, sizeof(struct tbl_log));
    new_tbl->flow = flow;
    new_tbl->match_info[new_tbl->match_res_cnt] = s;
    new_tbl->match_res_cnt ++;

    if(IPPROTO_TCP == pkt->proto) {
        new_tbl->log_type = TBL_LOG_TCP;
        write_tcp_record(flow, flow->direction, pkt, record);
    } else
    if(IPPROTO_UDP == pkt->proto) {
        new_tbl->log_type = TBL_LOG_UDP;
        write_udp_record(flow, flow->direction, pkt, record);
    } else
    if(IPPROTO_IPIP == pkt->proto) {
        new_tbl->log_type = TBL_LOG_IP;
        write_ip_record(flow, flow->direction, pkt, record);
    } else
    if(IPPROTO_IPV6 == pkt->proto) {
        new_tbl->log_type = TBL_LOG_IP;
        write_ip_record(flow, flow->direction, pkt, record);
    }

#ifdef DPI_SDT_ZDY
        write_common(record, TBL_LOG_MAX_LEN, flow, flow->direction);
#else
        write_shared_header(record, TBL_LOG_MAX_LEN, flow, flow->direction);
        write_link(record, flow, flow->direction);
#endif

    new_tbl->record = record;
    if (dpi_app_match_res_enqueue(new_tbl) != 0) {
        dpi_tbl_free(new_tbl);
    }

    return 0;
}

static void
_sdt_init_ip_dissector(void)
{
    ip_field_array[EM_SDT_IP_FLAG].callback         = cb_ip_flag;
    ip_field_array[EM_SDT_IP_LEN].callback          = cb_ip_len;
    ip_field_array[EM_SDT_IP_HEADER].callback       = cb_ip_header;
    ip_field_array[EM_SDT_IP_TTL].callback          = cb_ip_ttl;
    ip_field_array[EM_SDT_IP_CONTENT].callback      = cb_ip_payload;
    ip_field_array[EM_SDT_IP_CONTENT_LEN].callback  = cb_ip_payload_len;

    dpi_register_proto_schema(ip_field_array,EM_SDT_IP_MAX,"ip");
    map_fields_info_register(ip_field_array, PROTOCOL_IP, EM_SDT_IP_MAX, "ip");
    return;
}

static void
_sdt_init_udp_dissector(void)
{
    udp_field_array[EM_SDT_UDP_HEADER].callback         = cb_udp_header;
    udp_field_array[EM_SDT_UDP_PAYLOAD].callback        = cb_udp_payload;
    udp_field_array[EM_SDT_UDP_PAYLOAD_LEN].callback    = cb_udp_payload_length;

    dpi_register_proto_schema(udp_field_array,EM_SDT_UDP_MAX,"udp");
    map_fields_info_register(udp_field_array, PROTOCOL_UDP, EM_SDT_UDP_MAX, "udp");
    return;
}

static void
_sdt_init_tcp_dissector(void)
{
    tcp_field_array[EM_SDT_TCP_HEADER].callback     = cb_tcp_header;
    tcp_field_array[EM_SDT_TCP_HEADER_LEN].callback = cb_tcp_header_len;
    tcp_field_array[EM_SDT_TCP_FLAG].callback       = cb_tcp_flag;
    tcp_field_array[EM_SDT_TCP_FLAG_FIN].callback   = cb_tcp_flag_fin;
    tcp_field_array[EM_SDT_TCP_FLAG_SYN].callback   = cb_tcp_flag_syn;
    tcp_field_array[EM_SDT_TCP_FLAG_RST].callback   = cb_tcp_flag_rst;
    tcp_field_array[EM_SDT_TCP_FLAG_PSH].callback   = cb_tcp_flag_psh ;
    tcp_field_array[EM_SDT_TCP_FLAG_ACK].callback   = cb_tcp_flag_ack;
    tcp_field_array[EM_SDT_TCP_FLAG_URG].callback   = cb_tcp_flag_ugr;
    tcp_field_array[EM_SDT_TCP_FLAG_ECE].callback   = cb_tcp_flag_ece;
    tcp_field_array[EM_SDT_TCP_FLAG_CWR].callback   = cb_tcp_flag_cwr;
    tcp_field_array[EM_SDT_TCP_FLAG_NS].callback    = cb_tcp_flag_ns;
    tcp_field_array[EM_SDT_TCP_WINDOWSIZE].callback = cb_tcp_windowsize;
    tcp_field_array[EM_SDT_TCP_PAYLOAD].callback    = cb_tcp_payload;
    tcp_field_array[EM_SDT_TCP_PAYLOAD_LEN].callback= cb_tcp_payload_length;

    dpi_register_proto_schema(tcp_field_array,EM_SDT_TCP_MAX,"tcp");
    map_fields_info_register(tcp_field_array, PROTOCOL_TCP, EM_SDT_TCP_MAX, "tcp");
    return;
}

static __attribute((constructor)) void before_sdt_init_network(void){
    register_tbl_array(TBL_LOG_IP, 1, "ip", _sdt_init_ip_dissector);
    register_tbl_array(TBL_LOG_UDP, 1, "udp", _sdt_init_udp_dissector);
    register_tbl_array(TBL_LOG_TCP, 1, "tcp", _sdt_init_tcp_dissector);
}
