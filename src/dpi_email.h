/*******************************************************************************
 * 文 件 名 : dpi_email.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
- - - - 设计: chenzq      2023/04/11
- - - - 编码: chenzq      2023/04/11
- - - - 修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
- - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
- - -

*******************************************************************************/
#ifndef __DPI_EMAIL_H
#define __DPI_EMAIL_H

#include <yaemail/email.h>

#include "dpi_detect.h"

enum email_index_em{
    EM_EMAIL_SENDER_EMAIL,
    EM_EMAIL_SENDER_ALI,
    EM_EMAIL_FROM_IP,
    EM_EMAIL_FROM_IP_CNT,
    EM_EMAIL_FROM_DOM,
    EM_EMAIL_FROM_DOM_CNT,
    EM_EMAIL_FROM_ASN,
    EM_EMAIL_FROM_COUNTRY,
    EM_EMAIL_RCVR_EMAIL,
    EM_EMAIL_RCVR_ALI,
    EM_EMAIL_BY_IP,
    EM_EMAIL_RCV_DATE,
    EM_EMAIL_CC,
    EM_EMAIL_CC_ALI,
    EM_EMAIL_BCC,
    EM_EMAIL_REP_TO,
    EM_EMAIL_DATE,
    EM_EMAIL_EMA_PROT_TYPE,
    EM_EMAIL_LOGIN_SRV,
    EM_EMAIL_SMTP_SRV,
    EM_EMAIL_SMTP_SRV_AGE,
    EM_EMAIL_SUBJ,
    EM_EMAIL_X_MAI,
    EM_EMAIL_CON_TRA_ENC,
    EM_EMAIL_CON_TEX_CHA,
    EM_EMAIL_BODY_TYPE,
    EM_EMAIL_BODY_TYPE_CNT,
    EM_EMAIL_CON_TYPE,
    EM_EMAIL_CON_TYPE_CNT,
    EM_EMAIL_EMA_IND,
    EM_EMAIL_ATT_FILENAME,
    EM_EMAIL_ATT_FILENAME_CNT,
    EM_EMAIL_ATT_TYPE,
    EM_EMAIL_ATT_TYPE_CNT,
    EM_EMAIL_ATT_MD5,
    EM_EMAIL_ATT_MD5_CNT,
    EM_EMAIL_ATT_CON_SIZE,
    EM_EMAIL_HEAD_SET,
    EM_EMAIL_HEAD_SET_CNT,
    EM_EMAIL_MSG_ID,
    EM_EMAIL_MSG_ID_CNT,
    EM_EMAIL_MIME_VER,
    EM_EMAIL_MIME_VER_CNT,
    EM_EMAIL_LOGIN,
    EM_EMAIL_PWD,
    EM_EMAIL_REAL_FROM ,
    EM_EMAIL_REAL_TO,
    EM_EMAIL_CONTENT_WITH_HTML,
    EM_EMAIL_CHARSET,
    EM_EMAIL_CONTENT_LEN,
    EM_EMAIL_HOST,
    EM_EMAIL_DELIVERED_TO,
    EM_EMAIL_X_ORI_IP,
    EM_EMAIL_START_TLS,
    EM_EMAIL_COMMAND,
    EM_EMAIL_COUNT,
    EM_EMAIL_MAIL_FROM,
    EM_EMAIL_MAIL_FROM_DOM,
    EM_EMAIL_MAIL_FROM_DOM_CNT,
    EM_EMAIL_RCVR_DOM,
    EM_EMAIL_RCPT_TO,
    EM_EMAIL_RCPT_TO_DOM,
    EM_EMAIL_RCPT_TO_DOM_CNT,
    EM_EMAIL_RESENT_FROM,
    EM_EMAIL_RESENT_TO,
    EM_EMAIL_RESENT_DATE,
    EM_EMAIL_BODY_LEN,
    EM_EMAIL_BODY_TRA_ENC,
    EM_EMAIL_BODY_TEX_CHA,
    EM_EMAIL_NAME,
    EM_EMAIL_VENDOR,
    EM_EMAIL_VER,
    EM_EMAIL_OS,
    EM_EMAIL_OSVER,
    EM_EMAIL_RCVR_EMAIL_CNT,
    EM_EMAIL_SUBJ_CNT,
    EM_EMAIL_X_MAI_CNT,
    EM_EMAIL_RECEIVED,
    EM_EMAIL_WITH,
    EM_EMAIL_BYDOM,
    EM_EMAIL_BYASN,
    EM_EMAIL_BYCOUNTRY,
    EM_EMAIL_USRAGE,
    EM_EMAIL_SENDERDOM,
    EM_EMAIL_CONTNET,
    EM_EMAIL_RESENTSRVAGE,
    EM_EMAIL_BODY,
    EM_EMAIL_BODYURL,
#ifdef DPI_SDT_YNAO
    EM_EMAIL_LOCAL_FILENAME,
    EM_EMAIL_LOCAL_FILEPATH,
#endif
    EM_EMAIL_MAX
};

#define EMAIL_MAX_REASSMBLE_LEN  1024*1024*100   // 最大重组100M


typedef enum _email_type_t
{
  EMAIL_SMTP,
  EMAIL_POP,
  EMAIL_IMAP
}EmailType;

typedef enum
{
  EMAIL_STATE_READING_CMDS,              // reading commands
  EMAIL_STATE_READING_AUTHNAME,
  EMAIL_STATE_READING_AUTHPASSWD,
  EMAIL_STATE_READING_DATA,              // reading message data
  EMAIL_STATE_AWAITING_STARTTLS_RESPONSE, // sent STARTTLS, awaiting response
  EMAIL_STATE_READING_STATUS
} email_state_t;
/*
*  smtp 专用结构， eg  from to
*/
typedef struct head_field
{
  char  field[128];
  char  value[256];
  char  domin[128];
  int   domin_cnt;
}HeadField;

typedef enum
{
    SESSION_TYPE_SMTP,
    SESSION_TYPE_ESMTP,
} session_type_t;

typedef struct kv_pair_
{
  char key[128];
  char value[128];
}KeyValuePair;

#define EMAIL_CACHE_MAX 3
struct Emailcache
{
    char     *cache;
    int       cache_size;
    int       cache_hold;
};

#define EMAIL_IMAP_KV_MAX    10
typedef struct email_session
{

  char auth_name[128];
  char auth_passwd[64];

  char bcc[512];
  char filepath[256];
  char mail_filename[128];
  uint32_t eml_filesize;
  uint8_t  drop_data[16];
  char     login_status[COMMON_STATUS_LEN];
  int complemail;
  email_state_t state;
  // void * handle;
  precord_t * record;
  precord_t * pass_record;
  uint8_t     is_write;   //在imap协议中，没有body的口令字放在超时中输出，为了在超时中不重复输出，设置标记
  uint64_t creatime;
  //----------------smtp---------------------
  char send_host_ip4[64];
  char send_host_ip6[64];
  char mail_server_name_ptr[32];
  char mail_server_soft_ptr[32];
  char mail_helo_ptr[32];
  char mail_recv_host_ip_ptr[16];
  int miss_len ;
  struct Emailcache cache[EMAIL_CACHE_MAX]; //0,1 方向cache 2 邮件cache

  dpi_email_t *email;

  // 通用
  uint8_t       starttls_f;
  uint8_t       mail_num;  // 邮件份数
  char          email_type[16]; // 邮件类型 SMTP IMAP POP

  // 命令集合
  char          commands[2048];
  uint8_t       commands_num;

  // from asn  IP解析出来的自治域
  char          from_asns[2048];
  uint8_t       from_asns_num;

  // from country IP解析出来的国家
  char          from_countries[1024];
  uint8_t       from_countries_num;

  /*--------smtp 专用----------------*/
  HeadField     from;
  HeadField     rcptto;
  char          mail_server[256];
  uint32_t      mail_begin_seq;
  uint32_t      user_seq;
  uint32_t      passwd_seq;
  session_type_t session_type;
  uint8_t       has_ensure_type;
  /*--------smtp 专用----------------*/

  /*--------imap 专用----------------*/

  KeyValuePair kv_pairs[EMAIL_IMAP_KV_MAX];            // imap 协议中 command 为 ID 时，解析出来的键值对
  /*--------imap 专用----------------*/

  /*--------pop  专用----------------*/
  /*--------pop  专用----------------*/

  // char          mail_from[512];
  // char          mail_from_domin[512];

}EmailSession;

// get reassemable result
int email_get_reassemble(struct flow_info *flow, int direction, uint8_t **data, uint32_t *data_len);
/*
 * 解析 rfc 822 公共内容
 */
int         email_dissect_imf(struct flow_info *flow, int direction, uint8_t *data, uint32_t data_len);
const char *dpi_get_email_command(const char *data, int data_len, EmailType type);
void        write_email_log(struct flow_info *flow, int direction, dpi_email_t *email);
int         dissect_email_miss(struct flow_info *flow, uint8_t direction, uint32_t miss_len);
#endif