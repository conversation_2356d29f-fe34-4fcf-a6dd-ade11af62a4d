/****************************************************************************************
 * 文 件 名 : dpi_detect.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_DETECT_H_
#define _DPI_DETECT_H_
// #include "pch.h"
#include <sys/types.h>
#include <regex.h>
#include <rte_hash.h>
#include <rte_jhash.h>
#include <rte_timer.h>
#include <maxminddb.h>
#include <glib.h>


#include "dpi_tcp_reassemble.h"


#include "list.h"
#include "dpi_proto_ids.h"
#include "dpi_typedefs.h"
#include "dpi_trailer.h"
#include "dpi_common.h"
#include "ip2region.h"

#include "libsdtacl/yasdtacl.h"
#include "sdtapp_interface.h"
#include "libsdt/libsdt_interface.h"
#include "dpi_mac_pheader.h"
#include "dpi_forward_datatype.h"

#include "dpi_sdx_common.h"
#include "dpi_tll.h"
#include "tcp_rsm.h"
#include "dpi_memory.h"
#include "dpi_327_common.h"
#define TIMEOUT_MAX 60
#define RCV_CORE_MAX_NUM 16
#define TBL_RING_MAX_NUM 16
#define APP_PROTOCOL_RING_MAX_NUM 16


#ifndef ETH_P_IP
#define ETH_P_IP               0x0800     /* IPv4 */
#endif

#ifndef ETHERTYPE_PPP
#define ETHERTYPE_PPP               0x880B
#endif

#ifndef ETHERTYPE_3GPP2
#define ETHERTYPE_3GPP2         0x88D2
#endif

#ifndef ETHERTYPE_CDMA2000_A10_UBS
#define ETHERTYPE_CDMA2000_A10_UBS 0X8881
#endif

#ifndef GRE_WCCP
#define GRE_WCCP               0x883E
#endif



#ifndef ETH_P_IPV6
#define ETH_P_IPV6           0x86dd    /* IPv6 */
#endif

#define SLARP                  0x8035   /* Cisco Slarp */
#define CISCO_D_PROTO          0x2000    /* Cisco Discovery Protocol */

#define VLAN                   0x8100
#define MPLS_UNI               0x8847
#define MPLS_MULTI             0x8848
#define MPLS_PWETHCW           0x0000
#define PPPoE                  0x8864
#define PPPoE_DISCOVERY        0x8863   //发现报文
#define SNAP                   0xaa
#define BSTP                   0x42     /* Bridge Spanning Tree Protocol */

#define VMLAB                  0x88de
#define VMLAB_SIZE             24
#define VMLAB_ENCPTYPE_OFFSET  22

#define ARP                    0x0806
#define RARP                   0x8035
#define LACP                   0x8809
#define LLDP                   0x88CC

#define PPP_IP                0x0021   //IP报文
#define PPP_IPCP              0x8021
#define PPP_LCP               0xc021
#define PPP_PAP               0xc023
#define PPP_CHAP              0xc223

#define DPI_HDLC              0xd000
#define DPI_ETH               0xd001

#define GTP_U_V1_PORT          2152
#define TZSP_PORT              37008

#define IDLE_SCAN_BUDGET       10240
#define IDLE_SCAN_MAX_NUM      (10240 * 10)
//#define IDLE_SCAN_PERIOD           (10 * 1000) /* msec (use TICK_RESOLUTION = 1000) */
#define MAX_IDLE_TIME          (30 * 1000 * 1000)
#define MAX_THREAD_NUM         16
#define MAX_FLOW_THREAD_NUM    16

#define DEV_MAX_NUM            10
#define TRAFFIC_NUM            10
#define PKT_DISTRIBUTION_NUM   50

#define HTTP_CONTENT_MAX_CHUNK 2*1024*1024  //2M

#define REASSEMBLE_LEN_MAX     2*1024*1024
#define MAX_CONTENT_SIZE       2048
#define MAX_CHECK_LEN          2048


#define NORMAL_ARRAY_LEN       256

#define PKT_STREAM_DIR_NUM     2
#define SDT_MAX_MATCH_RULES    128

#define SDT_PAYLOAD_LEN_SET_NUM   8

#define MAX_CPU_CORES     128
#define DPI_MAX(a,b)   ((a) > (b) ? (a) : (b))
#define DPI_MIN(a,b)   ((a) < (b) ? (a) : (b))

typedef uint32_t dpi_ndpi_mask;

#define DPI_NUM_BITS              256

#define DPI_BITS /* 32 */ (sizeof(dpi_ndpi_mask) * 8 /* number of bits in a byte */)        /* bits per mask */
#define howmanybits(x, y)   (((x)+((y)-1))/(y))

#define DPI_SET(p, n)    ((p)->fds_bits[(n)/DPI_BITS] |=  (1ul << (((uint32_t)n) % DPI_BITS)))
#define DPI_CLR(p, n)    ((p)->fds_bits[(n)/DPI_BITS] &= ~(1ul << (((uint32_t)n) % DPI_BITS)))
#define DPI_ISSET(p, n)  ((p)->fds_bits[(n)/DPI_BITS] &   (1ul << (((uint32_t)n) % DPI_BITS)))
#define DPI_ZERO(p)      memset((char *)(p), 0, sizeof(*(p)))
#define DPI_ONE(p)       memset((char *)(p), 0xFF, sizeof(*(p)))

#define DPI_NUM_FDS_BITS     howmanybits(DPI_NUM_BITS, DPI_BITS)

typedef struct dpi_protocol_bitmask_struct
{
    dpi_ndpi_mask fds_bits[DPI_NUM_FDS_BITS];
}dpi_protocol_bitmask_struct_t;

#define DPI_PROTOCOL_BITMASK dpi_protocol_bitmask_struct_t

#define DPI_BITMASK_ADD(a,b)     DPI_SET(&a,b)
#define DPI_BITMASK_DEL(a,b)     DPI_CLR(&a,b)
#define DPI_BITMASK_RESET(a)     DPI_ZERO(&a)
#define DPI_BITMASK_SET_ALL(a)   DPI_ONE(&a)
#define DPI_BITMASK_SET(a, b)    { memcpy(&a, &b, sizeof(DPI_PROTOCOL_BITMASK)); }

#define DPI_ADD_PROTOCOL_TO_BITMASK(bmask,value)     DPI_SET(&bmask,value)
#define DPI_DEL_PROTOCOL_FROM_BITMASK(bmask,value)   DPI_CLR(&bmask,value)
#define DPI_COMPARE_PROTOCOL_TO_BITMASK(bmask,value) DPI_ISSET(&bmask,value)
#define DPI_SAVE_AS_BITMASK(bmask,value)  { DPI_ZERO(&bmask) ; DPI_ADD_PROTOCOL_TO_BITMASK(bmask, value); }

#define  READ_FROM_PCAP 3

enum pkt_status {
    PKT_OK,
    PKT_DROP
};

enum dissect_pkt_status {
    DISSECT_PKT_ORIGINAL,
    DISSECT_PKT_REASSEMBLE,
    DISSECT_PKT_FIANL
};

enum flow_dir {
    FLOW_DIR_DST2SRC,  //s2c
    FLOW_DIR_SRC2DST,  //c2s
    FLOW_DIR_MAX
};


enum TCP_STATUS
{
    FLOW_SYN = 1,
    FLOW_SYN_ACK,
    FLOW_ACK,
    FLOW_FIN_WAIT_1,
    FLOW_FIN_WAIT_2,
    FLOW_TIME_WAIT,
    FLOW_CLOSE,
};

enum RSS_USE_TYPE
{
    RSS_USE_CONF = 0,
    RSS_USE_FILTER_CTRL,
    RSS_USE_CUSTOM,
};

struct mac_forward_cnt_t
{
    uint8_t  mac[6];
    uint64_t cnt;
};

struct five_tuple
{
    uint8_t  ip_src[16];
    uint8_t  ip_dst[16];
    uint16_t port_src;
    uint16_t port_dst;
    uint16_t sctp_id;
    uint8_t  ip_version;
    uint8_t  proto;
};

struct flow_key {
    struct five_tuple outer;
    struct five_tuple inner;
};

struct traffic_stats {
    uint64_t ipkts;
    uint64_t ibytes;
    uint64_t imissed;
    uint64_t ierrors;

    uint64_t opkts;
    uint64_t obytes;

    uint64_t oerrors;
};

struct pkt_distribution
{
    uint32_t time;
    uint16_t len;
    uint8_t  direction;
};

struct pkt_line_end {
    uint8_t has_search;
    int linelen;
};

struct pkt_info {
    struct flow_info *flow; //归属于
    unsigned int            aclHashCode[SDT_ACL_MAX_CATEGORIES];//PKT的ACL命中ACL
    unsigned int            aclHashCnt;                         //PKT的ACL命中ACL个数
    int                     direction;

    /////////////////////////////////
    struct rte_mbuf         *mbuf;

    const uint8_t            *raw_pkt; //帧报文
    int                       pkt_len; //帧长度
    const struct dpi_ethhdr  *ethhdr;
    const struct dpi_iphdr   *iph4;
    const struct dpi_ipv6hdr *iph6;
    const struct dpi_tcphdr  *tcph;
    const struct dpi_udphdr  *udph;
    const struct dpi_sctphdr *sctph;
    uint8_t  proto;
    uint8_t  ipversion;

    const  uint8_t *payload;        //解析到哪里了
    uint32_t        payload_len;    //剩余长度 还有多少
};


struct stream_cache_st{
    uint8_t     *buff;
    uint32_t    buff_use;
    uint32_t    buff_left;
};


struct data_src
{
    /* 全局线路号 */
    uint8_t     Global_LineNO[16];

    /* X */
    uint16_t    LineType;

    /* Y */
    uint8_t     PosType;

    /* 高4bit：Z， 低4bit：设备类型 */
    uint8_t     SysNO_DeviceType;

    /* 设备序号*/
    uint8_t     DeviceSeq;

    /* 板卡类型      高4bit：板卡类型， 低4bit：板卡序号*/
    uint8_t     Board_Type_Seq;

    /* 发送端编号 */
    uint8_t     SendPort_Reserve;
}__attribute__((packed));



struct mac_packet_header
{
    //MAC信息
    uint8_t     dstMac[6];
    uint8_t     srcMac[6];
    uint16_t    wProtocol;
    uint8_t     mac_Seq;


    //数据类型 what
    /*
     * 0x00 - 0x06
     * 0x07 - 0x3f 保留
     * 0x40  匹配命中Ipv4
     * 0x41  未匹配命中Ipv4
     * 0x42  非IP数据
     * 0x43  IP线路统计数据
     * 0x44  IP统计结果数据
     * 0x45  匹配命中IPv6数据
     * 0x46    未匹配命中IPv6数据
     * 0x47    通联日志
     * 0x48    采样IP数据
     * 0x49-0x 7f  保留
     * 0x80
     * 0x81    GFP承载业务数据
     * 0x82    M－MPLS承载业务数据
     * 0x83    Nx64k承载业务数据
     * 0x84    10G SDH原始数据
     * 0x85    2．5G SDH原始数据
     * 0x86    622M SDH原始数据
     * 0x87    155M SDH原始数据
     * 0x88    智能复用数据
     * 0x89    SDH支路原始数据
     * 0X8A-0x ff  保留
    */
    uint8_t     DataType;

    //数据长度
    uint16_t    wDataLen;

    //数据来源 where
    struct data_src    Datasrc;

    //数据时间戳 when
    uint8_t     TimeStamp[8];

    //链路信息，后面紧跟报文 what
    uint8_t     bLinkType;
    uint8_t     bLinkLen;
} __attribute__((packed));

#if 1  /*******************************sdt 统计结构  *******************************************/
#define SDT_MAX_RULE_NUM      10000
#define SDT_MAX_OUT_RING      4
#define SDT_RULE_KEY_SIZE     64

struct stu_rule_pcap
{
    FILE *pcap_fp;
    FILE *pcap_log_fp;
    uint32_t  pcap_time;
    uint32_t  pkt_bytes;   /* 到目前为止输出总字节数 */
    uint64_t  max_pcap_size; /* 每个pcap文件.writing最大size Bytes*/
    uint32_t  max_time;      /* 每个pcap文件.writing状态存活最长时间*/
    char pcap_name[COMMON_FILE_PATH];
};

struct stu_rule_event
{
    FILE *event_fp;
    uint32_t  event_time;
    uint32_t  event_cnt;   /* 到目前为止输出event条数 */
    uint32_t  max_event_num;  /* 每个event文件最多存条数*/
    uint32_t  max_time;       /* 每个event文件.writing最大存在时间*/

    char event_name[COMMON_FILE_PATH];
};

struct stu_rule_syslog
{
    FILE *syslog_fp;
    uint32_t  syslog_time;
    uint32_t  syslog_cnt;   /* 到目前为止输出event条数 */

    char syslog_name[COMMON_FILE_PATH];
};


struct stu_rule_statistics{
    time_t    rule_first_match_time;
    time_t    rule_last_match_time;
    uint32_t  rule_report_cnt;       /* 规则命中上报计数 */
    uint32_t  rule_report_mn_cnt;    /* 对于event配置N秒上报M次使用 */
    uint32_t  rule_match_hits;       /* 规则命中数 */
    uint32_t  rule_match_flows;      /* 规则命中流计数 */
    uint64_t  rule_match_pkts;       /* 规则命中报文数 */
    uint64_t  rule_match_bytes;      /* 规则命中字节数 */
};


struct stu_statistics_result{
    time_t    rule_first_match_time;
    time_t    rule_last_match_time;

    uint32_t  match_pps;
    uint32_t  match_fps;
    uint32_t  match_bps;

    uint32_t  match_cnt;
};


typedef struct _sdt_out_status
{
    uint8_t        flag;
    int            in_used; //互斥量
    SdtMatchResult *match_result;
    struct stu_rule_pcap   pcap_status;
    struct stu_rule_event  event_status;
    struct stu_rule_syslog syslog_status;

    uint32_t                     report_mn_sec; /* 只有规则action是event并且是每N秒上报M次才会用到， 该参数标识N秒的数值*/
    uint32_t                     time_count;    /* 只有规则action是event并且是每N秒上报M次才会用到,当report_mn_sec有值，才计数*/
    struct stu_rule_statistics   statistics_web_before;  /* 保持web端轮询获取统计前一次记录*/
    struct stu_rule_statistics   statistics_before;      /* [暂时没用到]上报前一次统计记录 */
    struct stu_rule_statistics   statistics_current;     /* 上报当前统计记录，上报统计为增量 */
    struct stu_rule_statistics   thread_statistics[MAX_FLOW_THREAD_NUM]; /* 每个线程命中规则统计 */
    int log_type;   //匹配的协议类型
    struct mac_packet_header   mac_hdr;

    struct mac_packet_header   first_mac_hdr;
}sdt_out_status;

#endif

typedef struct std_mult_matches{
    uint8_t             sdt_result_flag;
    sdt_out_status      *flow_rule_tab;
    SdtMatchResult      match_result;
}sdt_mult_matches;

struct sdt_store_rules{
    uint8_t report_flag;
    uint32_t rule_hash_code;
};

struct sdt_rule_record{
    uint32_t rule_hash_code;
    void     *sdt_rule_status;
};

typedef struct _SdtFlowStatus
{
    struct list_head  pkt_stream_head;

    uint32_t          pkt_stream_num;

    uint32_t          pkt_stream_bytes[PKT_STREAM_DIR_NUM];
    uint32_t          single_stream_bytes[PKT_STREAM_DIR_NUM];
    uint32_t          tcp_rsm_bytes[PKT_STREAM_DIR_NUM];

    SdtEngine        *pEngine;

    //命中 LINK模式规则
    uint32_t         sdt_rule_cnt; /* 该流命中规则数 */
    uint32_t         sdt_rules_rd[SDT_MAX_MATCH_RULES]; /* 该流命中规则ID */
    sdt_out_status  *sdt_rules_status[SDT_MAX_MATCH_RULES]; /* 该流命中规则后的输出 */

    //命中 IPFF模式规则
    uint32_t        ipff_rule_cnt;
    uint32_t        ipff_rules_rd[SDT_MAX_MATCH_RULES];
    sdt_out_status *ipff_rules_status[SDT_MAX_MATCH_RULES];

    uint8_t          match_direction;

    void                     *sdt_rsm;
    struct stream_cache_st   sdt_ip_cache[PKT_STREAM_DIR_NUM];
    struct stream_cache_st   sdt_l4stream_cache[PKT_STREAM_DIR_NUM];
}SdtFlowStatus;


#define ACLHASHHITMAX  10
#define PROTO_NAME_LEN 25
#define PROTO_TYPE_LEN 25
#define CACHE_MAX      10  //协议未识别前 最多缓存多少?
//会话的数据结构
struct flow_info {
    struct work_process_data *workflow;
    rte_atomic16_t  ref;
    struct flow_key tuple;           //正向五元组
    struct flow_key tuple_reverse;   //反向五元组
    GHashTable      *ghash_link_action;//记录已命中的规则
    uint32_t aclHashHit[2][ACLHASHHITMAX]; //双向记录，便于直接判定是否已经命中!
    /*flow 是否从hash 表中删除，由于 flow 的生命周期会延伸到写日志
    * 超时会不断重置， 超时回调每次都会删除 hash表，会导致崩溃
      只要第一次超时从 hash 表中删除元素即可*/
    uint8_t hash_deleted;
    struct list_head node_timeout;   //超时链表的节点
    struct rte_timer timer;         // 定时器 handle
	uint64_t         timer_cnt;
    uint8_t flow_direction_flag;     // 0 syn包丢失，无法判断方向，1-tuple为 c2s， 2-tuple为s2c
    uint32_t thread_id;               //线程下标id

    void*   pTrailer;                //标签指针
    void*   pSDTMacHeader[2];        // sdt 报文头信息
    struct  data_link_layer_t data_link_layer;         //以太层硬件信息

    uint8_t vlan_flag;               //is vlan ?
    uint8_t is_mpls;                 //is mpls ?


    uint8_t ip_version;              //ip版本
    uint8_t ttl;                     //ttl
    uint8_t up_ttl;                  //上行首包ttl
    uint8_t down_ttl;                //下行首包ttl

    uint16_t guessed_protocol_id;
    uint16_t real_protocol_id;       //协议识别后的应用层协议id
    uint16_t slave_protocol_id;      //记录着哪些协议依赖了我
    uint16_t high_app_proto_id;      //高层应用层协议id,如Wechat,skype等
    uint64_t flow_id;                //flow的唯一id，现在是时间戳
    uint32_t flow_cycle;
    int      sub_flow_id;

    struct pkt_line_end pkt_first_line;//很多协议识别都会找换行，当时就存到flow中来了

    uint32_t src2dst_packets;        //源到目的的包数
    uint32_t dst2src_packets;        //目的到源的包数
    uint64_t src2dst_bytes;          //源到目的的字节数
    uint64_t dst2src_bytes;          //目的到源的字节数
    uint64_t src2dst_payload_len;    //源到目的的净荷字节数
    uint64_t dst2src_payload_len;    //目的到源的净荷字节数
    uint64_t last_seen;              //最后有数据包关联到会话的时间
    uint64_t time_tmp;              //最后有数据包关联到会话的时间

    uint8_t  proto_type;                      //协议栈属性
    uint8_t  handshake;                       //握手过程
    uint8_t  routers_flag;                    //多路由标识:目前取的是路由记录 Record Router选项的类型
    uint8_t  ip_link_type;                    //ip链接类型
    uint8_t  direction;                       //会话方向
    uint8_t  timeout_flag;                    //超时标志
    uint8_t  check[FLOW_DIR_MAX];             //上下行校验和: 完整性标识
    uint16_t src2dst_max_packet_len;          //上行最大包长
    uint16_t src2dst_min_packet_len;          //上行最小包长
    uint32_t src2dst_max_packet_interval;     //上行最大包间隔
    uint32_t src2dst_min_packet_interval;     //上行最小包间隔
    uint16_t dst2src_max_packet_len;          //下行最大包长
    uint16_t dst2src_min_packet_len;          //下行最小包长
    uint64_t dst2src_max_packet_interval;     //下行最大包间隔
    uint64_t dst2src_min_packet_interval;     //下行最小包间隔

    uint8_t  up_payload_len_cnt;
    uint16_t up_payload_len_set[SDT_PAYLOAD_LEN_SET_NUM];

    uint8_t  down_payload_len_cnt;
    uint16_t down_payload_len_set[SDT_PAYLOAD_LEN_SET_NUM];

    const uint8_t *l3;
    uint16_t      ip_len;
    uint32_t      linktype;/* ADD_BY chunli: 用于标识 L3 的数据类型 */
    uint32_t      capflag; /* ADD_BY chunli: 用于标识 是否需要抓包  */
    uint16_t      capnum;

    DPI_PROTOCOL_BITMASK excluded_protocol_bitmask;        //已经排除的协议识别的标志

    uint32_t reassemble_pkt_src2dst_num;         //源到目的重组的包数
    uint32_t reassemble_pkt_dst2src_num;         //目的到源重组的包数
    uint32_t rsm_src2dst_len;                    //源到目的重组的总长度
    uint32_t rsm_dst2src_len;                    //目的到源重组的总长度
    struct list_head reassemble_src2dst_head;    //源到目的的重组链表
    struct list_head reassemble_dst2src_head;    //目的到源的重组链表
    uint8_t  init_tcp_session[FLOW_DIR_MAX];     //发起TCP连接的方向
    uint8_t  ack_tcp_session[FLOW_DIR_MAX];      //响应TCP连接的方向

    uint8_t  tcp_status;        //tcp状态
    uint8_t  tcpsyncounter;     //会话syn计数
    uint8_t  tcpsynackcounter;  //会话syn-ack计数
    uint8_t  tcpackcounter;     //会话ack计数
    uint8_t  tcppshcounter;     //会话psh计数
    uint8_t  tcpfincounter;     //会话fin计数
    uint8_t  tcprstcounter;     //会话rst计数
    uint8_t  tcpurgcounter;     //会话urg计数
    uint8_t  tcpececounter;
    uint8_t  tcpcwrcounter;
    uint8_t  tcpnscounter;

    uint16_t  up_window;         //上行syn包窗口
    uint16_t  down_window;       //下行syn包窗口
    uint32_t up_seq;            //上行syn包序号
    uint32_t down_seq;          //下行syn包序号

    uint64_t create_time;       //会话创建时间
    uint64_t end_time;          //会话结束时间
    uint64_t tcpsyntime;        //会话syn时间戳
    uint64_t tcpsynacktime;     //会话syn ack时间戳
    uint64_t tcpacktime;        //会话syn ack ack时间戳
    uint8_t *synData;           //syn IP packet
    uint32_t synDataLen;        //syn IP packet length
    uint8_t *synackData;        //syn ack IP packet
    uint32_t synackDataLen;     //syn ack IP packet length

    // 由于tcp重组送往解析的数据有滞后性, flow的信息可能是超前且混乱的.
    // 解决办法: 两个方向的信息单独保存, 读取时按实际的方向读取
    struct dpi_ethhdr   session_ethhdr[FLOW_DIR_MAX]; // 记录双向的以太层信息


    int disbt_index;            //会话总包数
    struct pkt_distribution distribution[PKT_DISTRIBUTION_NUM]; //会话上的数据包分布

    uint64_t ovpn_session_id;
    uint8_t ovpn_counter;

    uint8_t   userdata[8];      //应用层协议的关键信息,放在这里以免申请app_session
    void      *app_session;     //应用层协议的数据结构
    void      *conv_session;
    uint8_t   reassemble_flag;  //针对乱序，0表示流需要排序缓存，1表示该流数据部分不需要缓存
    uint16_t  tunnel_ip_len;

    uint16_t  packet_stage;
    uint32_t  msg_id;

    uint8_t    blacklist_flag;        //黑名单标志
    uint8_t    whitelist_flag;        //白名单标志
    uint8_t    sslh_enc_pcap_0;       //ssl/ssh正向加密报文数
    uint8_t    sslh_enc_pcap_1;       //ssl/ssh反向加密报文数
    uint8_t    sslh_enc_written;      //ssl/ssh加密报文是否写入pcap
    struct sslh_enc_st *sslh_pcaphdr; //ssl/ssh加密报文头列表

    uint16_t   useless_tbl;           //无用的tbl
    uint64_t   timestamp;             //每包的时间戳
    char       *path;                 //读pcap包模式的pcap路径

    const struct pkt_info *pkt;       // 记录当前报文头部信息
    uint16_t   packet_len;
    uint16_t   port_src;
    uint16_t   port_dst;
    uint16_t   drt_port_src[2];
    uint16_t   drt_port_dst[2];

    uint16_t    match_data_len;  /* tcp重组回调解析函数中的待解析的报文长度. 用于匹配信息统计,超时预警.
                                    传递路径: flow -> tbl -> record */

    char *err_pcap;                   // 解析失败-报文转储
    int   err_pcap_hold;              // 解析失败-报文转储
    int   err_pcap_dump;              // 解析失败-报文转储
    uint64_t    flow_bit1_num;
    uint64_t    flow_bit_total;

    uint8_t     flow_compressed_count;
    uint8_t     flow_is_encripted;

    SdtFlowStatus           sdt_flow;
    uint8_t   special_flag;
	uint32_t		last_ttl; //flow超时, 会将TCP_RSM缓存的数据输出解析, HTTP输出TBL时, 需要TTL字段
    char   *msg;

    char        proto_info[256];       // 共性字段中的协议栈字段, ex: ETH.IPv4.TCP.HTTP.UNKNOWN等
    uint16_t    proto_layer[32];
    uint8_t     proto_layer_cnt;

    uint32_t    tunnel_id;               // gtp-u协议的teid字段 或者L2TP协议的 tunnelID
    uint32_t    tunnel_id_repl;          // teid字段副本
    uint8_t     intflag;                  //对于TCP流,序列号不连续视为不完整流

    /**************** 暂存TCP报文 **********************/
	int                  cache_index;       //暂存TCP报文
    struct
    {
        struct rte_mbuf    *mbuf;   // tcphdr, payload地址都来自于mbuf, 因此需要注意mbuf的生命周期
        struct dpi_tcphdr  *tcphdr;
        unsigned char      *payload;
        int                 payload_len;
    }cache[CACHE_MAX];
    /**************** 暂存TCP报文 **********************/
    TCP_RSM         *rsm;       // 新版 TCP 重组接口
    struct decode_t *decode;    //新版 解析模块接口

    //存储流的stream 信息 只128字节
    struct stream_flow_t {
        char   byte[128];
        int    index;
    } both_stream, up_stream, down_stream; //上下,请求,响应

    /*************** 内存管理器 ********************/
    MemoryAlloc     *memAc;
    /*************** 内存管理器 ********************/

    struct p327_header p327_header[2];//存储主对方双向mac作为标签信息解析数据

    // 通联日志相关
    uint8_t         tll_flag;
    DATA_STATE_T    tll_msg;                  // 通联日志消息结构体
#ifdef ENABLE_ARKIME
    uint16_t tcp_flag;

  uint64_t     payloadsrchex;      //上行传输层负载HEX。
  uint64_t     payloaddsthex;       //下行传输层负载HEX。

    // Arkime文件信息 - 链表结构，支持多文件和多帧
    // 格式：-1*file_num, file_pos1, file_pos2, ..., -1*next_file_num, file_pos1, ...
    struct arkime_file_pos_node *arkime_file_pos_list;  // 文件位置链表头
#endif
    uint64_t map_stream_id;  //ynao用于关联FLOW_M
};

int get_flow_direction(struct flow_info *flow);

// flow statistics info
typedef struct process_stats {
    uint16_t max_packet_len;
    uint16_t min_packet_len;
    uint32_t flow_count;
    uint32_t guessed_flow_protocols;
    uint64_t raw_packet_count;
    uint64_t ip_packet_count;
    uint64_t total_wire_bytes;
    uint64_t total_ip_bytes;

    uint64_t inc_flow_num;
    uint64_t tcp_count;
    uint64_t udp_count;
    uint64_t sctp_count;
    uint64_t mpls_count, pppoe_count, vlan_count, fragmented_count;
    uint64_t packet_len[6];

    uint64_t tll_count;         // 通联日志统计信息

    uint32_t flow_stats[PROTOCOL_MAX];
    uint64_t flow_stats_total[PROTOCOL_MAX];
    uint64_t flow_stats_total_pkts[PROTOCOL_MAX];
    uint64_t flow_stats_total_bytes[PROTOCOL_MAX];

    struct traffic_stats traffic[TRAFFIC_NUM];
} process_stats;

// workflow main structure
typedef struct work_process_data {
    pthread_t pthread;
    uint32_t  thread_id;
    uint32_t  flow_cycle;
    uint32_t  num_allocated_flows;
    uint64_t  last_time;
    uint64_t  pkt_interval;
    uint64_t  last_idle_scan_time;

    uint64_t  timestamp;
    char*     path;
    DpiHash  *hash;
    struct process_stats stats;
    struct list_head timeout_head[TIMEOUT_MAX];

    struct rte_mbuf *mbuf;      // 当前线程正在解析的mbuf
    uint16_t  payload_len;
    uint8_t   ip_version;
    uint8_t   proto_type;
    uint16_t  dst_port;
    uint8_t   ip_link_type;

    uint8_t         l4_protocol;
    const uint8_t  *l4_packet;
    uint16_t        l4_packet_len;

    uint8_t   is_mpls;         //mpls  flag
    uint8_t   vlan_flag;       //vlan  flag
    uint8_t   is_tll;          //sdx 通联日志帧 flag
    struct    data_link_layer_t data_link_layer;    //ether info
    char      pcap_file_name[COMMON_FILE_PATH];

    char      file_tbl_dir[COMMON_FILE_PATH];
    char      file_tbl_name[COMMON_FILE_PATH];
    FILE      *tbl_fp;

    uint32_t  file_count;
    uint32_t  timeout_sec;

    SdtEngine  *pEngine;
    uint8_t    *block_buff;

    uint16_t  layers[32];   // 每一帧层级记录
    uint8_t   layer_cnt;    // 每一帧层级计数, 每一帧到来之前置空
    struct p327_header p327_header;//mac作为标签信息解析数据
#ifdef ENABLE_ARKIME

    // Arkime文件信息 - 当前包的文件信息
    uint64_t  arkime_file_num;  // 当前包的文件序号
    uint64_t  arkime_file_pos;  // 当前包在文件中的偏移量
#endif
} dpi_workflow_t;

struct config_http
{
    int         error_pcap_dump        ; //转储Error  PCAP 开关
    int         tcp_out_of_order       ; //TCP 抖动容忍
    int         tcp_padding_len        ; //TCP 缺包小于N时 允许padding

    int         http_skip_body         ; //跳过HTTP Body解析
    int         http_strip_cache_size  ; //http 切割缓存大小
    uint8_t     http_switch_store_file ; //http 是否存储文件开关
    uint8_t     http_switch_response_file;  // http 下行数据是否要写文件
    int         http_file_size         ; //http 存储文件最大值，要小于cache szie

    char        tcp_padding_str[512]   ; //TCP PADDING 字符

    char        http_content_filter[512];
    char        *filter_split[COMMON_FILTER_NUM];
    regex_t     reg[COMMON_FILTER_NUM];
    int         num;
    uint8_t     drop_no_content_type;
    uint8_t     http_tax_switch;
    uint8_t     http_exquisite_switch;

};

struct global_config {
    time_t   g_now_time;               //时间戳,秒
    uint64_t g_now_time_usec;          //时间戳,微秒

    int data_source;
    uint32_t mac;
    pthread_t master_threadid;

    // IPC查询地理位置开关
    uint8_t  ip_position_switch;      //获取ip地理位置开关
    uint8_t mmdb_switch;
    MMDB_s mmdb_asn;
    uint8_t mmdb_asn_switch;
    MMDB_s mmdb_city;
    uint8_t mmdb_city_switch;
    ip2region_entry entry;
    uint8_t ip2region_switch;

    uint8_t stop_rcv_pkts;          //停止收包
    uint8_t exit_process;           //退出程序
    uint8_t start_offline_dissect;
    uint8_t stop_dissect;
    uint8_t stop_write_tbl;
    uint8_t receive_flag_per_core[RCV_CORE_MAX_NUM];
    uint8_t dissect_flag_per_core[MAX_FLOW_THREAD_NUM];
    uint8_t tblwrite_flag_per_core[TBL_RING_MAX_NUM];
    uint8_t conv_flag_per_core[MAX_FLOW_THREAD_NUM];

    // core config
    uint8_t  nb_rxq;                 // 每个网卡的收报队列数,收包线程数,可启动时配置
    uint32_t dissector_thread_num;   // 解析线程个数,可启动时配置
    uint32_t flow_aging_thread_num;  // 流表老化线程数量
    uint32_t flow_conv_thread_num; // 流表关联线程数量

    uint8_t  aging_core_id[MAX_THREAD_NUM];
    uint8_t  conv_core_id[MAX_FLOW_THREAD_NUM];
    uint8_t  pkt_rcv_core_id[RCV_CORE_MAX_NUM];
    uint8_t  dissector_core_id[MAX_FLOW_THREAD_NUM];
    uint8_t  log_thread_type; // 1 匹配tbl线程; 2 流表tbl线程，-l 与 -f 只能启动一个
    uint8_t  log_thread_num;
    uint8_t  log_core_id[MAX_FLOW_THREAD_NUM];
    uint8_t  app_match_thread_num;
    uint8_t  app_match_core_id[TBL_RING_MAX_NUM];

    unsigned int mempool_cache_size;
    unsigned int packet_ring_size;  // 解析线程的缓冲队列大小； 可启动时配置

    int      max_pkt_burst;
    uint16_t mbuf_size;
    uint16_t max_pkt_len;
    uint32_t nb_mbuf;
    uint32_t tcp_rsm_out_of_order;
    uint32_t max_hash_node_per_thread;
    uint32_t max_flow_num;
    uint32_t tcp_reassemble_mempool_num;
    uint32_t pkt_stream_mempool_num;
    uint32_t max_conversation_hash_node;
    uint32_t tbl_ring_size;
    uint32_t tbl_memool_num;
    uint32_t tbl_log_content_256k_num;
    int16_t enable_sbp;               //#DPDK 网卡硬件 RX 接收错误帧 [0: 不接受坏帧. 1:接受坏帧]
    int16_t disable_crc;              //#DPDK 网卡硬件 TX 禁止填充CRC[0: 填充CRC.    1:不填充CRC]
    uint16_t write_tbl_maxtime;       //写一个tbl文件，从开始到现在用时最大不能超过的时间（单位：秒）
    uint16_t tcp_flow_timeout;        //tcp会话的超时时间,可动态配置
    uint16_t udp_flow_timeout;        //udp会话的超时时间,可动态配置
    uint16_t sctp_flow_timeout;       //sctp会话的超时时间,可动态配置
    uint16_t tcp_flow_timeout_del;        //tcp会话的超时时间,可动态配置
    uint16_t dissector_thread_conv_timeout;//线程超时器时间

    uint16_t tcp_identify_pkt_num;    //tcp会话的最大报文识别个数,可动态配置
    uint16_t udp_identify_pkt_num;    //udp会话的最大报文识别个数,可动态配置
    uint16_t sctp_identify_pkt_num;   //sctp会话的最大报文识别个数,可动态配置

    uint16_t tcp_resseamble_max_num;  //tcp会话重组的最大数据包个数可动态配置
    uint32_t log_max_num;             //每个日志文件的最大条目数可动态配置

    uint8_t  protocol_switch_reverse; //协议开关的反转开关
    uint8_t  protocol_switch[PROTOCOL_MAX];//每个协议的开关 可动态配置
    uint8_t  conversation_switch;     //关联协议识别开关
    uint16_t conversation_identify_pkt_num; // 关联协议的最大识别个数; 主要应对rtl回放报文会乱序
    uint8_t  flow_log_switch;         //流日志开关
    uint32_t idle_scan_period;        //两次超时时间间隔,usec
    int      max_timeout_num;         //每次超时的最大流数
    int      socketid;                //查看使能网卡所在socketid
    int      unknown_cache_max;       //未识别成功报文缓存个数
    int log_output_level;             //日志输出级别
    char log_output_level_str[16];

    char log_output_path[COMMON_FILE_PATH];        //日志输出级别
    FILE *fp_log;

    char record_permin_path[COMMON_FILE_PATH];     // 每分钟记录统计文件存放路径
    char record_perday_path[COMMON_FILE_PATH];     // 每天记录统计文件存放路径
    uint16_t record_update_time;      // 统计文件更新时间间隔，每分钟

    char devname[64];
    char devno[64];                   // 设备号码< 1.config.ini配置的号码 2."AUTO"或无指定：使用IP或者MAC地址生成设备号码>

    uint8_t show_operator_switch;     //是否显示运营商信息
    uint8_t show_task_id;             //是否任务id
    Trailer_Type  trailer_type;       //trailer类型,HW,RT,PH,JL
    Net_Type      net_type;           //网络类型,移动|固网
    size_t  trailer_length;           //trailer结构长度
    char operator_type[64];           // 运营商配置信息，AUTO表示使用数据流中解析的运营商信息，其他表示使用配置文件提供的信息

    char tbl_out_dir[COMMON_FILE_PATH];            //tbl日志输出目录
    char sdx_mount_dir[128];                       //tbl 挂载目录

    char dpi_field_dir[COMMON_FILE_PATH];          // 字段表输出目录 add by liugh
    char dpi_field_json_dir[COMMON_FILE_PATH];     // json字段映射表目录

    /************* output config *************/
    uint8_t adapt_type;                 // 适配类型, 0 lua, 1 json
    char    adapt_dir_lua[128];         // lua 适配目录
    char    adapt_dir_clibs[COMMON_FILE_PATH];
    uint8_t  date_subdir_flag;    //是否增加日期子目录http/20240515/

    uint8_t tbl_out_minute_dir;
    char    tbl_out_dir_time_dir[256];			//tbl日志输出目录

    char     tbl_filenum_perday_path[256];     // 每天记录统计文件存放路径
    char      yv_data_suffix[COMMON_SOME_TYPE];
    int       yv_record_type;

    uint8_t rtl_flag;                 //退出

    uint8_t exit;                     //退出


    int timeout_index;                //会话链表超时下标

    uint8_t wxf_filter;               // 进一步提取微信有用数据开关

    /*内容还原开关*/
    uint8_t pop_content;
    uint8_t smtp_content;
    uint8_t imap_content;
    uint8_t ftp_content;
    uint8_t telnet_content;
    uint8_t tftp_content;

    char pcap_flow_pro[PATH_MAX]; //存pcap包 协议开关
    char pcap_flow_dir[PATH_MAX]; //存pcap包 文件路径
    int  pcap_flow_num          ; //存pcap包 写入条数
    int  pcap_flow_size         ; //存pcap包 写入大小
    int  pcap_flow_rate         ; //存pcap包 采样率

    int   error_pcap_size        ; //解析失败-错包转储
    char  error_pcap_dir[512]    ; //解析失败-错包转储

    struct  config_http  http;

    uint8_t ssl_pcap;
    uint8_t ssh_pcap;

    uint8_t write_one_esp;

    uint8_t ring_rss_mode;
    uint8_t rss_use_type[RTE_MAX_ETHPORTS]; // enum RSS_USE_TYPE, 表明不同的rss使用方式

    uint8_t show_traffic_speed_all;
    uint8_t write_l2tp_inner;
    uint8_t ssl_ssh_min_enc_num;
    uint8_t offline_read_over_stop;
    uint8_t task_name_switch;

    //重复tbl输出开关
    uint8_t h323_lazy;
    uint8_t ldap_lazy;
    uint8_t cldap_lazy;

    uint8_t dns_filter;
    uint8_t dns_filter_only_request;
    uint8_t sip_filter;
    uint8_t gtp_filter;
    uint8_t x509_write_switch;
    uint8_t x509_verify_switch;
    uint8_t x509_fingerprint_alg;
    uint8_t x509_whitelist_switch;
    char    x509_whitelist_filename[64];

    uint8_t ssl_flow_mode;
    uint8_t ssh_packet_mode;
    uint8_t rdp_packet_mode;
    uint8_t rdp_mutex_switch;

    uint8_t https_whitelist_switch;
    uint8_t https_blacklist_switch;
    uint8_t https_default_switch;
    char https_whitelist_filename[32];
    char https_blacklist_filename[32];

    char http_status_whitelist[32];

    uint8_t dns_whitelist_switch;
    uint8_t dns_blacklist_switch;
    uint8_t dns_default_switch;
    char dns_whitelist_filename[32];
    char dns_blacklist_filename[32];

    uint8_t ipv4_blacklist_switch;
    char ipv4_blacklist_filename[32];

    unsigned  ssh_login_valid_num;
    unsigned  rdp_login_valid_num;
    int       rdp_identify_only_port;
    int       login_success_time;
    char      task_id[32];
    char      task_name[64];
    uint8_t   task_name_len;

    uint8_t   pcap_port_id;
    /************* input config **************/
    uint8_t input_type;                //输入类型, 0 inline 1 pcap
    char pcap_dir[128];               //pcap文件目录

    int  http_content_limite; //HTTP 还原限制的控制.
    int  ssl_or_x509;


    char         work_mode[NORMAL_ARRAY_LEN];
    char         process_socket_ip[NORMAL_ARRAY_LEN];
    uint8_t      work_mode_flag;
    uint16_t     process_socket_port;
    uint16_t     vtysh_socket_port;
    uint8_t      dpi_plugin;

    //------------------------  sdt 配置变量 ----------------------------
    uint8_t      sdt_out_core[TBL_RING_MAX_NUM];
    uint8_t      match_status_switch;
    uint8_t      sdt_ip_with_mask;      //是否强制 规则语法 IP带上掩码
    uint8_t      sdt_switch_mult_hit;
    int          sdt_out_thead_num;
    char         time_str[32];
    char         sdt_rules_file[COMMON_FILE_NAME];
    char         sdt_rules_field[COMMON_FILE_NAME];
    char         sdt_out_pcap_dir[COMMON_FILE_PATH];
    char         sdt_out_event_dir[COMMON_FILE_PATH];
    char         sdt_out_syslog_dir[COMMON_FILE_PATH];
    char         sdt_pcap_identification[COMMON_FILE_PATH];
    uint32_t     sdt_out_pcap_max_MB;
    uint32_t     sdt_out_pcap_max_bytes;
    uint32_t     sdt_out_pcap_max_time;
    uint32_t     sdt_out_event_max_line;
    uint32_t     sdt_rule_max_num;
    uint32_t     sdt_out_ring_size;

    char        *sdt_rules_default_action;
    char         sdt_out_produce_data_dev_name[256];
    char         sdt_out_produce_data_dev_ip[32];

    uint32_t     sdt_local_port;
    char         sdt_web_addr[COMMON_SOME_TYPE];
    uint8_t      sdt_reload_rule;
    uint8_t      sdt_reflect_rule;


    uint8_t      sdt_user_id;
    uint32_t     sdt_block_buff_size;
    uint32_t     sdt_block_buff_keep;
    uint32_t     sdt_block_unctn_num;

    uint32_t     sdt_cache_max_pkts;
    uint32_t     sdt_match_max_rules;
    uint8_t      sdt_record_match_rule; /* 流表中是否记录已经命中的规则 0-不记录，1-记录，默认1*/



    char         sdt_kafka_ip[32];
    uint8_t      sdt_send_kafka;

    char        data_from[64];          // 数据来源：ML，KS，ZW
	uint8_t		sigtype;				/* 0x1 : 信号处理节点机
											0x2: IP统计设备
											0x3: 业务解承载设备
											0x4:整体信号分流器
											0x5:通道化信号分流器（高阶通道化）
											0x6:高阶虚级联处理设备（通道化分流器）
											0x7:低阶虚级联处理设备0x8:ATM处理设备
											0x9:DDN处理设备
											0xa－0xf:保留
                                          */
    uint16_t    ftp_port;               //FTP固定端口


	uint8_t		fwd_type;  /* 转发类型:
							 * FWD_MAC_FRAME	原始MAC帧转发
							 * FWD_DATA_COLLECT	数据采集转发
							 * FWD_UNKNOWN		未知类型
							*/

	uint8_t		sdt_dst_mac_fwd_collect[6];  // 当数据采集开启时，转发到目的mac地址
	uint8_t		sdt_data_collect_enable; /* 数据采集开关:
										  * 1=命中的包不再落包为pcap，被转发至sdt_dst_mac_fwd_collect地址
										  * 2=已开启，且sdt_dst_mac_fwd_collect被设置
										  * 0=关
										  */

    uint8_t		sdt_src_mac_forward[6]; // sdt命中后转发的的src mac地址
	uint8_t		sdt_dst_mac_forward[6]; // 转发到指定的dst mac地址
	uint8_t		sdt_mac_forward_flag;

	uint16_t	sdt_fwd_mbuf_size;  // 用于包转发的mbuf大小
	uint8_t		nb_txq;				// 每个网卡的发包队列数  目前只有包转发时用到
  uint8_t     hardware_zidnumber;// 设备zd号
  char     mdsecdeg[512];  // 元数据密级
  char filesecdeg[512]; // 数据文件密级
  char secdegpro[512];      // 安全等级保护
  uint8_t     sdx_out_all_field; //json输出所有字段

    dictionary *ini;
    struct sdx_config_variable sdx_config;
    struct web_config_variable web_config;

    // Elasticsearch配置
    struct {
        char es_host[256];          // ES服务器地址
        int es_port;                // ES端口
        char es_username[64];       // ES用户名
        char es_password[64];       // ES密码
        char node_name[64];         // 节点名称
        int timeout_seconds;        // 超时时间
        int enabled;                // 是否启用ES发送
        char jade_file_patch[64];   //jade文件生成路径
    } es_config;

    uint8_t      data_input_with_yn470_line_info;
    uint8_t      data_input_scanning_infinite;
    uint8_t      data_input_scanning_do_rename;
    char         data_input_scanning_ignore[100];
    char         finish_suffix[32];    // 对处理完成的文件名称尾部追加的后缀名
    uint8_t      data_input_scaning_del_file_flag;  //是否删除解析后的离线文件
    uint8_t      opened_fd_flag[TBL_RING_MAX_NUM];  //写tbl线程是否有未关闭fd标识
    uint32_t     pause_scan_pcap;
    uint8_t      _327_common_switch;

    // output
    uint8_t output_write;

    //2024-11-20 牡丹江现场 要求 帧转发没带MAC的要走默认的MAC地址池
    int  sdx_mac_num;
    char sdx_mac[10][6];

    uint8_t decode_identity_switch;//decode tcp重组非标端口识别开关
    uint8_t     test_mode;  // 测试模式

    /****************内容还原配置**************/
    uint8_t  ftp_identify_flag;      /* ftp 识别方式，默认端口，0-端口识别，其他值 标准识别会有大量误识别 */
    uint8_t  ftp_packet_mode;        /* ftp 支持报文模式解析 */

};

struct protocol_record {
    volatile uint64_t record_permin[PROTOCOL_MAX];
    volatile uint64_t record_permin_circle[PROTOCOL_MAX];    // 当每分钟数据量溢出uint64_t时，此处+1
    volatile uint64_t record_perday[PROTOCOL_MAX];
    volatile uint64_t record_perday_circle[PROTOCOL_MAX];    // 当每天数据量溢出uint64_t时，此处+1
    pthread_mutex_t record_mutex;
    time_t last_record_time;
    int fd_min;    // 按分钟记录的文件
    int fd_day;    // 按天记录的文件
    char *proto_name[PROTOCOL_MAX];
};


/* 关联的协议表，下规则时，切换协议的开关会用到 */
typedef struct {
	int						value;
	struct int_to_string	str_list[PROTOCOL_MAX];
} int_to_stringlist;


#define PORT_PROTO_NUM_MAX 4

struct guess_proto_data{
    uint16_t proto[PORT_PROTO_NUM_MAX];
};

//新本 TCP_RSM   -- 承上启下 层.
struct decode_t {
    const char *name;
    uint8_t  identify_type;         // 识别类型  端口、内容
    int         (*decode_initial)(struct decode_t *decode);
    int         (*pkt_identify)(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);
    int         (*pkt_arrive) (struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len); //单包的真实到达
    int         (*pkt_dissect) (struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);//重组后有序的报文
    int         (*pkt_miss)(struct flow_info *flow, uint8_t C2S, uint32_t miss_len);
    void        (*flow_finish)(struct flow_info *flow);
    int         (*decode_destroy)(struct decode_t *decode);
};
extern struct decode_t *decode[];

struct check_conv_data{
	uint16_t proto;
        int (*conv_dissect)(char *key, uint8_t *this_value,uint8_t* last_value);
        int (*conv_timeout)(char *key, uint8_t *value_ptr);
};
//基于端口的识别
extern struct decode_t *decode_port_on_tcp_array [65535][PORT_PROTO_NUM_MAX];
extern struct decode_t *decode_port_on_udp_array [65535][PORT_PROTO_NUM_MAX];
extern struct decode_t *decode_port_on_sctp_array[65535][PORT_PROTO_NUM_MAX];
int decode_on_port_tcp (uint16_t port, struct decode_t *decode);
int decode_on_port_udp (uint16_t port, struct decode_t *decode);
int decode_on_port_sctp(uint16_t port, struct decode_t *decode);

typedef enum dpi_identify_type_t
{
    DPI_IDENTIFY_PORT = 0,          // 端口识别
    DPI_IDENTIFY_CONTENT,           // 内容识别
    DPI_IDENTIFY_PORT_CONTENT,      // 端口识别，未知流量再进行内容识别
} dpi_identify_type_t;

struct check_proto_data{
    uint16_t proto;
    uint8_t  identify_type;         // 识别类型  端口、内容
    DPI_PROTOCOL_BITMASK excluded_protocol_bitmask;
    void (*identify_func)(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len);
    int  (*dissect_func)(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag);
    int  (*flow_timeout)(struct flow_info *flow);             //在 旧版TCP超时前
    void (*exit_func)(struct flow_info *flow, void *session); //在 旧版TCP超时后
};

//typedef int (*ext_proto_init)(char *fields_dir, char *filename);
typedef char **(*ext_proto_init)(int *len);
typedef int (*ext_proto_identify)( unsigned short port_src, unsigned short port_dst, const uint8_t *payload, uint32_t payload_Len);
typedef int (*ext_proto_dissector)( unsigned short port_src, unsigned short port_dst, const uint8_t *payload, uint32_t payload_Len, char *log_buff, int log_buff_len);

struct ext_proto_st{
    void                   *handle;
    ext_proto_init         proto_init;
    ext_proto_identify     proto_identify;
    ext_proto_dissector    proto_dissector;
    uint16_t               protocol_id;
    char                   ext_proto_name[COMMON_FILE_NAME];
};

typedef enum work_mode_flag_enum
{
    WMF_socket = 0,
    WMF_dpdk = 1,
    WMF_scan = 2,
    WMF_pprov,
    WMF_smart_nic,
} work_mode_flag_enum_t;

#define  PLUGIN_PROTO_START_ID    1000

extern struct guess_proto_data  tcp_port_proto[65536];
extern struct guess_proto_data  udp_port_proto[65536];
extern struct guess_proto_data sctp_port_proto[65536];

extern struct check_proto_data  tcp_detection_array[PROTOCOL_MAX];
extern struct check_proto_data  udp_detection_array[PROTOCOL_MAX];
extern struct check_proto_data sctp_detection_array[PROTOCOL_MAX];
extern struct check_conv_data  thread_conv_array[PROTOCOL_MAX];

int workflow_node_cmp(const void *a, const void *b);
int workflow_process_packet (struct work_process_data * workflow, uint64_t p_msec, const u_char *packet, uint32_t pkt_len);
int workflow_process_packet2 (struct work_process_data * workflow,u_int64_t p_usec, const u_char *raw_packet,
                              uint32_t raw_pkt_len, uint64_t timestamp, void* userdata);

void do_idle_flow_free(uint16_t thread_id, uint64_t now_time, int flag);

int skip_mpls(const char*p, uint32_t len, uint16_t *next_type, uint32_t *mpls_lablesl, uint8_t *mpls_cnt);
int dpi_packet_processing_ip_layer(struct work_process_data *workflow, const uint64_t time, const struct five_tuple *outer, struct pkt_info  *pkt, const void *iph4, uint16_t ipsize, uint16_t rawsize, SdtAclMatchedRuleInfo *aclHashHit);

uint16_t dpi_get_flow_timeout_index(uint8_t proto);
void do_all_flow_free(uint16_t thread_id);
void do_all_flow_timeout(struct work_process_data *process);
void port_add_proto_head(uint8_t tcp_or_udp, uint16_t port, uint16_t protocol);
void flow_bit_info(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len);
const char *filetype_flow(const char*p, int l);

int get_flow_total_num(void);

int dpi_dissect_arp(struct flow_info *flow, int direction, const uint8_t *payload, uint32_t payload_len, uint8_t flag);

int dpi_sdt_ip_match  (struct pkt_info *pkt);
int dpi_sdt_udp_match (struct pkt_info *pkt);
int dpi_sdt_tcp_match (struct pkt_info *pkt);
int dpi_sdt_sctp_match(struct pkt_info *pkt);

int sdt_network_process(struct work_process_data *workflow,
        struct flow_info *flow,
        const struct pkt_info  *pkt,
        uint16_t proto,
        uint8_t version,
        int dir,const uint8_t *payload,uint32_t payload_len);



int sdt_engine_match_rules(struct flow_info *flow,ProtoRecord * pRec);

int sdt_clean_flow_update_rule(void);

void sdt_enable_rule_protos_associated(int master_proto);

int sdt_set_rule_proto_switch(void);


/** 基于 checksum 的流完整性校验 **/
bool flowChecksum(const void *iph4, uint8_t iphver, const void *l4hdr, uint8_t l4proto, uint16_t l4len);

//将 src_workflow 内的数据链路层信息 拷贝入 dst_newflow
int data_link_layer_copy(struct flow_info *dst_newflow, struct work_process_data *src_workflow);

int mac_addr_parse(const char *str, uint8_t *mac);
void append_hardlink_proto_info(int type);


void sdt_atomic_lock(int *lock);
void sdt_atomic_unlock(int *lock);
void check_dpi_flow_func(struct flow_info *flow, const unsigned char *payload, const unsigned short paylod_len);
void append_proto_info(struct flow_info *flow);
void append_proto_info_no_real_protocol_id(struct flow_info *flow);
void dpi_flow_timeout(struct flow_info *flow);
void dpi_flow_timeout_free(struct flow_info *flow);


#ifdef __cplusplus
extern "C"
{
#endif

void init_ip_location_db(int db_type);
int read_manuf_data(void);
int write_ipff_log(struct flow_info *flow, struct pkt_info *pkt, SdtMatchResult *ruleAction);
int _sdt_stream_action_process(struct flow_info *flow,const struct pkt_info  *pkt, int direction);
#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
