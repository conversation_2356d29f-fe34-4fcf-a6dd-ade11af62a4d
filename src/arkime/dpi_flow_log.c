/****************************************************************************************
 * 文 件 名 : dpi_flow_log.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy          2018/12/26
编码: wangy            2018/12/26
修改: xuxn            2019/03/29
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/
#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include "dpi_arkime_pcap.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include <yaProtoRecord/precord_schema.h>
#include "dpi_pschema.h"
#include "yaFtypes/fvalue.h"
#include "dpi_gtp_control.h"
#include "cJSON.h"

extern struct global_config g_config;
extern struct rte_mempool  *tbl_log_mempool;
int                         write_flow_log(struct flow_info *flow);



// ""接口类型，十进制编码："},
struct int_to_string gtp_u_interface[] = {{1,"5G Uu"},
{2,"5G Xn"},
{3,"DC-NR X2"},
{4,"5G E1"},
{5,"5G F1"},
{6,"5G UE_MR"},
{7,"5G Cell_MR"},
{8,"N3"},
{9,"预留备用"},
{10,"预留备用"},
{11,"S1-U"},
{12-39,"预留备用"},
{39,"N1&N2"},
{40,"N4"},
{41,"N5"},
{42,"N7"},
{43,"N8"},
{44,"N10"},
{45,"N11"},
{46,"N12"},
{47,"N14"},
{48,"N15"},
{49,"N22"},
{50,"N26"},
{51,"N16"},
{52,"N24"},
{53,"N20"},
{54,"N21"},
{55,"N40"},
{56,"Nnrf"},
{57,"N9"},
{58-79,"预留备用"},
{80,"4G Uu"},
{81,"4G X2"},
{82,"4G UE_MR"},
{83,"4G Cell_MR"},
{84,"S1-MME"},
{85,"S6a"},
{86,"S11-C"},
{87,"S10"},
{88,"SGs"},
{89,"S5/S8"},
{90,"Gn-C"},
{91,"Gm信令面"},
{92,"Mw"},
{93,"Mg"},
{94,"Mi"},
{95,"Mj"},
{96,"ISC"},
{97,"Sv"},
{98,"Cx"},
{99,"Dx"},
{100,"Sh"},
{101,"Dh"},
{102,"Zh"},
{103,"Gx"},
{104,"Rx"},
{105,"I2(CSCF与eMSC之间的接口)"},
{106,"Nc(MGCF与关口局之间的接口)"},
{107,"ATCF-SCCAS"},
{108,"Ut "},
{109,"Sx"},
{110,"Gm接口VoLTE会话XDR（Gm Session XDR）"},
{111,"Gm接口VoLTE切片XDR（Gm Slice XDR）"},
{112,"S1-U接口VoLTE会话XDR（S1-U Session XDR）"},
{113,"S1-U接口VoLTE切片XDR（S1-U Slice XDR）"},
{114,"Mb接口VoLTE会话XDR（Mb Session XDR）"},
{115,"Mb接口VoLTE切片XDR（Mb Slice XDR）"},
{116,"S1-U特定源码XDR"},
{117,"S11-U"},
{118-129,"预留备用"},
{131,"5GM-01"},
{132,"5GM-02"},
{133,"5GM-03"},
{134,"5GM-04"},
{135,"5GM-05"},
{136,"DM-01"},
{137,"Ub"},
{138,"NNI-01"},
{139,"GC"},
{140,"CP-01"},
{141,"CP-02"},
{142,"Fli"},
{143,"CM-01"},
{144,"IW-01"},
{145,"IW-02"},
{146,"IW-03"},
{147,"E"},
{148,"ENUM/DNS"},
{149,"RF"},
{150,"IS-01"},
{151,"5G消息Zh"},
{152,"C"},
{153,"Zn"},
{154,"CB-01"},
{155,"CB-02"},
{156,"CB-03"},
{157,"CB-04"},
{158,"CB-05"},
{159,"CB-06"},
{160,"CB-07"},
{161,"CB-08"},
{162,"Ici"},
{163,"Izi"},
{164,"Ifi"},
{165-220,"预留备用"},
{221-254,"厂家/省份自定义"},
{255,"采用扩展Interface定义"}};

int get_tcpflag_str(char *__str, int len, uint16_t tcpflag)
{
    if(len < 10)
      return 0;
    memset(__str, 0, len);
    char flag[10]= "--------";
    if (tcpflag & 0x20)
      flag[2] = 'U';
    if (tcpflag & 0x10)
      flag[3] = 'A';
    if (tcpflag & 0x08)
      flag[4] = 'P';
    if (tcpflag & 0x04)
      flag[5] = 'R';
    if (tcpflag & 0x02)
      flag[6] = 'S';
    if (tcpflag & 0x01)
      flag[7] = 'F';
    strncpy(__str,(const char*)&flag,sizeof(flag));
    return 0;
}

int write_flow_m_gtp_c_log(struct flow_info *flow, int direction,  gtp_info_t *gtp_info) {
  if (g_config.flow_log_switch == 0)
    return 0;

  int             i, ret;
  int             idx = 0;
  struct tbl_log *log_ptr;
  char            __str[64] = {0};

  if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
    DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
    return PKT_OK;
  }
  init_log_ptr_data(log_ptr, flow, EM_TBL_LOG_ID_BY_DEFAULT);
  dpi_precord_new_record(log_ptr->record, NULL, NULL);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "FLOW_M");

  dpi_fvalue_new_string_put(log_ptr->record, YA_FT_STRING, "ip_base_proto", protocol_name_array[flow->real_protocol_id],
      strlen(protocol_name_array[flow->real_protocol_id]));
  dpi_fvalue_new_string_put(log_ptr->record, YA_FT_STRING, "mobile_imsi", (char *)gtp_info->imsi, strlen((char *)gtp_info->imsi));
  dpi_fvalue_new_string_put(log_ptr->record, YA_FT_STRING, "mobile_imei", (char *)gtp_info->imei, strlen((char *)gtp_info->imei));
  dpi_fvalue_new_string_put(
      log_ptr->record, YA_FT_STRING, "mobile_msisdn", (char *)gtp_info->msisdn, strlen((char *)gtp_info->msisdn));
  dpi_fvalue_new_string_put(log_ptr->record, YA_FT_STRING, "mobile_apn", (char *)gtp_info->apn, strlen((char *)gtp_info->apn));

  char ecgi[16] = {0};
  snprintf(ecgi, sizeof(ecgi), "%d", gtp_info->ecgi);
  dpi_fvalue_new_string_put(log_ptr->record, YA_FT_STRING, "mobile_ecgi", ecgi, strlen(ecgi));

  char tai[32] = {0};
  snprintf(ecgi, sizeof(ecgi), "%d-%d-%d", gtp_info->mcc, gtp_info->mnc, gtp_info->tac);
  dpi_fvalue_new_string_put(log_ptr->record, YA_FT_STRING, "mobile_tai", tai, strlen(tai));

  log_ptr->thread_id = flow->thread_id;
  log_ptr->log_type = TBL_LOG_FLOW;
  log_ptr->log_len = idx;
  log_ptr->content_len = 0;
  log_ptr->content_ptr = NULL;
  log_ptr->proto_id = PROTOCOL_FLOW;
  if (dpi_app_match_res_enqueue(log_ptr) != 1) {
    rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
  }

  return 0;
}

int write_flow_m_gtp_u_log(struct flow_info *flow) {
  if (g_config.flow_log_switch == 0)
    return 0;

  int             i, ret;
  int             idx = 0;
  struct tbl_log *log_ptr;
  char            __str[64] = {0};

  if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
    DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
    return PKT_OK;
  }
  init_log_ptr_data(log_ptr, flow, EM_TBL_LOG_ID_BY_DEFAULT);
  dpi_precord_new_record(log_ptr->record, NULL, NULL);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "FLOW_M");

// "mobile_interface"
// "mobile_srcip"    
// "mobile_srcport"  
// "mobile_destip"   
// "mobile_destport" 
// "mobile_teid"     


  log_ptr->thread_id = flow->thread_id;
  log_ptr->log_type = TBL_LOG_FLOW;
  log_ptr->log_len = idx;
  log_ptr->content_len = 0;
  log_ptr->content_ptr = NULL;
  log_ptr->proto_id = PROTOCOL_FLOW;
  if (dpi_app_match_res_enqueue(log_ptr) != 1) {
    rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
  }

  return 0;
}

int write_flow_i_log(struct flow_info *flow) {
  if (g_config.flow_log_switch == 0)
    return 0;

  int             i, ret;
  int             idx = 0;
  struct tbl_log *log_ptr;
  char            __str[64] = {0};

  if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
    DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
    return PKT_OK;
  }
  init_log_ptr_data(log_ptr, flow, EM_TBL_LOG_ID_BY_DEFAULT);
  dpi_precord_new_record(log_ptr->record, NULL, NULL);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "FLOW_I");

  precord_fvalue_put(log_ptr->record, "ip_begin_time", ya_fvalue_new_uinteger64(YA_FT_UINT64, flow->create_time >> 6));

  if (flow->tuple.inner.ip_version == 4) {
    precord_fvalue_put(log_ptr->record, "ip_source", ya_fvalue_new_ipv4(YA_FT_IPv4, *(uint32_t *)flow->tuple.inner.ip_src));
    precord_fvalue_put(log_ptr->record, "ip_destination", ya_fvalue_new_ipv4(YA_FT_IPv4, *(uint32_t *)flow->tuple.inner.ip_dst));

  } else {
    precord_fvalue_put(log_ptr->record, "ipv6_source", ya_fvalue_new_ipv6(YA_FT_IPv6, flow->tuple.inner.ip_dst));
    precord_fvalue_put(log_ptr->record, "ipv6_destination", ya_fvalue_new_ipv6(YA_FT_IPv6, flow->tuple.inner.ip_src));
  }
  precord_fvalue_put(log_ptr->record, "port_source"                   ,ya_fvalue_new_uinteger(YA_FT_UINT16, flow->port_src));
  precord_fvalue_put(log_ptr->record, "port_destination"              ,ya_fvalue_new_uinteger(YA_FT_UINT16, flow->port_dst));
  precord_fvalue_put(log_ptr->record, "ip_proto"                      ,ya_fvalue_new_uinteger(YA_FT_UINT16, flow->tuple.inner.proto));
  precord_fvalue_put(log_ptr->record, "ip_duration"                   ,ya_fvalue_new_uinteger(YA_FT_UINT32,flow->end_time - flow->create_time ));
  precord_fvalue_put(log_ptr->record, "ip_packets_source"             ,ya_fvalue_new_uinteger(YA_FT_UINT32,flow->dst2src_packets));
  precord_fvalue_put(log_ptr->record, "ip_bytes_source"               ,ya_fvalue_new_uinteger64(YA_FT_UINT64,flow->dst2src_bytes));

  precord_fvalue_put(log_ptr->record, "ip_packets_destination", ya_fvalue_new_uinteger(YA_FT_UINT32, flow->src2dst_packets));
  precord_fvalue_put(log_ptr->record, "ip_bytes_destination", ya_fvalue_new_uinteger64(YA_FT_UINT64, flow->src2dst_bytes));
  char trans_payload_hex_source[16] = {0};
  snprintf(trans_payload_hex_source, sizeof(trans_payload_hex_source), "%lx", flow->payloadsrchex);

  char trans_payload_hex_destination[16] = {0};
  snprintf(trans_payload_hex_destination, sizeof(trans_payload_hex_source), "%lx", flow->payloaddsthex);

  dpi_fvalue_new_string_put(
      log_ptr->record, YA_FT_STRING, "trans_payload_hex_destination", trans_payload_hex_source, strlen(trans_payload_hex_source));

  dpi_fvalue_new_string_put(log_ptr->record, YA_FT_STRING, "trans_payload_hex_source", trans_payload_hex_destination,
      strlen(trans_payload_hex_destination));

  if (flow->tcp_flag != 0) {
    char tcp_flag_str[10] = {0};
    get_tcpflag_str(tcp_flag_str, sizeof(tcp_flag_str), flow->tcp_flag);
    dpi_fvalue_new_string_put(log_ptr->record,YA_FT_STRING,"tcp_first_flag", tcp_flag_str,strlen(tcp_flag_str));
    }
    if(flow->real_protocol_id!= 0){
    dpi_fvalue_new_string_put(log_ptr->record, YA_FT_STRING, "ip_base_proto", protocol_name_array[flow->real_protocol_id],
        strlen(protocol_name_array[flow->real_protocol_id]));
    }

      // 添加packetPos和fileId数组（从record的文件信息链表中获取）
  cJSON *packet_pos = cJSON_CreateArray();
  cJSON *file_id    = cJSON_CreateArray();
  if (packet_pos && file_id) {
    // 获取record的文件位置数组
    int64_t *pos_array  = NULL;
    int      array_size = 0;

    // 使用函数获取文件位置数组
    if (arkime_record_get_file_pos_array_from_list(flow->arkime_file_pos_list, &pos_array, &array_size) == 0 && pos_array && array_size > 0) {
      // 添加所有文件位置信息到packetPos数组
      for (int i = 0; i < array_size; i++) {
        cJSON_AddItemToArray(packet_pos, cJSON_CreateNumber(pos_array[i]));
      }

      // 提取文件ID到fileId数组（负值表示文件ID）
      for (int i = 0; i < array_size; i++) {
        if (pos_array[i] < 0) {  // 负值表示文件ID
          cJSON_AddItemToArray(file_id, cJSON_CreateNumber(-pos_array[i]));
        }
      }

      free(pos_array);  // 释放分配的内存
    }
    // 如果没有文件信息，不添加默认值，保持数组为空
  }
  if (packet_pos) {
    char *pkt_pos = cJSON_PrintUnformatted(packet_pos);
    if (pkt_pos) {
      dpi_fvalue_new_string_put(log_ptr->record, YA_FT_STRING, "packetPos", pkt_pos, strlen(pkt_pos));
      free(pkt_pos);
    }
    free(packet_pos);
  }
  if (file_id) {
    char *file_id_str = cJSON_PrintUnformatted(file_id);
    if (file_id_str) {
      dpi_fvalue_new_string_put(log_ptr->record, YA_FT_STRING, "fileId", file_id_str, strlen(file_id_str));
    }
    free(file_id);
  }

  precord_fvalue_put(log_ptr->record, "map_stream_id"               ,ya_fvalue_new_uinteger64(YA_FT_UINT64,flow->map_stream_id));

  log_ptr->thread_id = flow->thread_id;
  log_ptr->log_type = TBL_LOG_FLOW;
  log_ptr->log_len = idx;
  log_ptr->content_len = 0;
  log_ptr->content_ptr = NULL;
  log_ptr->proto_id = PROTOCOL_FLOW;
  if (dpi_app_match_res_enqueue(log_ptr) != 1) {
    rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
  }

  return 0;
}

void proto_register_flow_i(void) {
  const char *proto_name = "FLOW_I";
  pschema_t  *schema = dpi_pschema_get_proto(proto_name);

  pschema_register_field(schema, "ip_source"                       , YA_FT_IPv4, "desc");
  pschema_register_field(schema, "ipv6_source"                     , YA_FT_IPv6, "desc");
  pschema_register_field(schema, "ip_destination"                  , YA_FT_IPv4, "desc");
  pschema_register_field(schema, "ipv6_destination"                , YA_FT_IPv6, "desc");
  pschema_register_field(schema, "port_source"                     , YA_FT_UINT16, "desc");
  pschema_register_field(schema, "port_destination"                , YA_FT_UINT16, "desc");
  pschema_register_field(schema, "ip_proto"                        , YA_FT_UINT8, "desc");
  pschema_register_field(schema, "ip_begin_time"                   , YA_FT_ABSOLUTE_TIME, "desc");
  pschema_register_field(schema, "ip_duration"                     , YA_FT_UINT32, "desc");
  pschema_register_field(schema, "ip_packets_source"               , YA_FT_UINT32, "desc");
  pschema_register_field(schema, "ip_packets_destination"          , YA_FT_UINT32, "desc");
  pschema_register_field(schema, "ip_bytes_source"                 , YA_FT_UINT64, "desc");
  pschema_register_field(schema, "ip_bytes_destination"            , YA_FT_UINT64, "desc");
  pschema_register_field(schema, "trans_payload_hex_source"        , YA_FT_STRING, "desc");
  pschema_register_field(schema, "trans_payload_hex_destination"   , YA_FT_STRING, "desc");
  pschema_register_field(schema, "tcp_first_flag"                  , YA_FT_STRING, "desc");
  pschema_register_field(schema, "ip_base_proto"                   , YA_FT_STRING, "desc");
  pschema_register_field(schema, "packetPos"                   , YA_FT_STRING, "desc");
  pschema_register_field(schema, "fileId"                   , YA_FT_STRING, "desc");
  pschema_register_field(schema, "map_stream_id"                   , YA_FT_STRING, "desc");
}

void proto_register_flow_m_common(pschema_t  *schema ){
  //gtp-c
  pschema_register_field(schema, "mobile_imsi"       , YA_FT_STRING, "desc");
  pschema_register_field(schema, "mobile_imei"       , YA_FT_STRING, "desc");
  pschema_register_field(schema, "mobile_msisdn"     , YA_FT_STRING, "desc");
  //gtp-u
  pschema_register_field(schema, "mobile_interface"  , YA_FT_STRING, "desc");
  pschema_register_field(schema, "mobile_srcip"      , YA_FT_IPv4, "desc");
  pschema_register_field(schema, "mobile_srcport"    , YA_FT_UINT16, "desc");
  pschema_register_field(schema, "mobile_destip"     , YA_FT_IPv4, "desc");
  pschema_register_field(schema, "mobile_destport"   , YA_FT_UINT16, "desc");
}


void proto_register_flow_m_user_layer(pschema_t  *schema ){
  //gtp-c
  pschema_register_field(schema, "mobile_apn"        , YA_FT_STRING, "desc");
  pschema_register_field(schema, "mobile_ecgi"       , YA_FT_STRING, "desc");
  pschema_register_field(schema, "mobile_tai"        , YA_FT_STRING, "desc");
  //gtp-u
  pschema_register_field(schema, "mobile_teid"       , YA_FT_UINT32, "desc");
}

void proto_register_flow_m(void) {
  const char *proto_name = "FLOW_M";
  pschema_t  *schema = dpi_pschema_get_proto(proto_name);
  proto_register_flow_m_common(schema);
  proto_register_flow_m_user_layer(schema);
}

static void init_flow_log(void) {
  dpi_register_proto("FLOW_M", "FLOW_M", NULL);
  proto_register_flow_m();
  dpi_register_proto("FLOW_I", "FLOW_I", NULL);
  proto_register_flow_i();
  return;
}

static __attribute((constructor)) void before_init_flow(void) { register_tbl_array(TBL_LOG_FLOW, 0, "flow", init_flow_log); }
