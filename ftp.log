hugepages is 4, set low performance.
fs.file-max = 150000
CMD_LINE is ./yaDpiSdt -l 0-6 -- -r 1 -p 2 -a 3 -l 3 -m 4 -o 5 -c 6 
  ERROR: <config::SDX_IP_INTERFACE> ć˛ĄćäťçŤŻĺŁ[ens192]čˇĺĺ°ććIP
TBL čžĺşčˇŻĺž ďź/tmp/tbls/sdt
RUNNING MODE DPDK
No Ethernet ports - bye
ććçŤŻĺŁä¸Şć° :0
avail nports number:0, lcores = 7, nb_rxq = 1
NUMA node: 0
Configured CPUs: 128
NUMA Node 0 Cpus: 0 1 2 3 4 5 6 7 
end
device name is lo
device name is ens33
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_main.c:init_packet_cache_mem:2162[0m Allocated mbuf pool on socket 0
flow again core id 3
[2025-08-22 17:20:18] [32mINFO [0m App fields sdt match thread on core 4
[1;36m2025-08-22 17:20:18[0m[1;32m[39083][0m[1;30m[39102][0m[1;34m[(null)][0m[1;32m[INFO]SDTEngine ĺĺ§ĺ threadID=1/1[0m
[2025-08-22 17:20:18] [32mINFO [0m Flow aging running thread 39103, thread_id 139814553060352, ring_id 0 . on core 3
éĺ¤ćł¨ĺ proto schema: <dbbasic>
adapt init lua = ./adapt_lua/init.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_dhcp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_ntlm.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_modbus.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_vnc.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_tcp.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_pap.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_ipip.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_xwin.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_wins.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_udp.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_socks.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_radius.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_igmp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_cdp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_vrrp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_net_login.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_isis.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_smb_netlogon.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_lacp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_nfs.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_tds.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_rsvp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_sunflower.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_pim.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_teredo.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_dns.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_telnet.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_slink.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_eigrp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_arp.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_dnp3.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_kerberos.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_ip.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_bgp.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_gre.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_teamviewer.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_rip.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_smb.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_stp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_mysql.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_s7comm.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_dtls.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_ah.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_ldp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_ocsp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_icmp.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_dcerpc.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_sctp.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_ldap.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_l2tp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_email.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_syslog.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_lcp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_rdp.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_sip.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_netbios.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_tll.lua failed, error: tll ĺčŽŽĺ¨ĺé¨ schemaDB ä¸­ĺšść˛Ąććł¨ĺčżďźčŻˇçĄŽčŽ¤ćŻĺŚĺ­ĺ¨čŻĽĺčŽŽ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_ntp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_gtp_u.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_ftp.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_http.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_esp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_chap.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_snmp.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_tftp.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_diameter.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_ssh.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_cflow.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_pptp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_dbbasic.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_stun.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_X509Cer.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_ssl_tls.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_megacomet.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_isakmp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_link.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_mgcp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_lldp.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_common.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:80[0m adapt proto file = ./adapt_lua/adapt_cwmp.lua
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_ospf.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_spnego.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_smb2.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_lua_adapt.c:_padapt_load_script:78[0m load lua adapt file ./adapt_lua/adapt_cldap.lua failed, error: yalua_get_last_reg_proto get protoFrom and protoTo error, čŻˇćŁćĽčćŹćŻĺŚć­ŁçĄŽä˝żç¨ yalua_register čżčĄäşééĺ¨ćł¨ĺ.
tbl log core id = 3
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_tbl_log.c:init_tbl_log_mempool:2689[0m Allocated tbl_log_mempool
[2025-08-22 17:20:18] [32mINFO [0m Logging running thread 39104, thread_id 139814204601344. on core 3
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_out_init:480[0m Allocated packet out pool on socket 0
app_version:*******
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_out_thfunc:1832[0m Running log thread on core 5
[2025-08-22 17:20:18] [31mERROR[0m SDX_TX_PORT_LIST: ćŞćžĺ° pci ĺ°ĺ [0000:0b:00.0]
WARN: PCI ĺ°ĺć ć. ĺ°ć ćłć§čĄč˝ŹĺćĽć
2025-08-22 09:20:19  I mongoose.c:1254:mg_log_se Setting log level to 3
2025-08-22 09:20:19  I sdt_HttpServer.cpp:376:Ru ĺźĺ§çćľč§ĺäťŁçćäťś: SDT_Rule_Update.txt
2025-08-22 09:20:19  I mongoose.c:2900:mg_listen 1 accepting on http://0.0.0.0:8888
2025-08-22 09:20:19  I sdt_HttpServer.cpp:199:re ćŞćł¨ĺ, webćĺĄĺ°ĺä¸şçŠş
[yaWatch]: version 1.3.0
[2025-08-22 17:20:18] [32mINFO [0m process func locore_id = 1, thread_id = 0
offline pcap running thread 39117, thread_id 139813025997824. on core 7
conv func locore_id = 2, thread_id = 0
[2025-08-22 17:20:18] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_conversation.c:conversation_thread_func:365[0m dequeue ring id 0

2025-08-22 09:20:20  I sdt_HttpServer.cpp:417:ch çćľćäťśĺçĺĺ[SDT_Rule_Update.txt]
2025-08-22 09:20:20  I sdt_HttpServer.cpp:435:ch ć´ć°č§ĺćäťś ./test.xml
1.[CN20#20###YVIEW###1001#TECH], rule count: 6
transform success
[SdtAppRuleHash_ruleTransactStart] /home/<USER>/SDX/sdx_dpi_sdt/src/sdtapp_interface.c:543
TransactStart ĺć­˘ćśĺ...
wait g_sdt_hash_db_clear ...
[2025-08-22 17:20:20] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_rule_hash_db_clean:193[0m ç­ĺž sdt_match çşżç¨çäżĄĺˇ...
[2025-08-22 17:20:20] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_rule_hash_db_clean:198[0m sdt_match çşżç¨ĺˇ˛ĺ¨é¨ćĺ!
[2025-08-22 17:20:20] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_rule_hash_db_clean:200[0m ç­ĺž tbl_out çşżç¨çäżĄĺˇ...
[2025-08-22 17:20:20] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_rule_hash_db_clean:205[0m tbl_out çşżç¨ĺˇ˛ĺ¨é¨ćĺ!
[2025-08-22 17:20:20] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_rule_hash_db_clean:207[0m ç­ĺž sdt_out çşżç¨çäżĄĺˇ...
[2025-08-22 17:20:20] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_rule_hash_db_clean:212[0m sdt_out çşżç¨ĺˇ˛ĺ¨é¨ćĺ!
wait g_sdt_hash_db_clear OK
çźčŻćĺč§ĺć°:6(IR ĺ˝˘ĺźč§ĺ)
ĺźĺ§ä¸ĺACL ćťčŽĄ:4
sdt_acl_info.log č°čŻćäťśä¸ĺ­ĺ¨
çťčŽĄçťćACL ćťčŽĄ:4, čćś 0ç§
[SdtAppRuleHash_RuleInitClear] /home/<USER>/SDX/sdx_dpi_sdt/src/sdtapp_interface.c:591
wait g_sdt_hash_db_clear ...
[2025-08-22 17:20:20] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_rule_hash_db_clean:193[0m ç­ĺž sdt_match çşżç¨çäżĄĺˇ...
[2025-08-22 17:20:20] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_rule_hash_db_clean:198[0m sdt_match çşżç¨ĺˇ˛ĺ¨é¨ćĺ!
[2025-08-22 17:20:20] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_rule_hash_db_clean:200[0m ç­ĺž tbl_out çşżç¨çäżĄĺˇ...
[2025-08-22 17:20:20] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_rule_hash_db_clean:205[0m tbl_out çşżç¨ĺˇ˛ĺ¨é¨ćĺ!
[2025-08-22 17:20:20] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_rule_hash_db_clean:207[0m ç­ĺž sdt_out çşżç¨çäżĄĺˇ...
[2025-08-22 17:20:20] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/sdt_action_out.c:sdt_rule_hash_db_clean:212[0m sdt_out çşżç¨ĺˇ˛ĺ¨é¨ćĺ!
wait g_sdt_hash_db_clear OK
[SdtAppRuleHash_RuleInitStart] /home/<USER>/SDX/sdx_dpi_sdt/src/sdtapp_interface.c:598
[SdtAppRuleHash_ruleInitEnd] /home/<USER>/SDX/sdx_dpi_sdt/src/sdtapp_interface.c:604
[SdtAppRuleHash_ruleTransactFinish] /home/<USER>/SDX/sdx_dpi_sdt/src/sdtapp_interface.c:566
TransactFinish ć˘ĺ¤ćśĺ.
äşĺĄć§čĄčćś 0ç§
Processing pcap file /home/<USER>/pcap/ftp/ftp_38.pcap
FTP: dissect_ftp_control 220 (vsFTPd 3.0.2)
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 220 (vsFTPd 3.0.2)
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 331 Please specify the password.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 230 Login successful.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Here comes the directory listing.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 226 Directory send OK.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 250 Directory successfully changed.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Here comes the directory listing.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 226 Directory send OK.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 Switching to ASCII mode.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Here comes the directory listing.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 226 Directory send OK.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 Switching to ASCII mode.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for 100-gdb-tips.pdf (922566 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: '100-gdb-tips.pdf'
FTP: Searching for empty storpath to update with '100-gdb-tips.pdf', list count=0FTP: No empty storpath entry found for update with '100-gdb-tips.pdf'
[2025-08-22 17:20:21] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_main.c:exit_signal_handle:1319[0m ćśĺ°äżĄĺˇ: 2
FTP: Data flow processing, looking for filename with port 4170
FTP: No filename found for port 4170 in any session
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for 111.zip (8173 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: '111.zip'
FTP: Searching for empty storpath to update with '111.zip', list count=0FTP: No empty storpath entry found for update with '111.zip'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for 14.swf (4696533 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: '14.swf'
FTP: Searching for empty storpath to update with '14.swf', list count=0FTP: No empty storpath entry found for update with '14.swf'
FTP: Data flow processing, looking for filename with port 4172
FTP: No filename found for port 4172 in any session
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for 20250819_145640.iso (7405568 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: '20250819_145640.iso'
FTP: Searching for empty storpath to update with '20250819_145640.iso', list count=0FTP: No empty storpath entry found for update with '20250819_145640.iso'
FTP: Data flow processing, looking for filename with port 4173
FTP: No filename found for port 4173 in any session
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for 9b28dca05d2ee03be311806d0f7b8e99.jpg (11716 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: '9b28dca05d2ee03be311806d0f7b8e99.jpg'
FTP: Searching for empty storpath to update with '9b28dca05d2ee03be311806d0f7b8e99.jpg', list count=0FTP: No empty storpath entry found for update with '9b28dca05d2ee03be311806d0f7b8e99.jpg'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for Archive.ace (577 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'Archive.ace'
FTP: Searching for empty storpath to update with 'Archive.ace', list count=0FTP: No empty storpath entry found for update with 'Archive.ace'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for SkinPlusPlusDLL.dll (294912 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'SkinPlusPlusDLL.dll'
FTP: Searching for empty storpath to update with 'SkinPlusPlusDLL.dll', list count=0FTP: No empty storpath entry found for update with 'SkinPlusPlusDLL.dll'
FTP: Data flow processing, looking for filename with port 4176
FTP: No filename found for port 4176 in any session
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for Xftp.chm (155122 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'Xftp.chm'
FTP: Searching for empty storpath to update with 'Xftp.chm', list count=0FTP: No empty storpath entry found for update with 'Xftp.chm'
FTP: Data flow processing, looking for filename with port 4177
FTP: No filename found for port 4177 in any session
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for YV-S2000-192.168.108.23-13.xls (21504 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'YV-S2000-192.168.108.23-13.xls'
FTP: Searching for empty storpath to update with 'YV-S2000-192.168.108.23-13.xls', list count=0FTP: No empty storpath entry found for update with 'YV-S2000-192.168.108.23-13.xls'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for YV-S2000_Certificate_20240115165111_10.5.61.119_5559_135.cer (948 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'YV-S2000_Certificate_20240115165111_10.5.61.119_5559_135.cer'
FTP: Searching for empty storpath to update with 'YV-S2000_Certificate_20240115165111_10.5.61.119_5559_135.cer', list count=0FTP: No empty storpath entry found for update with 'YV-S2000_Certificate_20240115165111_10.5.61.119_5559_135.cer'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for YV-S2000_http_20240226105307_10.5.61.124_40959_136.cab (10683 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'YV-S2000_http_20240226105307_10.5.61.124_40959_136.cab'
FTP: Searching for empty storpath to update with 'YV-S2000_http_20240226105307_10.5.61.124_40959_136.cab', list count=0FTP: No empty storpath entry found for update with 'YV-S2000_http_20240226105307_10.5.61.124_40959_136.cab'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for demo.xlsx (8963 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'demo.xlsx'
FTP: Searching for empty storpath to update with 'demo.xlsx', list count=0FTP: No empty storpath entry found for update with 'demo.xlsx'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for deyu.pptx (49092 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'deyu.pptx'
FTP: Searching for empty storpath to update with 'deyu.pptx', list count=0FTP: No empty storpath entry found for update with 'deyu.pptx'
FTP: dissect_ftp_control_rsm 220 (vsFTPd 3.0.2)
FTP: dissect_ftp_control_rsm 331 Please specify the password.
FTP: dissect_ftp_control_rsm 230 Login successful.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Here comes the directory listing.
FTP: dissect_ftp_control_rsm 226 Directory send OK.
FTP: dissect_ftp_control_rsm 250 Directory successfully changed.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Here comes the directory listing.
FTP: dissect_ftp_control_rsm 226 Directory send OK.
FTP: dissect_ftp_control_rsm 200 Switching to ASCII mode.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Here comes the directory listing.
FTP: dissect_ftp_control_rsm 226 Directory send OK.
FTP: dissect_ftp_control_rsm 200 Switching to ASCII mode.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for 100-gdb-tips.pdf (922566 bytes).
FTP: Reassemble - extracted filename from 150 response: '100-gdb-tips.pdf'
FTP: Force updating latest storpath with '100-gdb-tips.pdf', list count=0
FTP: No port entry found for force update with '100-gdb-tips.pdf'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for 111.zip (8173 bytes).
FTP: Reassemble - extracted filename from 150 response: '111.zip'
FTP: Force updating latest storpath with '111.zip', list count=0
FTP: No port entry found for force update with '111.zip'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for 14.swf (4696533 bytes).
FTP: Reassemble - extracted filename from 150 response: '14.swf'
FTP: Force updating latest storpath with '14.swf', list count=0
FTP: No port entry found for force update with '14.swf'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for 20250819_145640.iso (7405568 bytes).
FTP: Reassemble - extracted filename from 150 response: '20250819_145640.iso'
FTP: Force updating latest storpath with '20250819_145640.iso', list count=0
FTP: No port entry found for force update with '20250819_145640.iso'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for editcap.sh.bz (476 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'editcap.sh.bz'
FTP: Searching for empty storpath to update with 'editcap.sh.bz', list count=0FTP: No empty storpath entry found for update with 'editcap.sh.bz'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for fz_0226.7z (60257 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'fz_0226.7z'
FTP: Searching for empty storpath to update with 'fz_0226.7z', list count=0FTP: No empty storpath entry found for update with 'fz_0226.7z'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for http_01.doc (12288 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'http_01.doc'
FTP: Searching for empty storpath to update with 'http_01.doc', list count=0FTP: No empty storpath entry found for update with 'http_01.doc'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for mdjĺçş§.txt.Z (489 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'mdjĺçş§.txt.Z'
FTP: Searching for empty storpath to update with 'mdjĺçş§.txt.Z', list count=0FTP: No empty storpath entry found for update with 'mdjĺçş§.txt.Z'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for test-1time-20packets.pcap.gz (6105 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'test-1time-20packets.pcap.gz'
FTP: Searching for empty storpath to update with 'test-1time-20packets.pcap.gz', list count=0FTP: No empty storpath entry found for update with 'test-1time-20packets.pcap.gz'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for test.tgz (6239 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'test.tgz'
FTP: Searching for empty storpath to update with 'test.tgz', list count=0FTP: No empty storpath entry found for update with 'test.tgz'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for trailer_mac.pcap.arj (6067 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'trailer_mac.pcap.arj'
FTP: Searching for empty storpath to update with 'trailer_mac.pcap.arj', list count=0FTP: No empty storpath entry found for update with 'trailer_mac.pcap.arj'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for webmail_163_send.rar (5182 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'webmail_163_send.rar'
FTP: Searching for empty storpath to update with 'webmail_163_send.rar', list count=0FTP: No empty storpath entry found for update with 'webmail_163_send.rar'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for 9b28dca05d2ee03be311806d0f7b8e99.jpg (11716 bytes).
FTP: Reassemble - extracted filename from 150 response: '9b28dca05d2ee03be311806d0f7b8e99.jpg'
FTP: Force updating latest storpath with '9b28dca05d2ee03be311806d0f7b8e99.jpg', list count=0
FTP: No port entry found for force update with '9b28dca05d2ee03be311806d0f7b8e99.jpg'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for Archive.ace (577 bytes).
FTP: Reassemble - extracted filename from 150 response: 'Archive.ace'
FTP: Force updating latest storpath with 'Archive.ace', list count=0
FTP: No port entry found for force update with 'Archive.ace'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for SkinPlusPlusDLL.dll (294912 bytes).
FTP: Reassemble - extracted filename from 150 response: 'SkinPlusPlusDLL.dll'
FTP: Force updating latest storpath with 'SkinPlusPlusDLL.dll', list count=0
FTP: No port entry found for force update with 'SkinPlusPlusDLL.dll'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for Xftp.chm (155122 bytes).
FTP: Reassemble - extracted filename from 150 response: 'Xftp.chm'
FTP: Force updating latest storpath with 'Xftp.chm', list count=0
FTP: No port entry found for force update with 'Xftp.chm'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for YV-S2000-192.168.108.23-13.xls (21504 bytes).
FTP: Reassemble - extracted filename from 150 response: 'YV-S2000-192.168.108.23-13.xls'
FTP: Force updating latest storpath with 'YV-S2000-192.168.108.23-13.xls', list count=0
FTP: No port entry found for force update with 'YV-S2000-192.168.108.23-13.xls'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for YV-S2000_Certificate_20240115165111_10.5.61.119_5559_135.cer (948 bytes).
FTP: Reassemble - extracted filename from 150 response: 'YV-S2000_Certificate_20240115165111_10.5.61.119_5559_135.cer'
FTP: Force updating latest storpath with 'YV-S2000_Certificate_20240115165111_10.5.61.119_5559_135.cer', list count=0
FTP: No port entry found for force update with 'YV-S2000_Certificate_20240115165111_10.5.61.119_5559_135.cer'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for YV-S2000_http_20240226105307_10.5.61.124_40959_136.cab (10683 bytes).
FTP: Reassemble - extracted filename from 150 response: 'YV-S2000_http_20240226105307_10.5.61.124_40959_136.cab'
FTP: Force updating latest storpath with 'YV-S2000_http_20240226105307_10.5.61.124_40959_136.cab', list count=0
FTP: No port entry found for force update with 'YV-S2000_http_20240226105307_10.5.61.124_40959_136.cab'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for demo.xlsx (8963 bytes).
FTP: Reassemble - extracted filename from 150 response: 'demo.xlsx'
FTP: Force updating latest storpath with 'demo.xlsx', list count=0
FTP: No port entry found for force update with 'demo.xlsx'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for deyu.pptx (49092 bytes).
FTP: Reassemble - extracted filename from 150 response: 'deyu.pptx'
FTP: Force updating latest storpath with 'deyu.pptx', list count=0
FTP: No port entry found for force update with 'deyu.pptx'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for editcap.sh.bz (476 bytes).
FTP: Reassemble - extracted filename from 150 response: 'editcap.sh.bz'
FTP: Force updating latest storpath with 'editcap.sh.bz', list count=0
FTP: No port entry found for force update with 'editcap.sh.bz'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for fz_0226.7z (60257 bytes).
FTP: Reassemble - extracted filename from 150 response: 'fz_0226.7z'
FTP: Force updating latest storpath with 'fz_0226.7z', list count=0
FTP: No port entry found for force update with 'fz_0226.7z'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for http_01.doc (12288 bytes).
FTP: Reassemble - extracted filename from 150 response: 'http_01.doc'
FTP: Force updating latest storpath with 'http_01.doc', list count=0
FTP: No port entry found for force update with 'http_01.doc'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for mdjĺçş§.txt.Z (489 bytes).
FTP: Reassemble - extracted filename from 150 response: 'mdjĺçş§.txt.Z'
FTP: Force updating latest storpath with 'mdjĺçş§.txt.Z', list count=0
FTP: No port entry found for force update with 'mdjĺçş§.txt.Z'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for test-1time-20packets.pcap.gz (6105 bytes).
FTP: Reassemble - extracted filename from 150 response: 'test-1time-20packets.pcap.gz'
FTP: Force updating latest storpath with 'test-1time-20packets.pcap.gz', list count=0
FTP: No port entry found for force update with 'test-1time-20packets.pcap.gz'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for test.tgz (6239 bytes).
FTP: Reassemble - extracted filename from 150 response: 'test.tgz'
FTP: Force updating latest storpath with 'test.tgz', list count=0
FTP: No port entry found for force update with 'test.tgz'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for trailer_mac.pcap.arj (6067 bytes).
FTP: Reassemble - extracted filename from 150 response: 'trailer_mac.pcap.arj'
FTP: Force updating latest storpath with 'trailer_mac.pcap.arj', list count=0
FTP: No port entry found for force update with 'trailer_mac.pcap.arj'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for webmail_163_send.rar (5182 bytes).
FTP: Reassemble - extracted filename from 150 response: 'webmail_163_send.rar'
FTP: Force updating latest storpath with 'webmail_163_send.rar', list count=0
FTP: No port entry found for force update with 'webmail_163_send.rar'
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_00.docx (11670 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_00.docx'
FTP: Searching for empty storpath to update with 'wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_00.docx', list count=0FTP: No empty storpath entry found for update with 'wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_00.docx'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_00.docx (11670 bytes).
FTP: Reassemble - extracted filename from 150 response: 'wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_00.docx'
FTP: Force updating latest storpath with 'wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_00.docx', list count=0
FTP: No port entry found for force update with 'wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_00.docx'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_01.doc (14336 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_01.doc'
FTP: Searching for empty storpath to update with 'wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_01.doc', list count=0FTP: No empty storpath entry found for update with 'wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_01.doc'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_01.doc (14336 bytes).
FTP: Reassemble - extracted filename from 150 response: 'wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_01.doc'
FTP: Force updating latest storpath with 'wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_01.doc', list count=0
FTP: No port entry found for force update with 'wps_äťĺ¤ŠćŻ2025ĺš´8ć19ćĽććäş_ĺ¤Šć°çç­_01.doc'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_00.pptx (58743 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_00.pptx'
FTP: Searching for empty storpath to update with 'wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_00.pptx', list count=0FTP: No empty storpath entry found for update with 'wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_00.pptx'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_00.pptx (58743 bytes).
FTP: Reassemble - extracted filename from 150 response: 'wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_00.pptx'
FTP: Force updating latest storpath with 'wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_00.pptx', list count=0
FTP: No port entry found for force update with 'wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_00.pptx'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_01.ppt (60416 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_01.ppt'
FTP: Searching for empty storpath to update with 'wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_01.ppt', list count=0FTP: No empty storpath entry found for update with 'wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_01.ppt'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_01.ppt (60416 bytes).
FTP: Reassemble - extracted filename from 150 response: 'wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_01.ppt'
FTP: Force updating latest storpath with 'wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_01.ppt', list count=0
FTP: No port entry found for force update with 'wps_ĺç§ćäťśçąťĺĺćäťśĺ¤´ć čŻĺ¤§ĺ¨_01.ppt'
[yaWatch]: ćśćŻć¨éĺ¤ąč´Ľ - {
	"device_infos" : 
	{
		"case_name" : "yaDpiSdt_01",
		"ip_address" : "",
		"node_type" : 2
	},
	"hardware_used" : 
	{
		"cpu" : 43.761755485893417,
		"disk" : 48.595385108514307,
		"memory" : 38.614999687827265,
		"nic" : ""
	},
	"status_infos" : 
	{
		"conn_duplex" : 50.0,
		"conn_full" : 50.0,
		"disk_save_bps" : "",
		"kakfa_conn_status" : "Down",
		"nic_conn_status" : "{\"ens33\":\"UP\",\"docker0\":\"DOWN\"}",
		"ntfs_conn_status" : "DOWN",
		"rx_bps" : "",
		"rx_datace" : 0,
		"rx_port_status" : "\u7aef\u53e3\u5168\u90e8\u6b63\u5e38",
		"rx_total" : "",
		"signal_bps" : "10Gbps",
		"tx_bps" : "",
		"tx_port_status" : "\u7aef\u53e3\u5168\u90e8\u6b63\u5e38",
		"tx_total" : ""
	}
}
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for wps_ĺˇĽä˝ĺŽćčŽĄĺ_00.xlsx (9741 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'wps_ĺˇĽä˝ĺŽćčŽĄĺ_00.xlsx'
FTP: Searching for empty storpath to update with 'wps_ĺˇĽä˝ĺŽćčŽĄĺ_00.xlsx', list count=0FTP: No empty storpath entry found for update with 'wps_ĺˇĽä˝ĺŽćčŽĄĺ_00.xlsx'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for wps_ĺˇĽä˝ĺŽćčŽĄĺ_00.xlsx (9741 bytes).
FTP: Reassemble - extracted filename from 150 response: 'wps_ĺˇĽä˝ĺŽćčŽĄĺ_00.xlsx'
FTP: Force updating latest storpath with 'wps_ĺˇĽä˝ĺŽćčŽĄĺ_00.xlsx', list count=0
FTP: No port entry found for force update with 'wps_ĺˇĽä˝ĺŽćčŽĄĺ_00.xlsx'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for wps_ĺˇĽä˝ĺŽćčŽĄĺ_01.xls (20480 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'wps_ĺˇĽä˝ĺŽćčŽĄĺ_01.xls'
FTP: Searching for empty storpath to update with 'wps_ĺˇĽä˝ĺŽćčŽĄĺ_01.xls', list count=0FTP: No empty storpath entry found for update with 'wps_ĺˇĽä˝ĺŽćčŽĄĺ_01.xls'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for wps_ĺˇĽä˝ĺŽćčŽĄĺ_01.xls (20480 bytes).
FTP: Reassemble - extracted filename from 150 response: 'wps_ĺˇĽä˝ĺŽćčŽĄĺ_01.xls'
FTP: Force updating latest storpath with 'wps_ĺˇĽä˝ĺŽćčŽĄĺ_01.xls', list count=0
FTP: No port entry found for force update with 'wps_ĺˇĽä˝ĺŽćčŽĄĺ_01.xls'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for xfrmx64.sys (96664 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'xfrmx64.sys'
FTP: Searching for empty storpath to update with 'xfrmx64.sys', list count=0FTP: No empty storpath entry found for update with 'xfrmx64.sys'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for xfrmx64.sys (96664 bytes).
FTP: Reassemble - extracted filename from 150 response: 'xfrmx64.sys'
FTP: Force updating latest storpath with 'xfrmx64.sys', list count=0
FTP: No port entry found for force update with 'xfrmx64.sys'
FTP: Data flow processing, looking for filename with port 4198
FTP: No filename found for port 4198 in any session
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for yaSdt_test.tar (10280960 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'yaSdt_test.tar'
FTP: Searching for empty storpath to update with 'yaSdt_test.tar', list count=0FTP: No empty storpath entry found for update with 'yaSdt_test.tar'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for yaSdt_test.tar (10280960 bytes).
FTP: Reassemble - extracted filename from 150 response: 'yaSdt_test.tar'
FTP: Force updating latest storpath with 'yaSdt_test.tar', list count=0
FTP: No port entry found for force update with 'yaSdt_test.tar'
FTP: Data flow processing, looking for filename with port 4199
FTP: No filename found for port 4199 in any session
[yaWatch]: ćśćŻć¨éĺ¤ąč´Ľ - {
	"device_infos" : 
	{
		"case_name" : "yaDpiSdt_01",
		"ip_address" : "",
		"node_type" : 2
	},
	"proto_statis" : null,
	"status_infos" : 
	{
		"aclmask_rule_num" : 6,
		"bytes_inc" : 0,
		"class_b_ip_num" : 0,
		"class_c_ip_num" : 0,
		"combine_rule_num" : 24,
		"effect_rule_num" : 6,
		"ether_name_num" : 0,
		"exact_ip_num" : 0,
		"exact_keyword_num" : 0,
		"filter_pkt_num" : 0,
		"float_keyword_num" : 0,
		"line_name_num" : 0,
		"metdata_name_num" : 6,
		"output_bps" : 0,
		"packet_inc" : 0,
		"pkt_len_num" : 0,
		"protocol_feature_num" : 6,
		"regex_num" : 6,
		"rule_last_updatetime" : 1755854420,
		"tcpflag_rule_num" : 0,
		"ttl_name_num" : 0
	}
}
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for zip2excel.bat (88 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'zip2excel.bat'
FTP: Searching for empty storpath to update with 'zip2excel.bat', list count=0FTP: No empty storpath entry found for update with 'zip2excel.bat'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for zip2excel.bat (88 bytes).
FTP: Reassemble - extracted filename from 150 response: 'zip2excel.bat'
FTP: Force updating latest storpath with 'zip2excel.bat', list count=0
FTP: No port entry found for force update with 'zip2excel.bat'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for zkclient-0.7.jar (73756 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'zkclient-0.7.jar'
FTP: Searching for empty storpath to update with 'zkclient-0.7.jar', list count=0FTP: No empty storpath entry found for update with 'zkclient-0.7.jar'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for zkclient-0.7.jar (73756 bytes).
FTP: Reassemble - extracted filename from 150 response: 'zkclient-0.7.jar'
FTP: Force updating latest storpath with 'zkclient-0.7.jar', list count=0
FTP: No port entry found for force update with 'zkclient-0.7.jar'
FTP: Data flow processing, looking for filename with port 4201
FTP: No filename found for port 4201 in any session
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for äź čžćäťśćľčŻ1-éżćäźŻčŻ­pptx.eml (82796 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'äź čžćäťśćľčŻ1-éżćäźŻčŻ­pptx.eml'
FTP: Searching for empty storpath to update with 'äź čžćäťśćľčŻ1-éżćäźŻčŻ­pptx.eml', list count=0FTP: No empty storpath entry found for update with 'äź čžćäťśćľčŻ1-éżćäźŻčŻ­pptx.eml'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for äź čžćäťśćľčŻ1-éżćäźŻčŻ­pptx.eml (82796 bytes).
FTP: Reassemble - extracted filename from 150 response: 'äź čžćäťśćľčŻ1-éżćäźŻčŻ­pptx.eml'
FTP: Force updating latest storpath with 'äź čžćäťśćľčŻ1-éżćäźŻčŻ­pptx.eml', list count=0
FTP: No port entry found for force update with 'äź čžćäťśćľčŻ1-éżćäźŻčŻ­pptx.eml'
FTP: Data flow processing, looking for filename with port 4202
FTP: No filename found for port 4202 in any session
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for ć źĺźč˝Źć˘.exe (2052608 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'ć źĺźč˝Źć˘.exe'
FTP: Searching for empty storpath to update with 'ć źĺźč˝Źć˘.exe', list count=0FTP: No empty storpath entry found for update with 'ć źĺźč˝Źć˘.exe'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for ć źĺźč˝Źć˘.exe (2052608 bytes).
FTP: Reassemble - extracted filename from 150 response: 'ć źĺźč˝Źć˘.exe'
FTP: Force updating latest storpath with 'ć źĺźč˝Źć˘.exe', list count=0
FTP: No port entry found for force update with 'ć źĺźč˝Źć˘.exe'
FTP: Data flow processing, looking for filename with port 4203
FTP: No filename found for port 4203 in any session
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for ćłčŻ­.docx (12926 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'ćłčŻ­.docx'
FTP: Searching for empty storpath to update with 'ćłčŻ­.docx', list count=0FTP: No empty storpath entry found for update with 'ćłčŻ­.docx'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for ćłčŻ­.docx (12926 bytes).
FTP: Reassemble - extracted filename from 150 response: 'ćłčŻ­.docx'
FTP: Force updating latest storpath with 'ćłčŻ­.docx', list count=0
FTP: No port entry found for force update with 'ćłčŻ­.docx'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for ćľčŻćäťś.lzh (1804398 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'ćľčŻćäťś.lzh'
FTP: Searching for empty storpath to update with 'ćľčŻćäťś.lzh', list count=0FTP: No empty storpath entry found for update with 'ćľčŻćäťś.lzh'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for ćľčŻćäťś.lzh (1804398 bytes).
FTP: Reassemble - extracted filename from 150 response: 'ćľčŻćäťś.lzh'
FTP: Force updating latest storpath with 'ćľčŻćäťś.lzh', list count=0
FTP: No port entry found for force update with 'ćľčŻćäťś.lzh'
FTP: Data flow processing, looking for filename with port 4205
FTP: No filename found for port 4205 in any session
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for çŽä˝ä¸­ć.ppt (79872 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'çŽä˝ä¸­ć.ppt'
FTP: Searching for empty storpath to update with 'çŽä˝ä¸­ć.ppt', list count=0FTP: No empty storpath entry found for update with 'çŽä˝ä¸­ć.ppt'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for çŽä˝ä¸­ć.ppt (79872 bytes).
FTP: Reassemble - extracted filename from 150 response: 'çŽä˝ä¸­ć.ppt'
FTP: Force updating latest storpath with 'çŽä˝ä¸­ć.ppt', list count=0
FTP: No port entry found for force update with 'çŽä˝ä¸­ć.ppt'
FTP: Data flow processing, looking for filename with port 4206
FTP: No filename found for port 4206 in any session
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for čąčŻ­.rtf (16384 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'čąčŻ­.rtf'
FTP: Searching for empty storpath to update with 'čąčŻ­.rtf', list count=0FTP: No empty storpath entry found for update with 'čąčŻ­.rtf'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for čąčŻ­.rtf (16384 bytes).
FTP: Reassemble - extracted filename from 150 response: 'čąčŻ­.rtf'
FTP: Force updating latest storpath with 'čąčŻ­.rtf', list count=0
FTP: No port entry found for force update with 'čąčŻ­.rtf'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 200 PORT command successful. Consider using PASV.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 200 PORT command successful. Consider using PASV.
FTP: dissect_ftp_control 150 Opening BINARY mode data connection for éŚéĄľ.html (43932 bytes).
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Single packet - extracted filename from 150 response: 'éŚéĄľ.html'
FTP: Searching for empty storpath to update with 'éŚéĄľ.html', list count=0FTP: No empty storpath entry found for update with 'éŚéĄľ.html'
FTP: dissect_ftp_control_rsm 150 Opening BINARY mode data connection for éŚéĄľ.html (43932 bytes).
FTP: Reassemble - extracted filename from 150 response: 'éŚéĄľ.html'
FTP: Force updating latest storpath with 'éŚéĄľ.html', list count=0
FTP: No port entry found for force update with 'éŚéĄľ.html'
FTP: dissect_ftp_control 226 Transfer complete.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 226 Transfer complete.
FTP: dissect_ftp_control 221 Goodbye.
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control_rsm 221 Goodbye.
FTP: dissect_ftp_control 
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: dissect_ftp_control 
FTP: Direction DST2SRC, port_dst=21, control_port_dst=4164
FTP: Data flow processing, looking for filename with port 4165
FTP: No filename found for port 4165 in any session
FTP: Data flow processing, looking for filename with port 4175
FTP: No filename found for port 4175 in any session
FTP: Data flow processing, looking for filename with port 4185
FTP: No filename found for port 4185 in any session
FTP: Data flow processing, looking for filename with port 4169
FTP: No filename found for port 4169 in any session
FTP: Data flow processing, looking for filename with port 4182
FTP: No filename found for port 4182 in any session
FTP: Data flow processing, looking for filename with port 4189
FTP: No filename found for port 4189 in any session
FTP: Data flow processing, looking for filename with port 4192
FTP: No filename found for port 4192 in any session
FTP: Data flow processing, looking for filename with port 4171
FTP: No filename found for port 4171 in any session
FTP: Control flow finishing, file_num=0, filename_list count=0FTP: Updating all data convs, filename_list count=0FTP: Finished updating all data convs
FTP: No file transfer, writing control log directly
[2025-08-22 17:20:21] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_pschema.c:dpi_fvalue_new_string_put:227[0m field_name: Return_content input string len :6033
FTP: Data flow processing, looking for filename with port 4181
FTP: No filename found for port 4181 in any session
FTP: Data flow processing, looking for filename with port 4207
FTP: No filename found for port 4207 in any session
FTP: Data flow processing, looking for filename with port 4167
FTP: No filename found for port 4167 in any session
FTP: Data flow processing, looking for filename with port 4195
FTP: No filename found for port 4195 in any session
FTP: Data flow processing, looking for filename with port 4180
FTP: No filename found for port 4180 in any session
FTP: Data flow processing, looking for filename with port 4186
FTP: No filename found for port 4186 in any session
FTP: Data flow processing, looking for filename with port 4208
FTP: No filename found for port 4208 in any session
FTP: Data flow processing, looking for filename with port 4200
FTP: No filename found for port 4200 in any session
FTP: Data flow processing, looking for filename with port 4197
FTP: No filename found for port 4197 in any session
FTP: Data flow processing, looking for filename with port 4174
FTP: No filename found for port 4174 in any session
FTP: Data flow processing, looking for filename with port 4204
FTP: No filename found for port 4204 in any session
FTP: Data flow processing, looking for filename with port 4188
FTP: No filename found for port 4188 in any session
FTP: Data flow processing, looking for filename with port 4187
FTP: No filename found for port 4187 in any session
FTP: Data flow processing, looking for filename with port 4178
FTP: No filename found for port 4178 in any session
FTP: Data flow processing, looking for filename with port 4183
FTP: No filename found for port 4183 in any session
FTP: Data flow processing, looking for filename with port 4194
FTP: No filename found for port 4194 in any session
FTP: Data flow processing, looking for filename with port 4190
FTP: No filename found for port 4190 in any session
FTP: Data flow processing, looking for filename with port 4191
FTP: No filename found for port 4191 in any session
FTP: Data flow processing, looking for filename with port 4184
FTP: No filename found for port 4184 in any session
FTP: Data flow processing, looking for filename with port 4196
FTP: No filename found for port 4196 in any session
FTP: Data flow processing, looking for filename with port 4179
FTP: No filename found for port 4179 in any session
DPDK     : total forward  0pkts,	total missed  0pkts,	total errors  0pkts, fail percent  0.000000%
DPI      : total receive  18973pkts,	fail enqueue  0pkts,	fail percent  0.000000%
TBL      : fail  enqueue  0pkts,0bytes,0KB,0MB,0GB
RSM      : fail  get      0
Flow     : fail  get      0
256K LOG : fail  get      0

[2025-08-22 17:20:21] [36mDEBUG[0m [90m/home/<USER>/SDX/sdx_dpi_sdt/src/dpi_main.c:work_dpdk_mode:3651[0m Bye...
2025-08-22 09:21:24  I sdt_HttpServer.cpp:122:~H ç­ĺž main_loop çşżç¨çťć...
2025-08-22 09:21:25  I sdt_HttpServer.cpp:381:~R ĺć­˘çćľč§ĺäťŁçćäťś: SDT_Rule_Update.txt
2025-08-22 09:21:25    mongoose.c:2716:close_con 1 closed
2025-08-22 09:21:25  I mongoose.c:2091:mg_mgr_fr All connections closed
éćž ipv4äťćĺčŽŽĺˇ
éćž ipv6äťćĺčŽŽĺˇ
